# 基础导入 - 快速启动
import re
import json
import os
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import logging
import shutil
import uuid
import hashlib
import datetime
import threading
import pathlib
import sys

# 延迟导入的重型库 - 只在需要时导入
# import pandas as pd  # 延迟导入
# import pdfplumber  # 延迟导入
# import PyPDF2  # 延迟导入
# import pyperclip  # 延迟导入
# from io import StringIO  # 延迟导入
# from pdfminer.* # 延迟导入
# import requests  # 延迟导入
# from collections import Counter  # 延迟导入

def lazy_import_pandas():
    """延迟导入pandas"""
    global pd
    try:
        import pandas as pd
        return pd
    except ImportError:
        messagebox.showerror("错误", "缺少pandas库，请安装：pip install pandas")
        return None

def lazy_import_pdfplumber():
    """延迟导入pdfplumber"""
    try:
        import pdfplumber
        return pdfplumber
    except ImportError:
        messagebox.showerror("错误", "缺少pdfplumber库，请安装：pip install pdfplumber")
        return None

def lazy_import_pypdf2():
    """延迟导入PyPDF2"""
    try:
        import PyPDF2
        return PyPDF2
    except ImportError:
        messagebox.showerror("错误", "缺少PyPDF2库，请安装：pip install PyPDF2")
        return None

def lazy_import_pyperclip():
    """延迟导入pyperclip"""
    try:
        import pyperclip
        return pyperclip
    except ImportError:
        messagebox.showerror("错误", "缺少pyperclip库，请安装：pip install pyperclip")
        return None

def lazy_import_requests():
    """延迟导入requests"""
    try:
        import requests
        return requests
    except ImportError:
        messagebox.showerror("错误", "缺少requests库，请安装：pip install requests")
        return None

def lazy_import_pdfminer():
    """延迟导入pdfminer"""
    try:
        from io import StringIO
        from pdfminer.converter import TextConverter
        from pdfminer.layout import LAParams
        from pdfminer.pdfdocument import PDFDocument
        from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
        from pdfminer.pdfpage import PDFPage
        from pdfminer.pdfparser import PDFParser
        return {
            'StringIO': StringIO,
            'TextConverter': TextConverter,
            'LAParams': LAParams,
            'PDFDocument': PDFDocument,
            'PDFResourceManager': PDFResourceManager,
            'PDFPageInterpreter': PDFPageInterpreter,
            'PDFPage': PDFPage,
            'PDFParser': PDFParser
        }
    except ImportError:
        messagebox.showerror("错误", "缺少pdfminer库，请安装：pip install pdfminer.six")
        return None

def lazy_import_collections():
    """延迟导入collections"""
    try:
        from collections import Counter
        return Counter
    except ImportError:
        return None

def determine_image_group_by_attribute_set(attribute_set):
    """
    根据属性集确定图片应该分到哪个组

    Args:
        attribute_set: 属性集字符串

    Returns:
        str: 对应的图片分组名称，如 "30x40cm", "40x40cm", "40x60cm"
    """
    if not attribute_set:
        return None

    attribute_set_lower = attribute_set.lower()

    logging.info(f"🔍 [图片分组] 分析属性集: '{attribute_set}' -> '{attribute_set_lower}'")

    # 首先尝试精确匹配英寸格式并转换为厘米
    # 匹配 11.8x15.7inch 格式
    inch_pattern = r'(\d+\.\d+)x(\d+\.\d+)inch'
    inch_match = re.search(inch_pattern, attribute_set_lower)
    if inch_match:
        width_inch = inch_match.group(1)
        height_inch = inch_match.group(2)
        cm_size = convert_inch_to_cm(width_inch, height_inch)
        logging.info(f"🔍 [图片分组] 英寸转换: {width_inch}x{height_inch}inch -> {cm_size}")

        # 从转换结果中提取尺寸组
        if "30x40cm" in cm_size:
            return "30x40cm"
        elif "40x60cm" in cm_size:
            return "40x60cm"
        elif "40x40cm" in cm_size:
            return "40x40cm"

    # 匹配整数英寸格式 12x16inch
    inch_int_pattern = r'(\d+)x(\d+)inch'
    inch_int_match = re.search(inch_int_pattern, attribute_set_lower)
    if inch_int_match:
        width_inch = inch_int_match.group(1)
        height_inch = inch_int_match.group(2)
        cm_size = convert_inch_to_cm(width_inch, height_inch)
        logging.info(f"🔍 [图片分组] 整数英寸转换: {width_inch}x{height_inch}inch -> {cm_size}")

        # 从转换结果中提取尺寸组
        if "30x40cm" in cm_size:
            return "30x40cm"
        elif "40x60cm" in cm_size:
            return "40x60cm"
        elif "40x40cm" in cm_size:
            return "40x40cm"

    # 直接匹配厘米格式
    cm_pattern = r'(\d+)x(\d+)cm'
    cm_match = re.search(cm_pattern, attribute_set_lower)
    if cm_match:
        width_cm = cm_match.group(1)
        height_cm = cm_match.group(2)
        cm_size = f"{width_cm}x{height_cm}cm"
        logging.info(f"🔍 [图片分组] 直接厘米匹配: {cm_size}")
        return cm_size

    # 检查属性集中的尺寸信息（原有逻辑，作为备用）
    if "40x60" in attribute_set_lower or ("40" in attribute_set_lower and "60" in attribute_set_lower and "40x60" in attribute_set_lower):
        logging.info(f"🔍 [图片分组] 备用逻辑匹配: 40x60cm")
        return "40x60cm"
    elif "40x40" in attribute_set_lower or "40*40" in attribute_set_lower:
        logging.info(f"🔍 [图片分组] 备用逻辑匹配: 40x40cm")
        return "40x40cm"
    elif "30x40" in attribute_set_lower or ("30" in attribute_set_lower and "40" in attribute_set_lower and "30x40" in attribute_set_lower):
        logging.info(f"🔍 [图片分组] 备用逻辑匹配: 30x40cm")
        return "30x40cm"
    elif "20" in attribute_set_lower and "20" in attribute_set_lower:
        logging.info(f"🔍 [图片分组] 备用逻辑匹配: 20x20 -> 30x40cm")
        return "30x40cm"  # 20x20 通常归类为30x40cm
    elif "24" in attribute_set_lower and "24" in attribute_set_lower:
        logging.info(f"🔍 [图片分组] 备用逻辑匹配: 24x24 -> 30x40cm")
        return "30x40cm"  # 24x24 通常归类为30x40cm
    elif "16" in attribute_set_lower and "16" in attribute_set_lower:
        logging.info(f"🔍 [图片分组] 备用逻辑匹配: 16x16 -> 30x40cm")
        return "30x40cm"  # 16x16 通常归类为30x40cm

    # 如果无法确定，返回None使用备用逻辑
    logging.warning(f"🔍 [图片分组] 无法确定分组: '{attribute_set}'")
    return None

def merge_pdf_files(pdf_files, output_path):
    """
    合并多个PDF文件为一个PDF文件

    Args:
        pdf_files: PDF文件路径列表
        output_path: 输出文件路径

    Returns:
        bool: 合并是否成功
    """
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        return False

    if not pdf_files:
        logging.warning("没有PDF文件需要合并")
        return False

    if len(pdf_files) == 1:
        # 只有一个文件，直接复制
        try:
            shutil.copy2(pdf_files[0], output_path)
            logging.info(f"📄 单文件复制: {pdf_files[0]} -> {output_path}")
            return True
        except Exception as e:
            logging.error(f"❌ 单文件复制失败: {e}")
            return False

    try:
        # 多个文件需要合并
        merger = PyPDF2.PdfWriter()

        # 按文件名排序，确保页面顺序正确
        sorted_files = sorted(pdf_files, key=lambda x: os.path.basename(x))
        logging.info(f"📄 开始合并 {len(sorted_files)} 个PDF文件:")

        for pdf_file in sorted_files:
            logging.info(f"  - 添加文件: {os.path.basename(pdf_file)}")
            with open(pdf_file, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    merger.add_page(page)

        # 写入合并后的文件
        with open(output_path, 'wb') as output_file:
            merger.write(output_file)

        logging.info(f"✅ PDF合并成功: {output_path}")
        return True

    except Exception as e:
        logging.error(f"❌ PDF合并失败: {e}")
        return False

# 设置常量 - 统一配置文件
UNIFIED_CONFIG_FILE = "app_config.json"

# 兼容性：保持原有文件名常量，但实际使用统一配置
LICENSE_FILE = "activationg.license"  # 兼容性保留
CONFIG_FILE = "confige.json"  # 兼容性保留

# 新增：文件夹命名配置
FOLDER_NAME_CONFIG = {
    "date": "4-15",
    "time_period": "上午",  # 上午/下午
    "operator": "吴进宇",
    "shop_number": "店铺号"
}

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')

# ========== 新增：问题诊断和修复功能 ==========

def collect_unmapped_attributes(df):
    """
    收集拣货单中未映射的属性集，用于更新映射表

    Args:
        df: 拣货单DataFrame

    Returns:
        list: 未映射的属性集列表
    """
    if df is None or df.empty:
        return []

    # 获取所有唯一的属性集
    unique_attributes = df['属性集'].dropna().unique()
    unmapped_attributes = []

    for attr_set in unique_attributes:
        # 使用与get_template_mapping相同的逻辑检查是否有映射
        mapping_result = get_template_mapping(attr_set)

        # 如果返回"unknown"，说明没有找到映射
        if mapping_result == "unknown":
            cleaned_attr = clean_attribute_set(attr_set)
            unmapped_attributes.append({
                'original': attr_set,
                'cleaned': cleaned_attr
            })
            logging.info(f"[未映射属性集] 发现: '{attr_set}' -> 清理后: '{cleaned_attr}'")

    return unmapped_attributes

def diagnose_attribute_mapping_issues(df, source_folder):
    """
    诊断属性集映射问题，帮助用户识别和解决以下问题：
    1. 拣货单中的属性集无法匹配到映射表
    2. 多PC和单PC图片混合问题
    3. 缺失的图片文件

    Args:
        df: 拣货单DataFrame
        source_folder: 源图片文件夹路径

    Returns:
        dict: 诊断结果
    """
    logging.info("🔍 开始诊断属性集映射问题...")

    diagnosis_results = {
        "unmapped_attributes": [],  # 无法映射的属性集
        "pc_mismatch_issues": [],   # PC数不匹配问题
        "missing_images": [],       # 缺失的图片
        "mixed_pc_warnings": [],    # 多PC/单PC混合警告
        "suggestions": []           # 修复建议
    }

    # 检查每个属性集的映射情况
    unique_attributes = df['属性集'].dropna().unique()

    for attr_set in unique_attributes:
        # 清理属性集
        cleaned_attr = clean_attribute_set(attr_set)

        # 检查是否能找到映射
        mapped_result = get_template_mapping(cleaned_attr)

        if mapped_result == cleaned_attr:  # 如果返回的是清理后的原属性集，说明没有找到映射
            diagnosis_results["unmapped_attributes"].append({
                "original": attr_set,
                "cleaned": cleaned_attr,
                "suggestion": f"需要在映射表中添加: '{cleaned_attr}' -> '目标文件夹名称'"
            })

        # 检查PC数匹配问题
        template_pc_count, _, _ = extract_pc_and_size_from_target(mapped_result)

        # 查找使用此属性集的SKC
        rows_with_attr = df[df['属性集'] == attr_set]
        for _, row in rows_with_attr.iterrows():
            product_info = row.get('商品信息', '')
            if isinstance(product_info, str) and product_info.strip():
                skc_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
                if skc_match:
                    skc_base = skc_match.group(1)

                    # 检查实际图片数量
                    actual_jpg_count, actual_pc_count = count_images_and_pc(source_folder, skc_base)

                    if template_pc_count != actual_pc_count and actual_pc_count > 0:
                        diagnosis_results["pc_mismatch_issues"].append({
                            "skc": skc_base,
                            "attribute_set": attr_set,
                            "template_pc": template_pc_count,
                            "actual_pc": actual_pc_count,
                            "actual_images": actual_jpg_count,
                            "warning": f"SKC{skc_base}: 模板要求{template_pc_count}pc，实际检测到{actual_pc_count}pc"
                        })

    # 生成修复建议
    if diagnosis_results["unmapped_attributes"]:
        diagnosis_results["suggestions"].append(
            "建议1: 导入或更新映射表，添加缺失的属性集映射关系"
        )

    if diagnosis_results["pc_mismatch_issues"]:
        diagnosis_results["suggestions"].append(
            "建议2: 检查图片文件命名，确保PC数与属性集要求一致"
        )
        diagnosis_results["suggestions"].append(
            "建议3: 更新映射表中的PC数，使其与实际图片数量匹配"
        )

    # 输出诊断结果
    logging.info("📊 诊断结果汇总:")
    logging.info(f"  - 无法映射的属性集: {len(diagnosis_results['unmapped_attributes'])} 个")
    logging.info(f"  - PC数不匹配问题: {len(diagnosis_results['pc_mismatch_issues'])} 个")

    if diagnosis_results["unmapped_attributes"]:
        logging.info("❌ 无法映射的属性集:")
        for item in diagnosis_results["unmapped_attributes"][:5]:  # 只显示前5个
            logging.info(f"    '{item['original']}'")
        if len(diagnosis_results["unmapped_attributes"]) > 5:
            logging.info(f"    ... 还有 {len(diagnosis_results['unmapped_attributes']) - 5} 个")

    if diagnosis_results["pc_mismatch_issues"]:
        logging.info("⚠️ PC数不匹配问题:")
        for item in diagnosis_results["pc_mismatch_issues"][:5]:  # 只显示前5个
            logging.info(f"    {item['warning']}")
        if len(diagnosis_results["pc_mismatch_issues"]) > 5:
            logging.info(f"    ... 还有 {len(diagnosis_results['pc_mismatch_issues']) - 5} 个")

    if diagnosis_results["suggestions"]:
        logging.info("💡 修复建议:")
        for suggestion in diagnosis_results["suggestions"]:
            logging.info(f"    {suggestion}")

    return diagnosis_results

# 随机生成SALT值
def generate_salt():
    return os.urandom(16).hex()  # 生成一个16字节的随机字符串

def get_machine_code():
    mac = uuid.getnode()
    return hashlib.md5(str(mac).encode()).hexdigest()

def generate_activation_code(machine_code):
    expiry_date = "99991231"
    salt = generate_salt()  # 随机生成SALT值
    activation_code = hashlib.sha256((machine_code + expiry_date + salt).encode()).hexdigest().upper()
    return f"{activation_code}:{expiry_date}:{salt}"  # 将SALT值也存储在激活码中

def verify_activation_code(code):
    try:
        code_parts = code.split(":")
        if len(code_parts) != 3:
            return False

        activation_code, expiry_date, salt = code_parts

        current_date = datetime.datetime.now().strftime("%Y%m%d")
        if current_date > expiry_date:
            return False

        machine_code = get_machine_code()
        expected_code = hashlib.sha256((machine_code + expiry_date + salt).encode()).hexdigest().upper()
        return activation_code == expected_code
    except:
        return False

# ========== 统一配置文件管理系统 ==========

def load_unified_config():
    """加载统一配置文件"""
    config = {
        "activation": {
            "license_code": ""
        },
        "paths": {
            "main_folder": "",
            "img_folder": ""
        },
        "folder_config": {
            "date": "",
            "time_period": "",
            "operator": "",
            "shop_number": "",
            "operator_history": [],
            "shop_history": []
        },
        "template_mapping": {}
    }

    try:
        if os.path.exists(UNIFIED_CONFIG_FILE):
            with open(UNIFIED_CONFIG_FILE, 'r', encoding='utf-8') as f:
                saved_config = json.load(f)
                config.update(saved_config)
        else:
            # 首次运行，尝试从旧文件迁移
            migrate_old_config_files(config)
    except Exception as e:
        logging.warning(f"加载统一配置失败: {e}")
        # 尝试从旧文件加载
        migrate_old_config_files(config)

    return config

def save_unified_config(config):
    """保存统一配置文件"""
    try:
        with open(UNIFIED_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.error(f"保存统一配置失败: {e}")

def migrate_old_config_files(config):
    """迁移旧的配置文件到统一配置"""
    try:
        # 迁移激活码
        if os.path.exists(LICENSE_FILE):
            with open(LICENSE_FILE, 'r') as f:
                license_code = f.read().strip()
                config["activation"]["license_code"] = license_code
            logging.info("已迁移激活码配置")

        # 迁移路径和文件夹配置
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                old_config = json.load(f)
                config["paths"]["main_folder"] = old_config.get("main_folder", "")
                config["paths"]["img_folder"] = old_config.get("img_folder", "")
                config["folder_config"] = old_config.get("folder_config", config["folder_config"])
            logging.info("已迁移路径和文件夹配置")

        # 迁移模板映射
        template_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'template_mapping.json')
        if os.path.exists(template_file):
            with open(template_file, 'r', encoding='utf-8') as f:
                template_mapping = json.load(f)
                config["template_mapping"] = template_mapping
            logging.info("已迁移模板映射配置")

        # 保存迁移后的配置
        save_unified_config(config)
        logging.info("配置迁移完成，已保存到统一配置文件")

    except Exception as e:
        logging.warning(f"配置迁移失败: {e}")

def is_activated():
    """检查激活状态 - 使用统一配置"""
    config = load_unified_config()
    license_code = config.get("activation", {}).get("license_code", "")
    if license_code:
        return verify_activation_code(license_code)

    # 兼容性：如果统一配置中没有，尝试从旧文件读取
    if os.path.exists(LICENSE_FILE):
        with open(LICENSE_FILE, 'r') as f:
            saved = f.read().strip()
        return verify_activation_code(saved)
    return False

def save_activation_code(code):
    """保存激活码 - 只使用统一配置"""
    config = load_unified_config()
    config["activation"]["license_code"] = code
    save_unified_config(config)

    # 不再保存到旧文件，只使用统一配置

def prompt_for_activation():
    root = tk.Tk()
    root.title("软件激活")
    root.geometry("400x300")

    machine_code = get_machine_code()

    ttk.Label(root, text="请输入激活码:").pack(pady=10)
    code_entry = ttk.Entry(root, width=50)
    code_entry.pack(pady=5)

    ttk.Label(root, text="机器码（发送给作者）:").pack(pady=5)
    machine_code_label = ttk.Label(root, text=machine_code)
    machine_code_label.pack(pady=5)

    def copy_machine_code():
        pyperclip = lazy_import_pyperclip()
        if pyperclip:
            pyperclip.copy(machine_code)
            messagebox.showinfo("提示", "机器码已复制到剪贴板")
        else:
            messagebox.showerror("错误", "无法复制到剪贴板")

    ttk.Button(root, text="复制机器码", command=copy_machine_code).pack(pady=5)

    def activate():
        activation_code = code_entry.get().strip()
        if verify_activation_code(activation_code):
            save_activation_code(activation_code)
            messagebox.showinfo("成功", "激活成功！")
            root.destroy()
        else:
            messagebox.showerror("错误", "激活码无效或已过期")

    ttk.Button(root, text="激活", command=activate).pack(pady=10)
    root.mainloop()

# 文件选择函数
def select_folder(entry):
    folder_path = filedialog.askdirectory()
    if folder_path:  # 确保用户选择了文件夹
        entry.delete(0, tk.END)  # 清空当前输入
        entry.insert(0, folder_path)  # 插入选择的文件夹路径

def select_pdf(entry):
    pdf_path = filedialog.askopenfilename(filetypes=[("PDF Files", "*.pdf")])
    if pdf_path:  # 确保用户选择了文件
        entry.delete(0, tk.END)  # 清空当前输入
        entry.insert(0, pdf_path)  # 插入选择的 PDF 文件路径

# 功能一：处理文件和PDF数据
def check_missing_files(df, source_folder, small_pdf_folder):
    """检查缺失的文件并返回缺失信息和统计数据"""
    missing_files = []
    matched_images = []
    matched_small_pdfs = []
    duplicate_skcs = []

    # 统计所有SKC的图片数量和具体文件名
    skc_image_count = {}
    skc_image_files = {}  # 记录每个SKC对应的具体文件名

    # 新增：统计所有SKU货号的图片数量和具体文件名
    sku_image_count = {}
    sku_image_files = {}  # 记录每个SKU货号对应的具体文件名

    # 第一步：检查所有SKC的图片
    for index, row in df.iterrows():
        product_info = row['商品信息']

        # 检查 product_info 是否为非空字符串
        if not isinstance(product_info, str) or not product_info.strip():
            logging.warning(f"第{index}行：'商品信息' 无效或为空，跳过此行SKC检查。")
            continue # 如果商品信息无效或为空，跳过此行

        # 修改正则表达式，使其能匹配 SKC 后面的 9 到 11 位数字
        skc_number_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if not skc_number_match:
              continue

        skc_base = skc_number_match.group(1)
        logging.info(f"第{index}行：从商品信息 '{product_info}' 中提取到 SKC: {skc_base} (位数: {len(skc_base)})") # 添加日志

        # 检查图片文件是否存在，使用 move_files_with_suffix 查找
        image_paths = move_files_with_suffix(source_folder, skc_base, df)

        # 分离SKC命名图片和SKU货号图片
        skc_image_paths = []
        sku_image_paths = []

        for image_path in image_paths:
            filename = os.path.basename(image_path)
            # 如果文件名以SKC编号开头，归类为SKC图片
            if filename.lower().startswith(skc_base.lower()):
                skc_image_paths.append(image_path)
            else:
                # 否则归类为SKU货号图片
                sku_image_paths.append(image_path)

        # 处理找到的图片
        if image_paths:  # 如果找到任何图片（SKC或SKU货号）
            # 只将SKC命名图片添加到matched_images（避免重复统计）
            if skc_image_paths:
                matched_images.extend(skc_image_paths)
                # 统计每个SKC的图片数量（只统计SKC命名图片）
                if skc_base in skc_image_count:
                    skc_image_count[skc_base] += len(skc_image_paths)
                else:
                    skc_image_count[skc_base] = len(skc_image_paths)

                # 记录SKC命名图片到skc_image_files中（用于重复检测）
                if skc_base not in skc_image_files:
                    skc_image_files[skc_base] = []
                for image_path in skc_image_paths:
                    filename = os.path.basename(image_path)
                    skc_image_files[skc_base].append(filename)
            # 如果只有SKU货号图片，不报告缺失（因为有对应的图片）
        else:
            # 完全没找到图片时才报告缺失
            missing_files.append(f"SKC{skc_base} 的图片文件")

    # 第二步：检查所有SKU货号的图片（新增逻辑）
    logging.info("🔍 开始检查所有SKU货号的图片...")
    all_sku_haos = df['SKU货号'].dropna().unique().tolist()

    for sku_hao in all_sku_haos:
        # 彻底清理SKU货号：去除换行符、制表符和多余空格
        sku_hao_original = str(sku_hao)
        sku_hao_str = sku_hao_original.strip().replace('\n', '').replace('\r', '').replace('\t', ' ')
        # 去除多余的空格
        sku_hao_str = ' '.join(sku_hao_str.split())

        # 如果清理前后不同，记录调试信息
        if sku_hao_original != sku_hao_str:
            logging.info(f"🧹 清理SKU货号: '{repr(sku_hao_original)}' -> '{sku_hao_str}'")

        if not sku_hao_str:
            continue

        # 🔧 重要修复：使用统一的SKU货号验证函数
        if not is_valid_sku_hao(sku_hao_str):
            logging.info(f"🔍 跳过无效SKU货号: {sku_hao_str} (不符合SKU货号格式)")
            continue

        logging.info(f"🔍 检查有效SKU货号: {sku_hao_str} (包含英文字母)")

        # 查找SKU货号命名的图片
        sku_images = find_images_by_sku_hao(source_folder, sku_hao_str)
        if sku_images:
            logging.info(f"✅ 找到 {len(sku_images)} 张SKU货号 {sku_hao_str} 命名的图片")
            matched_images.extend(sku_images)

            # 统计每个SKU货号的图片数量
            if sku_hao_str in sku_image_count:
                sku_image_count[sku_hao_str] += len(sku_images)
            else:
                sku_image_count[sku_hao_str] = len(sku_images)

            # 记录每个SKU货号对应的具体文件名
            if sku_hao_str not in sku_image_files:
                sku_image_files[sku_hao_str] = []
            for image_path in sku_images:
                filename = os.path.basename(image_path)
                sku_image_files[sku_hao_str].append(filename)
        else:
            logging.info(f"ℹ️ 未找到SKU货号 {sku_hao_str} 命名的图片")
            missing_files.append(f"SKU货号{sku_hao_str} 的图片文件")

    # 输出统计信息（去重计算）
    unique_matched_images = list(set(matched_images))  # 去除重复的图片路径
    logging.info(f"📊 图片识别统计:")
    logging.info(f"📊 SKC图片统计: {len(skc_image_files)} 个SKC，共 {sum(skc_image_count.values())} 张图片")
    logging.info(f"📊 SKU货号图片统计: {len(sku_image_files)} 个SKU货号，共 {sum(sku_image_count.values())} 张图片")
    logging.info(f"📊 总计匹配图片: {len(unique_matched_images)} 张")

    # 第三步：检查小标PDF文件（保持原有逻辑，但只针对SKC）
    skc_pdf_status = {}  # 跟踪每个SKC的PDF查找状态

    for index, row in df.iterrows():
        product_info = row['商品信息']

        # 检查 product_info 是否为非空字符串
        if not isinstance(product_info, str) or not product_info.strip():
            continue

        # 修改正则表达式，使其能匹配 SKC 后面的 9 到 11 位数字
        skc_number_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if not skc_number_match:
              continue

        skc_base = skc_number_match.group(1)

        # 如果这个SKC已经处理过了，跳过
        if skc_base in skc_pdf_status:
            continue

        # 检查拆分后的小标PDF，支持新旧两种命名格式
        found_small_pdf = False
        if os.path.isdir(small_pdf_folder):
             for filename in os.listdir(small_pdf_folder):
                 if not filename.lower().endswith('.pdf'):
                     continue

                 # 检查新格式：SKC-页数.pdf
                 new_format_match = re.match(r'(\d{9,11})-(\d+)\.pdf$', filename, re.IGNORECASE)
                 if new_format_match:
                     file_skc = new_format_match.group(1)
                     page_number = new_format_match.group(2)
                     # 检查SKC匹配
                     if skc_base == file_skc or (len(skc_base) == 9 and file_skc.startswith(skc_base) and len(file_skc) == 11):
                         small_pdf_path = os.path.join(small_pdf_folder, filename)
                         matched_small_pdfs.append(small_pdf_path)
                         found_small_pdf = True
                         logging.info(f"第{index}行：找到匹配的小标PDF文件(新格式): {filename} (SKC: {skc_base}, 页数: {page_number})")
                         break

                 # 检查旧格式：SKC.pdf (向后兼容)
                 old_format_match = re.fullmatch(r'\d{9,11}\.pdf', filename.lower())
                 if old_format_match:
                      file_skc = filename.split('.')[0]
                      # 如果提取的SKC与文件名完全匹配，或者提取的SKC是文件名的9位前缀
                      if skc_base == file_skc or (len(skc_base) == 9 and file_skc.startswith(skc_base) and len(file_skc) == 11):
                           small_pdf_path = os.path.join(small_pdf_folder, filename)
                           matched_small_pdfs.append(small_pdf_path)
                           found_small_pdf = True
                           logging.info(f"第{index}行：找到匹配的小标PDF文件(旧格式): {filename} (匹配SKC: {skc_base})")
                           break

        # 如果没有找到SKC PDF，尝试查找SKU PDF
        if not found_small_pdf and df is not None:
            logging.info(f"🔍 未找到SKC {skc_base} 的小标PDF，尝试查找SKU PDF...")
            skc_to_sku_list, _, _, _ = build_skc_sku_mapping(df)

            # 调试信息：显示映射关系
            logging.info(f"🔍 调试：SKC {skc_base} 在映射表中的状态:")
            if skc_base in skc_to_sku_list:
                logging.info(f"🔍 调试：找到映射关系，对应SKU列表: {skc_to_sku_list[skc_base]}")
            else:
                logging.info(f"🔍 调试：未找到映射关系")
                logging.info(f"🔍 调试：映射表中的所有SKC: {list(skc_to_sku_list.keys())}")
                # 尝试模糊匹配
                for mapped_skc in skc_to_sku_list.keys():
                    if skc_base in mapped_skc or mapped_skc in skc_base:
                        logging.info(f"🔍 调试：发现可能的模糊匹配: {mapped_skc} -> {skc_to_sku_list[mapped_skc]}")

            # 调试信息：显示小标PDF文件夹内容
            if os.path.exists(small_pdf_folder):
                all_pdf_files = [f for f in os.listdir(small_pdf_folder) if f.lower().endswith('.pdf')]
                logging.info(f"🔍 调试：小标PDF文件夹中的所有PDF文件 ({len(all_pdf_files)} 个): {all_pdf_files[:10]}...")  # 只显示前10个

            if skc_base in skc_to_sku_list:
                sku_list = skc_to_sku_list[skc_base]
                logging.info(f"🔍 找到对应SKU货号列表: {sku_list}")

                # 查找所有SKU货号命名的PDF文件
                all_sku_matched_files = []
                total_sku_matched_count = 0

                for sku_hao in sku_list:
                    # 清理SKU货号：去除换行符、制表符和多余空格
                    sku_hao_str = str(sku_hao).strip().replace('\n', '').replace('\r', '').replace('\t', ' ')
                    sku_hao_str = ' '.join(sku_hao_str.split())  # 去除多余空格

                    if not sku_hao_str:
                        continue

                    logging.info(f"🔍 查找SKU货号 {sku_hao_str} 命名的PDF文件...")

                    sku_matched_count = 0
                    for filename in os.listdir(small_pdf_folder):
                        if not filename.lower().endswith('.pdf'):
                            continue

                        # 多种匹配方式
                        filename_lower = filename.lower()
                        sku_hao_lower = sku_hao_str.lower()

                        matched = False
                        match_type = ""

                        # 方式1：去掉文件扩展名后完全匹配（最精确）
                        if filename_lower.replace('.pdf', '') == sku_hao_lower:
                            matched = True
                            match_type = "完全匹配"

                        # 方式2：文件名以SKU货号开头（带页数等）
                        elif filename_lower.startswith(sku_hao_lower):
                            matched = True
                            match_type = "开头匹配"

                        # 方式3：文件名包含SKU货号
                        elif sku_hao_lower in filename_lower:
                            matched = True
                            match_type = "包含匹配"

                        if matched:
                            small_pdf_path = os.path.join(small_pdf_folder, filename)
                            all_sku_matched_files.append(small_pdf_path)
                            matched_small_pdfs.append(small_pdf_path)
                            sku_matched_count += 1
                            total_sku_matched_count += 1
                            logging.info(f"第{index}行：找到匹配的SKU小标PDF文件({match_type}): {filename} (SKU货号: {sku_hao_str})")

                    if sku_matched_count > 0:
                        logging.info(f"✅ SKU货号 {sku_hao_str} 共找到 {sku_matched_count} 个匹配的PDF文件")

                # 只要找到任何一个SKU的PDF文件，就认为找到了
                if total_sku_matched_count > 0:
                    found_small_pdf = True
                    logging.info(f"✅ 总共找到 {total_sku_matched_count} 个SKU命名的小标PDF文件")

                if not found_small_pdf:
                    logging.warning(f"❌ 所有SKU货号都未找到对应的小标PDF文件")
            else:
                logging.warning(f"❌ 未找到SKC {skc_base} 对应的SKU货号")

        # 记录这个SKC的处理状态
        skc_pdf_status[skc_base] = found_small_pdf

        if not found_small_pdf:
            missing_files.append(f"{skc_base} 的拆分后小标PDF文件")

    # 调试信息：显示所有SKC的处理状态
    logging.info(f"🔍 调试：SKC PDF处理状态总结:")
    for skc, status in skc_pdf_status.items():
        status_text = "✅ 找到" if status else "❌ 缺失"
        logging.info(f"🔍 调试：  SKC {skc}: {status_text}")

    # 检查重复的SKC（考虑文件名序号，如-1、-2、-3等）
    logging.info(f"🔍 [调试] 开始检查SKC重复，共 {len(skc_image_files)} 个SKC")
    for skc, files in skc_image_files.items():
        logging.info(f"🔍 [调试] 检查SKC {skc}，文件数量: {len(files)}")
        logging.info(f"🔍 [调试] 文件列表: {files}")

        if len(files) > 1:
            # 提取文件名中的序号
            file_suffixes = set()
            for filename in files:
                # 提取SKC后面的部分作为后缀（包括-1、-2、-3等序号）
                if filename.lower().startswith(skc.lower()):
                    suffix = filename[len(skc):].lower()  # 获取SKC后面的部分
                    file_suffixes.add(suffix)
                    logging.info(f"🔍 [调试] 文件 {filename} 的后缀: '{suffix}'")

            logging.info(f"🔍 [调试] SKC {skc} 的所有后缀: {list(file_suffixes)}")

            # 如果所有文件的后缀都不同，则不算重复
            if len(file_suffixes) == len(files):
                logging.info(f"✅ SKC {skc} 有 {len(files)} 个文件，但序号不重复: {list(file_suffixes)}")
            else:
                # 如果有相同的后缀，才算重复
                duplicate_skcs.append(skc)
                logging.warning(f"❌ SKC {skc} 有重复文件: {files}")
                logging.warning(f"❌ 重复原因：后缀数量 {len(file_suffixes)} < 文件数量 {len(files)}")
        else:
            logging.info(f"✅ SKC {skc} 只有1个文件，不重复")

    # 检查重复的SKU货号（只有完全相同的文件名才算重复）
    logging.info(f"🔍 [调试] 开始检查SKU货号重复，共 {len(sku_image_files)} 个SKU货号")
    for sku_hao, files in sku_image_files.items():
        logging.info(f"🔍 [调试] 检查SKU货号 {sku_hao}，文件数量: {len(files)}")
        logging.info(f"🔍 [调试] 文件列表: {files}")

        if len(files) > 1:
            # 对于SKU货号，只有完全相同的文件名才算重复
            unique_files = set(files)
            logging.info(f"🔍 [调试] 去重后文件数量: {len(unique_files)}")

            if len(unique_files) < len(files):
                # 有完全相同的文件名，才算重复
                duplicate_skcs.append(f"SKU货号:{sku_hao}")
                logging.warning(f"❌ SKU货号 {sku_hao} 有重复文件: {files}")
                logging.warning(f"❌ 重复原因：去重后文件数量 {len(unique_files)} < 原文件数量 {len(files)}")
            else:
                # 所有文件名都不同，不算重复
                logging.info(f"✅ SKU货号 {sku_hao} 有 {len(files)} 个文件，但文件名不重复: {files}")
        else:
            logging.info(f"✅ SKU货号 {sku_hao} 只有1个文件，不重复")

    # 返回统计信息
    stats = {
        'matched_images_count': len(matched_images),
        'matched_small_pdfs_count': len(matched_small_pdfs),
        'has_duplicates': len(duplicate_skcs) > 0,
        'duplicate_skcs': duplicate_skcs
    }

    return missing_files, stats

def show_missing_files_dialog(missing_files):
    """显示可复制的缺失文件对话框"""
    dialog = tk.Toplevel()
    dialog.title("缺失文件列表")
    dialog.geometry("800x500")  # 增大窗口尺寸

    # 创建文本框和滚动条
    frame = ttk.Frame(dialog)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    scrollbar = ttk.Scrollbar(frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # 设置字体，确保中文显示正常
    text = tk.Text(frame, yscrollcommand=scrollbar.set, wrap=tk.WORD,
                   font=("Microsoft YaHei", 10), width=80, height=25)
    text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.config(command=text.yview)

    # 插入缺失文件列表，确保每个文件占一行
    text.insert(tk.END, "以下文件缺失：\n\n")
    for i, file in enumerate(missing_files, 1):
        # 添加序号并确保换行
        text.insert(tk.END, f"{i}. {file}\n")

    # 设置文本框只读
    text.config(state=tk.DISABLED)

    # 添加按钮框架
    button_frame = ttk.Frame(dialog)
    button_frame.pack(fill=tk.X, padx=10, pady=5)

    def copy_skc_numbers():
        """只复制SKC数字到剪贴板"""
        skc_numbers = []
        for file in missing_files:
            # 使用正则表达式提取SKC数字 (匹配 9 到 11 位纯数字)
            match = re.search(r'(\d{9,11})', file)
            if match:
                skc_numbers.append(match.group(1))

        if skc_numbers:
            dialog.clipboard_clear()
            dialog.clipboard_append('\n'.join(skc_numbers))
            messagebox.showinfo("提示", "已复制SKC数字到剪贴板")
        else:
            messagebox.showinfo("提示", "没有找到SKC数字")

    def continue_processing():
        """继续处理"""
        dialog.destroy()
        return True

    def cancel_processing():
        """取消处理"""
        dialog.destroy()
        return False

    # 添加按钮
    ttk.Button(button_frame, text="复制SKC数字", command=copy_skc_numbers).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="继续处理", command=continue_processing).pack(side=tk.RIGHT, padx=5)
    ttk.Button(button_frame, text="取消", command=cancel_processing).pack(side=tk.RIGHT, padx=5)

    # 设置模态对话框
    dialog.transient(dialog.master)
    dialog.grab_set()
    dialog.wait_window()

    return dialog.result if hasattr(dialog, 'result') else False

def copy_duplicate_skcs():
    """复制重复SKC编号到剪贴板的独立功能"""
    # 延迟导入pyperclip
    pyperclip = lazy_import_pyperclip()
    if not pyperclip:
        return

    pdf_file_path = pdf_entry.get() # 拣货单PDF
    source_folder = source_entry.get()

    if not (os.path.exists(pdf_file_path) and pdf_file_path.lower().endswith('.pdf')):
        messagebox.showerror("错误", "请先选择有效的拣货单PDF文件。")
        return
    if not (os.path.exists(source_folder) and os.path.isdir(source_folder)):
        messagebox.showerror("错误", "请先选择有效的源文件夹。")
        return

    # 提取数据
    df = extract_pdf_data(pdf_file_path)
    if df is None or df.empty:
        messagebox.showerror("错误", "未能从拣货单PDF中提取任何数据，请检查文件。")
        return

    # 创建临时小标PDF文件夹用于检查
    path_prefix = path_entry.get()
    if not path_prefix:
        messagebox.showerror("错误", "请先选择目标文件夹。")
        return

    small_pdf_folder = os.path.join(path_prefix, '小标PDF')

    # 检查重复SKC
    _, stats = check_missing_files(df, source_folder, small_pdf_folder)

    if stats['duplicate_skcs']:
        duplicate_skcs_str = ','.join(stats['duplicate_skcs'])
        pyperclip.copy(duplicate_skcs_str)
        messagebox.showinfo("提示", f"已复制重复SKC编号到剪贴板：\n{duplicate_skcs_str}")
    else:
        messagebox.showinfo("提示", "未发现重复的SKC编号。")

def run_script_1(progress_bar, progress_label):
    path_prefix = path_entry.get()
    pdf_file_path = pdf_entry.get() # 拣货单PDF
    source_folder = source_entry.get()
    small_pdf_path = small_entry.get() # 小标源PDF

    # 验证路径
    if not (os.path.exists(path_prefix) and os.path.isdir(path_prefix)):
        messagebox.showerror("错误", "目标文件夹路径无效。")
        return
    if not (os.path.exists(pdf_file_path) and pdf_file_path.lower().endswith('.pdf')):
        messagebox.showerror("错误", "拣货单PDF文件路径无效。")
        return
    if not (os.path.exists(source_folder) and os.path.isdir(source_folder)):
        messagebox.showerror("错误", "源文件夹路径无效。")
        return
    if not (os.path.exists(small_pdf_path) and small_pdf_path.lower().endswith('.pdf')):
         messagebox.showerror("错误", "小标源PDF文件路径无效。")
         return

    # 创建小标PDF文件夹
    small_pdf_folder = os.path.join(path_prefix, '小标PDF')
    os.makedirs(small_pdf_folder, exist_ok=True)

    # 拆分小标PDF
    split_pdf(small_pdf_path, small_pdf_folder)

    # 提取数据
    df = extract_pdf_data(pdf_file_path)
    if df.empty:
        messagebox.showerror("错误", "未能从拣货单PDF中提取任何数据，请检查文件。")
        return

    # 🔍 新增：自动诊断属性集映射问题
    logging.info("🔍 正在进行属性集映射诊断...")
    diagnosis_results = diagnose_attribute_mapping_issues(df, source_folder)

    # 如果发现严重问题，给用户提示
    if diagnosis_results["unmapped_attributes"]:
        unmapped_count = len(diagnosis_results["unmapped_attributes"])
        logging.warning(f"⚠️ 发现 {unmapped_count} 个无法映射的属性集，这些属性集将使用原始名称创建文件夹")

    if diagnosis_results["pc_mismatch_issues"]:
        mismatch_count = len(diagnosis_results["pc_mismatch_issues"])
        logging.warning(f"⚠️ 发现 {mismatch_count} 个PC数不匹配问题，可能导致多PC图片进入单PC文件夹")

    # 检查图片和小标PDF缺失文件，获取统计信息
    missing_files, stats = check_missing_files(df, source_folder, small_pdf_folder)

    # 输出检查结果日志
    logging.info("检查结果：")
    logging.info(f"- 匹配到的图片数量：{stats['matched_images_count']}")
    logging.info(f"- 匹配到的小标PDF数量：{stats['matched_small_pdfs_count']}")
    logging.info(f"- 是否有重复图片：{'有' if stats['has_duplicates'] else '无'}")
    if stats['duplicate_skcs']:
        duplicate_skcs_str = ','.join(stats['duplicate_skcs'])
        logging.info(f"- 重复图片SKC编号：{duplicate_skcs_str}（可复制粘贴）")
    else:
        logging.info("- 重复图片SKC编号：无")

    # 直接执行，不管是否有重复图片
    if missing_files:
        if show_missing_files_dialog(missing_files):
            process_files(df, path_prefix, source_folder, small_pdf_folder, progress_bar, progress_label)
            messagebox.showinfo("完成", "功能一处理完成。")
        else:
            messagebox.showinfo("提示", "已取消功能一处理。")
    else:
        process_files(df, path_prefix, source_folder, small_pdf_folder, progress_bar, progress_label)
        messagebox.showinfo("完成", "功能一处理完成。")



# 提取PDF中的表格数据
def extract_pdf_data(pdf_file_path):
    # 延迟导入
    pdfplumber = lazy_import_pdfplumber()
    pd = lazy_import_pandas()

    if not pdfplumber or not pd:
        return None

    data = []
    try:
        with pdfplumber.open(pdf_file_path) as pdf:
            logging.info(f"📄 开始提取PDF数据: {pdf_file_path}")
            logging.info(f"📄 PDF总页数: {len(pdf.pages)}")

            for page_num, page in enumerate(pdf.pages):
                logging.info(f"📄 处理第{page_num + 1}页...")
                table = page.extract_table()
                if table:
                    logging.info(f"📄 第{page_num + 1}页提取到表格，共{len(table)}行")

                    # 显示表头信息
                    if len(table) > 0:
                        logging.info(f"📄 表头: {table[0]}")

                    # 显示前几行数据用于调试
                    for i, row in enumerate(table[:5]):  # 显示前5行
                        logging.info(f"📄 第{page_num + 1}页第{i + 1}行: {row}")

                    # 跳过表头，添加数据行
                    for row_num, row in enumerate(table[1:], 1):  # 跳过表头
                        if row and any(cell for cell in row if cell and str(cell).strip()):  # 跳过空行
                            # 检查是否为"合计"行
                            is_total_row = any(isinstance(cell, str) and '合计' in cell for cell in row if cell)
                            if is_total_row:
                                logging.info(f"📄 跳过合计行: 第{page_num + 1}页第{row_num + 1}行: {row}")
                                continue

                            data.append(row)
                            logging.info(f"📄 添加数据行{len(data)}: {row}")
                        else:
                            logging.info(f"📄 跳过空行: 第{page_num + 1}页第{row_num + 1}行")
                else:
                    logging.warning(f"📄 第{page_num + 1}页未提取到表格")

        if not data:
            logging.error("❌ 未从PDF中提取到任何数据。")
            return pd.DataFrame()
        else:
            logging.info(f"✅ 成功提取到{len(data)}行数据。")

            # 创建DataFrame并显示基本信息
            df = pd.DataFrame(data, columns=["序号", "商品信息", "属性集", "SKU ID", "SKU货号", "数量", "拣货数"])
            logging.info(f"📊 DataFrame信息:")
            logging.info(f"📊 行数: {len(df)}")
            logging.info(f"📊 列数: {len(df.columns)}")
            logging.info(f"📊 列名: {list(df.columns)}")

            # 显示前几行数据
            for i in range(min(5, len(df))):
                logging.info(f"📊 第{i + 1}行数据: {dict(df.iloc[i])}")

            return df

    except Exception as e:
        logging.error(f"❌ 读取PDF文件时出错: {e}")
        messagebox.showerror("错误", f"读取PDF文件时出错: {e}")
        return pd.DataFrame()

# 拆分PDF文件
def split_pdf(input_pdf_path, output_folder, log_callback=None):
    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        return

    try:
        if not os.path.exists(input_pdf_path):
            if log_callback:
                log_callback(f"输入文件不存在: {input_pdf_path}")
            else:
                logging.error(f"输入文件不存在: {input_pdf_path}")
            return

        # 第一遍：提取每页的SKC信息
        page_info = []
        with open(input_pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for i, page in enumerate(reader.pages):
                text = page.extract_text()
                skc, size_info = extract_skc_and_size_from_text(text)
                if skc:
                    # 构建新的文件名：SKC+页数
                    page_number = i + 1  # 页数从1开始
                    filename = f"{skc}-{page_number}.pdf"

                    page_info.append((i, filename))
                    if log_callback:
                        log_callback(f"第{i+1}页: SKC={skc}, 页数={page_number}, 文件名={filename}")
                    else:
                        logging.info(f"第{i+1}页: SKC={skc}, 页数={page_number}, 文件名={filename}")

        # 第二遍：根据提取的信息拆分并保存PDF
        with open(input_pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for i, page in enumerate(reader.pages):
                writer = PyPDF2.PdfWriter()
                writer.add_page(page)

                # 查找对应的文件名
                for page_index, filename in page_info:
                    if page_index == i:
                        output_pdf_path = os.path.join(output_folder, filename)
                        with open(output_pdf_path, 'wb') as output_file:
                            writer.write(output_file)

                        if log_callback:
                            log_callback(f"已保存: {filename}")
                        else:
                            logging.info(f"已保存小标PDF: {filename}")
                        break

    except Exception as e:
        if log_callback:
            log_callback(f"发生错误: {e}")
        else:
            logging.error(f"发生错误: {e}")

# 提取数字
def extract_number_from_text(text):
    # 修改正则表达式以匹配 9 到 11 位的数字序列
    match = re.search(r'\b\d{9,11}\b', text)
    if match:
        return match.group(0)
    else:
        logging.error("未能从文本中提取数字。")
        return None

def extract_skc_and_size_from_text(text):
    """
    从小标PDF文本中提取SKC和尺码信息

    Args:
        text: PDF页面提取的文本

    Returns:
        tuple: (skc, size_info) 或 (None, None)
    """
    if not text:
        return None, None

    # 提取SKC (9-11位数字)
    skc_match = re.search(r'\b(\d{9,11})\b', text)
    skc = skc_match.group(1) if skc_match else None

    # 提取尺码信息 - 常见的尺码格式
    size_info = None

    # 尝试匹配各种尺码格式，优先匹配更复杂的格式
    size_patterns = [
        r'(A-\d+\.\d+in/\d+cm\*\d+\.\d+in/\d+cm)',  # A-15.8in/40cm*23.8in/60cm
        r'(\d+\.\d+in/\d+cm\*\d+\.\d+in/\d+cm)',    # 15.8in/40cm*23.8in/60cm
        r'(\d+x\d+cm)',                              # 30x40cm
        r'(\d+\.\d+x\d+\.\d+inch)',                  # 11.8x15.7inch
        r'(\d+x\d+inch)',                            # 12x16inch
        r'(\d+x\d+)',                                # 30x40 (无单位)
        r'([A-Z]-\d+x\d+)',                          # G-30x40, H-30x40
        r'(\d+\.\d+x\d+\.\d+)',                      # 11.8x15.7 (无单位)
    ]

    for pattern in size_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            size_info = match.group(1)
            break

    # 清理尺码信息
    if size_info:
        size_info = clean_size_info(size_info)

    logging.info(f"从PDF文本提取: SKC={skc}, 尺码={size_info}")
    return skc, size_info

def clean_size_info(size_info):
    """
    清理尺码信息并转换为标准格式

    Args:
        size_info: 原始尺码信息

    Returns:
        str: 清理并标准化后的尺码信息
    """
    if not size_info or not isinstance(size_info, str):
        return ""

    # 首先使用和clean_attribute_set相同的基础清理方法
    # 1. 去除所有类型的换行符和制表符
    cleaned = re.sub(r'[\n\r\t\f\v]+', '', size_info)

    # 2. 将多个连续空格替换为单个空格
    cleaned = re.sub(r'\s+', ' ', cleaned)

    # 3. 去除首尾空格
    cleaned = cleaned.strip()

    # 4. 特殊处理：解析复杂尺码格式并转换为标准格式
    standardized = parse_and_standardize_size(cleaned)

    logging.info(f"尺码清理: '{size_info}' -> '{cleaned}' -> '{standardized}'")
    return standardized

def parse_and_standardize_size(size_info):
    """
    解析复杂尺码格式并转换为标准格式

    Args:
        size_info: 清理后的尺码信息

    Returns:
        str: 标准化的尺码格式
    """
    if not size_info:
        return ""

    # 处理中文英寸格式：11.8*15.7 -> 30x40cm (转换为厘米)
    chinese_inch_pattern = r'(\d+(?:\.\d+)?)\*(\d+(?:\.\d+)?)'
    match = re.search(chinese_inch_pattern, size_info)
    if match:
        width_inch = match.group(1)
        height_inch = match.group(2)
        # 使用转换函数
        result = convert_inch_to_cm(width_inch, height_inch)
        logging.info(f"🏷️ [尺寸标准化] 中文英寸格式: '{size_info}' -> {width_inch}*{height_inch}inch -> {result}")
        return result

    # 处理复杂格式：A-15.8in/40cm*23.8in/60cm
    complex_pattern = r'A?-?(\d+(?:\.\d+)?)in/(\d+)cm\*(\d+(?:\.\d+)?)in/(\d+)cm'
    match = re.search(complex_pattern, size_info, re.IGNORECASE)
    if match:
        width_cm = int(match.group(2))
        height_cm = int(match.group(4))

        # 确保尺寸按照标准顺序排列（小的在前）
        sizes = sorted([width_cm, height_cm])
        standard_size = f"{sizes[0]}x{sizes[1]}cm"
        logging.info(f"复杂格式解析: '{size_info}' -> '{standard_size}'")
        return standard_size

    # 处理简单格式：30x40cm, 11.8x15.7inch等
    simple_patterns = [
        (r'(\d+)x(\d+)cm', lambda m: f"{m.group(1)}x{m.group(2)}cm"),
        (r'(\d+\.\d+)x(\d+\.\d+)inch', lambda m: convert_inch_to_cm(m.group(1), m.group(2))),
        (r'(\d+)x(\d+)inch', lambda m: convert_inch_to_cm(m.group(1), m.group(2))),
        (r'(\d+)x(\d+)', lambda m: f"{m.group(1)}x{m.group(2)}cm"),  # 默认添加cm
    ]

    for pattern, converter in simple_patterns:
        match = re.search(pattern, size_info, re.IGNORECASE)
        if match:
            result = converter(match)
            logging.info(f"简单格式解析: '{size_info}' -> '{result}'")
            return result

    # 如果无法解析，返回原始清理后的字符串
    return size_info

def convert_inch_to_cm(width_inch, height_inch):
    """
    将英寸尺寸转换为厘米尺寸的标准格式

    Args:
        width_inch: 宽度（英寸）
        height_inch: 高度（英寸）

    Returns:
        str: 标准厘米格式
    """
    try:
        # 常见的英寸到厘米的转换映射（精确映射）
        inch_to_cm_map = {
            "11.8": "30",
            "15.7": "40",
            "23.6": "60",
            "15.8": "40",  # 添加您例子中的尺寸
            "23.8": "60",  # 添加23.8英寸对应60厘米
            "12": "30",    # 12英寸对应30厘米
            "16": "40",    # 16英寸对应40厘米
            "24": "60",    # 24英寸对应60厘米
            "18": "45",    # 18英寸对应45厘米（正方形抱枕）
        }

        width_str = str(width_inch)
        height_str = str(height_inch)

        logging.info(f"🔄 [英寸转换] 输入: {width_str}x{height_str}inch")

        width_cm = inch_to_cm_map.get(width_str)
        height_cm = inch_to_cm_map.get(height_str)

        if width_cm and height_cm:
            # 确保尺寸按照标准顺序排列（小的在前）
            sizes = sorted([int(width_cm), int(height_cm)])
            result = f"{sizes[0]}x{sizes[1]}cm"
            logging.info(f"🔄 [英寸转换] 精确映射: {width_str}x{height_str}inch -> {result}")
            return result
        else:
            # 如果没有预设映射，使用数学转换（1英寸 = 2.54厘米）
            width_cm_calc = round(float(width_inch) * 2.54)
            height_cm_calc = round(float(height_inch) * 2.54)

            # 确保尺寸按照标准顺序排列（小的在前）
            sizes = sorted([width_cm_calc, height_cm_calc])
            result = f"{sizes[0]}x{sizes[1]}cm"
            logging.info(f"🔄 [英寸转换] 数学计算: {width_str}x{height_str}inch -> {result}")
            return result
    except Exception as e:
        logging.warning(f"🔄 [英寸转换] 转换失败: {width_inch}x{height_inch}inch, 错误: {e}")
        return f"{width_inch}x{height_inch}inch"

def extract_size_from_pdf_file(pdf_path):
    """
    从单个PDF文件中提取尺码信息（增强版）

    Args:
        pdf_path: PDF文件路径

    Returns:
        str: 提取的尺码信息，如果没有找到返回None
    """
    if not os.path.exists(pdf_path):
        logging.warning(f"🏷️ [尺寸提取] PDF文件不存在: {pdf_path}")
        return None

    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        logging.warning(f"🏷️ [尺寸提取] PyPDF2库不可用")
        return None

    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            if len(reader.pages) == 0:
                logging.warning(f"🏷️ [尺寸提取] PDF文件无页面: {pdf_path}")
                return None

            # 尝试从所有页面提取文本
            all_text = ""
            for page_num, page in enumerate(reader.pages):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        all_text += page_text + "\n"
                        logging.info(f"🏷️ [尺寸提取] 第{page_num+1}页文本: {page_text[:100]}...")
                except Exception as e:
                    logging.warning(f"🏷️ [尺寸提取] 第{page_num+1}页提取失败: {e}")

            if not all_text.strip():
                logging.warning(f"🏷️ [尺寸提取] 无法从PDF提取文本: {pdf_path}")
                # 尝试从文件名提取尺寸信息作为备用方案
                return extract_size_from_filename(os.path.basename(pdf_path))

            logging.info(f"🏷️ [尺寸提取] 提取的完整文本: {all_text[:200]}...")

            # 使用增强的提取函数
            _, size_info = extract_skc_and_size_from_text_enhanced(all_text)

            if not size_info:
                # 备用方案：从文件名提取
                logging.info(f"🏷️ [尺寸提取] 从PDF内容提取失败，尝试从文件名提取")
                size_info = extract_size_from_filename(os.path.basename(pdf_path))

            return size_info

    except Exception as e:
        logging.warning(f"🏷️ [尺寸提取] 从PDF文件提取尺码失败 {pdf_path}: {e}")
        # 备用方案：从文件名提取
        return extract_size_from_filename(os.path.basename(pdf_path))

def extract_size_from_filename(filename):
    """
    从文件名中提取尺寸信息作为备用方案

    Args:
        filename: 文件名

    Returns:
        str: 提取的尺寸信息，如果没有找到返回None
    """
    if not filename:
        return None

    # 尝试匹配文件名中的尺寸信息
    size_patterns = [
        r'(\d+x\d+cm)',           # 30x40cm
        r'(\d+\.\d+x\d+\.\d+inch)', # 11.8x15.7inch
        r'(\d+x\d+inch)',         # 12x16inch
        r'(\d+x\d+)',             # 30x40 (无单位)
    ]

    for pattern in size_patterns:
        match = re.search(pattern, filename, re.IGNORECASE)
        if match:
            size_info = match.group(1)
            logging.info(f"🏷️ [尺寸提取] 从文件名提取尺码: '{filename}' -> '{size_info}'")
            return size_info

    logging.info(f"🏷️ [尺寸提取] 无法从文件名提取尺码: {filename}")
    return None

def generate_dynamic_size_patterns():
    """
    动态生成尺寸匹配模式，基于当前映射表A列的内容

    Returns:
        list: 动态生成的正则表达式模式列表
    """
    patterns = []

    # 基础通用模式（始终包含）
    base_patterns = [
        # 复杂英寸/厘米格式
        r'(A-\d+\.\d+in/\d+cm\*\d+\.\d+in/\d+cm)',  # A-15.8in/40cm*23.8in/60cm
        r'(\d+\.\d+in/\d+cm\*\d+\.\d+in/\d+cm)',    # 15.8in/40cm*23.8in/60cm

        # 中文英寸格式
        r'【A\s*-\s*(\d+\.\d+\*\d+\.\d+)英寸[^】]*】',  # 【A -11.8*15.7英寸帆框】
        r'【(\d+\.\d+\*\d+\.\d+)英寸[^】]*】',         # 【11.8*15.7英寸帆框】
        r'(\d+\.\d+\*\d+\.\d+)英寸',                 # 11.8*15.7英寸
        r'【A\s*-\s*(\d+\*\d+)英寸[^】]*】',          # 【A -12*16英寸帆框】
        r'【(\d+\*\d+)英寸[^】]*】',                  # 【12*16英寸帆框】
        r'(\d+\*\d+)英寸',                           # 12*16英寸

        # 中文厘米格式
        r'【A\s*-\s*(\d+\.\d+\*\d+\.\d+)厘米[^】]*】', # 【A -30.0*40.0厘米帆框】
        r'【(\d+\.\d+\*\d+\.\d+)厘米[^】]*】',        # 【30.0*40.0厘米帆框】
        r'(\d+\.\d+\*\d+\.\d+)厘米',                # 30.0*40.0厘米
        r'【A\s*-\s*(\d+\*\d+)厘米[^】]*】',         # 【A -30*40厘米帆框】
        r'【(\d+\*\d+)厘米[^】]*】',                 # 【30*40厘米帆框】
        r'(\d+\*\d+)厘米',                          # 30*40厘米

        # 中文公分格式
        r'【A\s*-\s*(\d+\.\d+\*\d+\.\d+)公分[^】]*】', # 【A -30.0*40.0公分帆框】
        r'【(\d+\.\d+\*\d+\.\d+)公分[^】]*】',        # 【30.0*40.0公分帆框】
        r'(\d+\.\d+\*\d+\.\d+)公分',                # 30.0*40.0公分
        r'【A\s*-\s*(\d+\*\d+)公分[^】]*】',         # 【A -30*40公分帆框】
        r'【(\d+\*\d+)公分[^】]*】',                 # 【30*40公分帆框】
        r'(\d+\*\d+)公分',                          # 30*40公分

        # 中文cm格式
        r'【A\s*-\s*(\d+\.\d+\*\d+\.\d+)cm[^】]*】',  # 【A -30.0*40.0cm帆框】
        r'【(\d+\.\d+\*\d+\.\d+)cm[^】]*】',         # 【30.0*40.0cm帆框】
        r'(\d+\.\d+\*\d+\.\d+)cm',                 # 30.0*40.0cm
        r'【A\s*-\s*(\d+\*\d+)cm[^】]*】',          # 【A -30*40cm帆框】
        r'【(\d+\*\d+)cm[^】]*】',                  # 【30*40cm帆框】
        r'(\d+\*\d+)cm',                           # 30*40cm

        # 标准格式
        r'(\d+\.\d+x\d+\.\d+inch)',                  # 11.8x15.7inch
        r'(\d+x\d+inch)',                            # 12x16inch
        r'(\d+\.\d+x\d+\.\d+cm)',                    # 11.8x15.7cm
        r'(\d+x\d+cm)',                              # 30x40cm

        # 带前缀的格式
        r'([A-Z]-\d+\.\d+x\d+\.\d+cm)',             # G-30.0x40.0cm
        r'([A-Z]-\d+x\d+cm)',                        # G-30x40cm, H-30x40cm
        r'([A-Z]-\d+\.\d+x\d+\.\d+)',               # G-30.0x40.0
        r'([A-Z]-\d+x\d+)',                          # G-30x40, H-30x40

        # 无单位格式
        r'(\d+\.\d+x\d+\.\d+)',                      # 11.8x15.7 (无单位)
        r'(\d+x\d+)',                                # 30x40 (无单位)
    ]

    patterns.extend(base_patterns)

    # 从当前映射表A列动态生成模式
    if _template_mapping:
        logging.info(f"🔍 [动态模式] 开始从映射表A列生成尺寸模式，当前映射表有 {len(_template_mapping)} 条")

        for original_attr in _template_mapping.keys():
            # 分析A列内容，生成对应的正则表达式
            dynamic_patterns = analyze_and_generate_patterns(original_attr)
            patterns.extend(dynamic_patterns)

    # 去重并返回
    unique_patterns = list(dict.fromkeys(patterns))  # 保持顺序的去重
    logging.info(f"🔍 [动态模式] 总共生成 {len(unique_patterns)} 个尺寸匹配模式")
    return unique_patterns

def analyze_and_generate_patterns(text):
    """
    分析映射表A列的文本，生成对应的正则表达式模式

    Args:
        text: 映射表A列的文本

    Returns:
        list: 生成的正则表达式模式列表
    """
    if not text or not isinstance(text, str):
        return []

    patterns = []

    # 特殊处理：复杂格式 A-15.8in/40cm*11.8in/30cm
    if 'in/' in text and 'cm*' in text and 'in/' in text:
        # 生成专门提取厘米部分的模式
        # 匹配格式：A-数字in/数字cm*数字in/数字cm，提取两个厘米数字
        complex_pattern = r'[A-Z]?-?\d+(?:\.\d+)?in/(\d+(?:\.\d+)?)cm\*\d+(?:\.\d+)?in/(\d+(?:\.\d+)?)cm'
        patterns.append(complex_pattern)
        logging.debug(f"🔍 [动态模式] 复杂格式专用模式: '{text}' -> '{complex_pattern}'")

        # 还要生成一个简化的模式，直接匹配整个字符串
        escaped_text = re.escape(text)
        patterns.append(f'({escaped_text})')
        logging.debug(f"🔍 [动态模式] 完整匹配模式: '{text}' -> '({escaped_text})'")

        return patterns

    # 转义特殊字符，但保留数字和基本符号
    escaped_text = re.escape(text)

    # 将具体数字替换为通用模式
    # 替换整数
    pattern = re.sub(r'\\(\d+)', r'(\\d+)', escaped_text)
    # 替换小数
    pattern = re.sub(r'\\(\d+)\\\.\\(\d+)', r'(\\d+\\.\\d+)', pattern)
    # 替换连续的数字模式
    pattern = re.sub(r'\(\\d\+\)\\\.\(\\d\+\)', r'(\\d+(?:\\.\\d+)?)', pattern)
    pattern = re.sub(r'\(\\d\+\)', r'(\\d+(?:\\.\\d+)?)', pattern)

    # 如果生成的模式有意义（包含数字模式），则添加
    if '(\\d+' in pattern and pattern != escaped_text:
        patterns.append(pattern)
        logging.debug(f"🔍 [动态模式] 从 '{text}' 生成模式: '{pattern}'")

    return patterns

def extract_skc_and_size_from_text_enhanced(text):
    """
    增强版文本提取函数，动态支持映射表A列中的所有尺寸格式

    Args:
        text: PDF页面提取的文本

    Returns:
        tuple: (skc, size_info) 或 (None, None)
    """
    if not text:
        return None, None

    # 提取SKC (9-11位数字)
    skc_match = re.search(r'\b(\d{9,11})\b', text)
    skc = skc_match.group(1) if skc_match else None

    # 动态生成尺码匹配模式
    size_info = None

    # 使用动态生成的尺寸模式
    size_patterns = generate_dynamic_size_patterns()

    for pattern in size_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            size_info = match.group(1)
            logging.info(f"🏷️ [尺寸提取] 匹配到尺码格式: '{pattern}' -> '{size_info}'")
            break

    # 清理尺码信息
    if size_info:
        size_info = clean_size_info(size_info)

    logging.info(f"🏷️ [尺寸提取] 从PDF文本提取: SKC={skc}, 尺码={size_info}")
    return skc, size_info

def infer_size_from_picking_list(skc, df):
    """
    从拣货单中推断SKC对应的尺寸信息

    Args:
        skc: SKC编号
        df: 拣货单DataFrame

    Returns:
        str: 推断的尺寸信息，如果没有找到返回None
    """
    if not skc or df is None or df.empty:
        return None

    logging.info(f"🏷️ [尺寸推断] 开始为SKC {skc} 从拣货单推断尺寸")

    # 在拣货单中查找包含该SKC的行
    for _, row in df.iterrows():
        product_info = row.get('商品信息', '')
        attribute_set = row.get('属性集', '')

        if not isinstance(product_info, str) or not product_info.strip():
            continue

        # 检查是否包含目标SKC
        if skc in product_info:
            logging.info(f"🏷️ [尺寸推断] 找到SKC {skc} 在商品信息: {product_info}")
            logging.info(f"🏷️ [尺寸推断] 对应属性集: {attribute_set}")

            # 从属性集中提取尺寸信息
            if attribute_set:
                # 首先尝试从属性集直接提取尺寸
                size_from_attr = extract_size_from_attribute(attribute_set)
                if size_from_attr:
                    logging.info(f"🏷️ [尺寸推断] 从属性集提取尺寸: {size_from_attr}")
                    return size_from_attr

                # 如果直接提取失败，尝试通过模板映射获取目标属性集
                target_attribute = get_template_mapping(attribute_set)
                if target_attribute and target_attribute != attribute_set:
                    size_from_target = extract_size_from_attribute(target_attribute)
                    if size_from_target:
                        logging.info(f"🏷️ [尺寸推断] 从目标属性集提取尺寸: {size_from_target}")
                        return size_from_target

            # 尝试从商品信息中直接提取尺寸
            size_from_product = extract_size_from_text_general(product_info)
            if size_from_product:
                logging.info(f"🏷️ [尺寸推断] 从商品信息提取尺寸: {size_from_product}")
                return size_from_product

    logging.info(f"🏷️ [尺寸推断] 无法为SKC {skc} 推断尺寸")
    return None

def extract_size_from_text_general(text):
    """
    从一般文本中提取尺寸信息

    Args:
        text: 文本内容

    Returns:
        str: 提取的尺寸信息，如果没有找到返回None
    """
    if not text:
        return None

    # 通用尺寸匹配模式
    size_patterns = [
        r'(\d+x\d+cm)',           # 30x40cm
        r'(\d+\.\d+x\d+\.\d+inch)', # 11.8x15.7inch
        r'(\d+x\d+inch)',         # 12x16inch
        r'(\d+\.\d+x\d+\.\d+cm)', # 11.8x15.7cm
        r'(\d+x\d+)',             # 30x40 (无单位)
    ]

    for pattern in size_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            size_info = match.group(1)
            logging.info(f"🏷️ [尺寸推断] 从文本提取尺寸: '{text}' -> '{size_info}'")
            return size_info

    return None

def extract_sku_from_pdf_left_top(pdf_path):
    """
    从小标PDF左上角提取SKU货号信息

    Args:
        pdf_path: PDF文件路径

    Returns:
        str: 提取到的SKU货号，如果提取失败返回None
    """
    try:
        logging.info(f"🔍🔍🔍 [SKU提取] 开始从PDF左上角提取SKU货号: {pdf_path}")

        # 延迟导入pdfplumber
        pdfplumber = lazy_import_pdfplumber()
        if not pdfplumber:
            logging.error(f"🔍🔍🔍 [SKU提取] pdfplumber导入失败")
            return None

        with pdfplumber.open(pdf_path) as pdf:
            if len(pdf.pages) > 0:
                page = pdf.pages[0]

                # 获取页面尺寸
                page_width = page.width
                page_height = page.height
                logging.info(f"🔍🔍🔍 [SKU提取] 页面尺寸: {page_width} x {page_height}")

                # 定义左上角区域 (左上角1/4区域)
                left_top_bbox = (
                    0,                      # 左边界：页面左边
                    0,                      # 上边界：页面顶部
                    page_width * 0.5,       # 右边界：页面宽度的50%
                    page_height * 0.25      # 下边界：页面高度的25%
                )
                logging.info(f"🔍🔍🔍 [SKU提取] 左上角区域: {left_top_bbox}")

                # 提取左上角区域的文本
                left_top_text = page.within_bbox(left_top_bbox).extract_text()
                logging.info(f"🔍🔍🔍 [SKU提取] 左上角文本: '{left_top_text}'")

                if left_top_text:
                    # 查找SKU货号模式
                    import re

                    # SKU货号模式（如：zxl0614db0329-16x16）
                    sku_patterns = [
                        r'([a-zA-Z0-9]+(?:-[a-zA-Z0-9]+)*)',  # 通用SKU格式
                        r'([a-zA-Z]+\d+[a-zA-Z]*\d*(?:-\d+x\d+)?)',  # zxl0614db0329-16x16格式
                        r'([a-zA-Z]{2,}\d{4,}[a-zA-Z]*\d*)',  # 字母+数字组合
                    ]

                    for pattern in sku_patterns:
                        matches = re.findall(pattern, left_top_text.strip())
                        if matches:
                            # 返回第一个匹配的SKU货号
                            sku_number = matches[0].strip()
                            logging.info(f"✅ 从PDF左上角提取到SKU货号: {sku_number}")
                            return sku_number

                    logging.info(f"❌ 未能从左上角文本中提取到SKU货号: '{left_top_text.strip()}'")
                else:
                    logging.info(f"❌ 左上角区域没有文本")

    except Exception as e:
        logging.warning(f"从PDF左上角提取SKU货号失败: {pdf_path}, 错误: {e}")

    return None

def extract_size_from_pdf_content(pdf_path):
    """
    从小标PDF右上角提取属性集信息

    Args:
        pdf_path: PDF文件路径

    Returns:
        str: 提取到的属性集信息，如果提取失败返回None
    """
    try:
        logging.info(f"🔍🔍🔍 [PDF内容提取] 开始处理PDF: {pdf_path}")

        # 延迟导入pdfplumber
        pdfplumber = lazy_import_pdfplumber()
        if not pdfplumber:
            logging.error(f"🔍🔍🔍 [PDF内容提取] pdfplumber导入失败")
            return None

        logging.info(f"🔍🔍🔍 [PDF内容提取] pdfplumber导入成功")

        with pdfplumber.open(pdf_path) as pdf:
            logging.info(f"🔍🔍🔍 [PDF内容提取] PDF打开成功，页数: {len(pdf.pages)}")

            if len(pdf.pages) > 0:
                page = pdf.pages[0]

                # 获取页面尺寸
                page_width = page.width
                page_height = page.height
                logging.info(f"🔍🔍🔍 [PDF内容提取] 页面尺寸: {page_width} x {page_height}")

                # 定义右上角区域 (右上角1/4区域)
                right_top_bbox = (
                    page_width * 0.75,  # 左边界：页面宽度的75%
                    0,                  # 上边界：页面顶部
                    page_width,         # 右边界：页面右边
                    page_height * 0.25  # 下边界：页面高度的25%
                )
                logging.info(f"🔍🔍🔍 [PDF内容提取] 右上角区域: {right_top_bbox}")

                # 提取右上角区域的文本
                right_top_text = page.within_bbox(right_top_bbox).extract_text()
                logging.info(f"🔍🔍🔍 [PDF内容提取] 右上角文本: '{right_top_text}'")

                if right_top_text:
                    logging.info(f"🔍 从PDF右上角提取的文本: {right_top_text.strip()}")

                    # 查找属性集模式
                    import re

                    # 匹配各种属性集格式
                    attribute_patterns = [
                        r'(\d+\.?\d*\s*[xX×]\s*\d+\.?\d*\s*cm)',
                        r'(\d+\.?\d*\s*[xX×]\s*\d+\.?\d*\s*inch)',
                        r'(\d+\.?\d*\s*[xX×]\s*\d+\.?\d*)',
                        r'([A-Za-z0-9\-\.]+\s*[xX×]\s*[A-Za-z0-9\-\.]+)',
                    ]

                    for pattern in attribute_patterns:
                        matches = re.findall(pattern, right_top_text)
                        if matches:
                            # 返回第一个匹配的属性集
                            attribute_set = matches[0].strip()
                            logging.info(f"✅ 从PDF右上角提取到属性集: {attribute_set}")
                            return attribute_set

                # 如果右上角没有找到，尝试提取整页文本中的属性集信息
                full_text = page.extract_text()
                logging.info(f"🔍🔍🔍 [PDF内容提取] 整页文本: '{full_text}'")

                if full_text:
                    import re

                    # 首先尝试匹配完整的属性集格式（如：A-15.8in/40cm*23.8in/60cm）
                    attribute_patterns = [
                        r'([A-Za-z0-9\-\.]+in/\d+cm\*[A-Za-z0-9\-\.]+in/\d+cm)',  # A-15.8in/40cm*23.8in/60cm
                        r'(\d+\.?\d*\s*[xX×]\s*\d+\.?\d*\s*cm)',                    # 30x40cm
                        r'(\d+\.?\d*\s*[xX×]\s*\d+\.?\d*\s*inch)',                  # 30x40inch
                        r'(\d+\.?\d*\s*[xX×]\s*\d+\.?\d*)',                         # 30x40
                        r'([A-Za-z0-9\-\.]+\s*[xX×]\s*[A-Za-z0-9\-\.]+)',          # 通用格式
                    ]

                    for pattern in attribute_patterns:
                        matches = re.findall(pattern, full_text)
                        if matches:
                            # 返回第一个匹配的属性集
                            attribute_set = matches[0].strip()
                            logging.info(f"✅ 从PDF全文提取到属性集: {attribute_set}")
                            return attribute_set

                    # 如果没有找到完整属性集，尝试查找尺寸信息作为备用
                    size_patterns = [
                        r'(\d+\.?\d*)\s*[xX×]\s*(\d+\.?\d*)\s*cm',
                        r'(\d+\.?\d*)\s*[xX×]\s*(\d+\.?\d*)\s*inch',
                    ]

                    for pattern in size_patterns:
                        match = re.search(pattern, full_text)
                        if match:
                            width = match.group(1)
                            height = match.group(2)
                            if 'cm' in pattern:
                                size_info = f"{width}x{height}cm"
                            else:
                                size_info = f"{width}x{height}inch"
                            logging.info(f"✅ 从PDF全文提取到尺寸信息: {size_info}")
                            return size_info

    except Exception as e:
        logging.warning(f"从PDF提取属性集失败: {pdf_path}, 错误: {e}")

    return None

def is_size_match_attribute_set(pdf_attribute_set, current_attribute_set, target_folders):
    """
    检查PDF中的属性集是否与当前属性集匹配

    Args:
        pdf_attribute_set: 从PDF右上角提取的属性集信息
        current_attribute_set: 当前处理的属性集
        target_folders: 目标文件夹列表（暂未使用）

    Returns:
        bool: 是否匹配
    """
    if not pdf_attribute_set or not current_attribute_set:
        return False

    try:
        # 标准化属性集格式进行比较
        def normalize_attribute_set(attr_str):
            if not attr_str:
                return ""
            # 移除空格，统一格式
            import re
            attr_str = re.sub(r'\s+', '', attr_str.lower())
            # 统一分隔符
            attr_str = re.sub(r'[×x]', 'x', attr_str)
            return attr_str

        pdf_attr_normalized = normalize_attribute_set(pdf_attribute_set)
        current_attr_normalized = normalize_attribute_set(current_attribute_set)

        # 直接比较
        if pdf_attr_normalized == current_attr_normalized:
            logging.info(f"✅ 属性集直接匹配: PDF={pdf_attribute_set} vs 当前={current_attribute_set}")
            return True

        # 尝试通过模板映射进行比较
        pdf_mapped = get_template_mapping(pdf_attribute_set)
        current_mapped = get_template_mapping(current_attribute_set)

        if pdf_mapped and current_mapped:
            pdf_mapped_normalized = normalize_attribute_set(pdf_mapped)
            current_mapped_normalized = normalize_attribute_set(current_mapped)

            if pdf_mapped_normalized == current_mapped_normalized:
                logging.info(f"✅ 属性集映射匹配: PDF={pdf_mapped} vs 当前={current_mapped}")
                return True

        # 尝试提取数字进行比较（针对尺寸信息）- 必须所有数字都匹配
        import re
        pdf_numbers = re.findall(r'\d+\.?\d*', pdf_attr_normalized)
        current_numbers = re.findall(r'\d+\.?\d*', current_attr_normalized)

        logging.info(f"🔍🔍🔍 [数值匹配] PDF数字: {pdf_numbers}, 当前数字: {current_numbers}")

        # 只有当数字数量相同且所有数字都匹配时才认为匹配
        if len(pdf_numbers) == len(current_numbers) and len(pdf_numbers) >= 2:
            # 将数字转换为浮点数进行比较
            pdf_floats = [float(x) for x in pdf_numbers]
            current_floats = [float(x) for x in current_numbers]

            # 检查所有数字是否匹配（允许顺序不同）
            def numbers_match(list1, list2):
                if len(list1) != len(list2):
                    return False
                # 尝试直接匹配
                if all(abs(a - b) < 0.1 for a, b in zip(list1, list2)):
                    return True
                # 尝试反向匹配（只对4个数字的情况）
                if len(list1) == 4:
                    # A-15.8in/40cm*23.8in/60cm vs A-15.8in/40cm*11.8in/30cm
                    # 应该比较: [15.8, 40, 23.8, 60] vs [15.8, 40, 11.8, 30]
                    # 不应该匹配，因为23.8!=11.8, 60!=30
                    return False
                return False

            if numbers_match(pdf_floats, current_floats):
                logging.info(f"✅ 属性集数值完全匹配: PDF={pdf_floats} vs 当前={current_floats}")
                return True
            else:
                logging.info(f"❌ 属性集数值不匹配: PDF={pdf_floats} vs 当前={current_floats}")
        else:
            logging.info(f"❌ 属性集数字数量不匹配: PDF有{len(pdf_numbers)}个数字, 当前有{len(current_numbers)}个数字")

        logging.info(f"❌ 属性集不匹配: PDF={pdf_attribute_set} vs 当前={current_attribute_set}")
        return False

    except Exception as e:
        logging.warning(f"属性集匹配检查失败: {e}")
        return False

def find_matching_small_pdf_by_skc_and_size(small_pdf_folder, skc_base, target_folders, current_attribute_set=None, df=None):
    """
    简化的小标PDF匹配逻辑：
    1. 先用SKC匹配小标PDF文件
    2. 如果没有找到SKC PDF，尝试查找对应的SKU PDF
    3. 如果有多个匹配文件，从PDF右上角提取尺码信息
    4. 将提取的尺码与当前属性集的尺码进行匹配

    Args:
        small_pdf_folder: 小标PDF文件夹路径
        skc_base: SKC编号
        target_folders: 目标文件夹列表，用于尺码匹配
        current_attribute_set: 当前处理的属性集，用于确定目标尺码
        df: 拣货单DataFrame，用于SKC-SKU映射

    Returns:
        str: 匹配的PDF文件名，如果没有找到返回None
    """
    if not os.path.isdir(small_pdf_folder):
        return None

    logging.info(f"🔍 匹配小标PDF: SKC={skc_base}")

    # 🔥 调试：列出小标PDF文件夹中的所有文件
    if os.path.exists(small_pdf_folder):
        all_files = os.listdir(small_pdf_folder)
        pdf_files = [f for f in all_files if f.lower().endswith('.pdf')]
        logging.info(f"🔥🔥🔥 [调试] 小标PDF文件夹中的所有PDF文件: {pdf_files}")
    else:
        logging.error(f"🔥🔥🔥 [调试] 小标PDF文件夹不存在: {small_pdf_folder}")

    # 第一步：收集所有匹配SKC的PDF文件
    skc_matched_files = []
    sku_named_files = []  # 新增：收集SKU货号命名的PDF文件

    for filename in os.listdir(small_pdf_folder):
        if not filename.lower().endswith('.pdf'):
            continue

        logging.info(f"🔥🔥🔥 [调试] 检查文件: {filename}")

        # 首先检查PDF内容，判断是SKC命名还是SKU货号命名
        pdf_path = os.path.join(small_pdf_folder, filename)
        left_top_sku = extract_sku_from_pdf_left_top(pdf_path)

        if left_top_sku and re.search(r'[a-zA-Z]', left_top_sku):
            # 左上角包含字母数字混合，这是SKU货号命名格式
            logging.info(f"🏷️ [SKU命名] 检测到SKU货号命名格式: {filename}, 左上角SKU: {left_top_sku}")
            sku_named_files.append((filename, left_top_sku))
            continue

        # 检查多种SKC文件名格式
        file_skc = None
        page_number = ""

        # 格式1：SKC-页数.pdf
        new_format_match = re.match(r'(\d{9,11})-(\d+)\.pdf$', filename, re.IGNORECASE)
        if new_format_match:
            file_skc = new_format_match.group(1)
            page_number = new_format_match.group(2)
            logging.info(f"🔥🔥🔥 [调试] 格式1匹配成功: file_skc={file_skc}, page_number={page_number}")

        # 格式2：SKC.pdf
        elif re.match(r'(\d{9,11})\.pdf$', filename, re.IGNORECASE):
            file_skc = filename.split('.')[0]
            page_number = ""

        # 格式3：更宽松的匹配，文件名中包含SKC
        elif skc_base in filename:
            # 尝试从文件名中提取SKC
            skc_pattern = r'(\d{9,11})'
            skc_matches_in_name = re.findall(skc_pattern, filename)
            for potential_skc in skc_matches_in_name:
                if (skc_base == potential_skc or
                    (len(skc_base) == 9 and potential_skc.startswith(skc_base) and len(potential_skc) == 11)):
                    file_skc = potential_skc
                    # 尝试提取页数
                    page_match = re.search(r'-(\d+)\.pdf$', filename, re.IGNORECASE)
                    page_number = page_match.group(1) if page_match else ""
                    break

        # 如果找到了匹配的SKC，进行进一步检查
        if file_skc:
            skc_matches = (skc_base == file_skc or
                          (len(skc_base) == 9 and file_skc.startswith(skc_base) and len(file_skc) == 11))

            if skc_matches:
                skc_matched_files.append((filename, page_number))
                if page_number:
                    logging.info(f"  SKC匹配文件: {filename} (SKC: {file_skc}, 页数: {page_number})")
                else:
                    logging.info(f"  SKC匹配文件: {filename} (SKC: {file_skc})")

    # 🏷️ 优先处理SKU货号命名的文件
    if sku_named_files:
        logging.info(f"🏷️ [SKU命名] 找到 {len(sku_named_files)} 个SKU货号命名的PDF文件")

        # 对SKU货号命名的文件进行属性集匹配
        if current_attribute_set:
            logging.info(f"🏷️ [SKU命名] 根据当前属性集过滤SKU命名文件: {current_attribute_set}")

            filtered_sku_files = []
            for filename, sku_number in sku_named_files:
                pdf_path = os.path.join(small_pdf_folder, filename)

                # 从PDF右上角提取属性集信息
                pdf_attribute_set = extract_size_from_pdf_content(pdf_path)
                logging.info(f"🏷️ [SKU命名] PDF {filename} 属性集: {pdf_attribute_set}")

                if pdf_attribute_set and is_size_match_attribute_set(pdf_attribute_set, current_attribute_set, target_folders):
                    # 查找对应的SKU ID来重命名文件
                    corresponding_sku_id = None

                    # 从映射表中查找SKU货号对应的SKU ID
                    if hasattr(find_matching_small_pdf_by_skc_and_size, '_sku_mapping'):
                        sku_mapping = find_matching_small_pdf_by_skc_and_size._sku_mapping
                        for skc, sku_list in sku_mapping.items():
                            for i in range(0, len(sku_list), 2):  # 每两个为一组：SKU货号, SKU ID
                                if i + 1 < len(sku_list):
                                    mapped_sku_name = sku_list[i]
                                    mapped_sku_id = sku_list[i + 1]
                                    if mapped_sku_name == sku_number:
                                        corresponding_sku_id = mapped_sku_id
                                        break
                            if corresponding_sku_id:
                                break

                    # 始终使用SKU货号命名，不使用SKU ID
                    new_filename = f"{sku_number}.pdf"
                    logging.info(f"🏷️ [SKU命名] ✅ 匹配成功，使用SKU货号命名: {new_filename}")
                    filtered_sku_files.append((filename, new_filename, sku_number))
                else:
                    logging.info(f"🏷️ [SKU命名] ❌ 属性集不匹配: PDF={pdf_attribute_set} vs 当前={current_attribute_set}")

            if filtered_sku_files:
                logging.info(f"🏷️ [SKU命名] 找到匹配的SKU命名文件: {len(filtered_sku_files)} 个")
                # 返回需要重命名的文件信息
                return [(original_name, new_name, sku) for original_name, new_name, sku in filtered_sku_files]
        else:
            # 没有属性集信息，返回所有SKU命名文件
            logging.info(f"🏷️ [SKU命名] 无属性集信息，返回所有SKU命名文件")
            return [(filename, f"{sku_number}.pdf", sku_number) for filename, sku_number in sku_named_files]

    # 🔥 重要修复：如果找到了SKC匹配的文件，需要根据当前属性集进行过滤
    if skc_matched_files:
        logging.info(f"✅ 找到 {len(skc_matched_files)} 个SKC匹配的PDF文件")

        # 当匹配到多个文件且有属性集信息时，进行属性集过滤
        if current_attribute_set and len(skc_matched_files) > 1:
            logging.info(f"🔍 多个SKC PDF文件匹配，根据当前属性集过滤: {current_attribute_set}")

            # 尝试从PDF内容中提取属性集信息，匹配当前属性集
            filtered_files = []
            for filename, page_number in skc_matched_files:
                pdf_path = os.path.join(small_pdf_folder, filename)
                if os.path.exists(pdf_path):
                    # 从PDF右上角提取属性集信息
                    logging.info(f"🔍🔍🔍 [PDF属性集提取] 开始提取PDF属性集: {pdf_path}")
                    logging.info(f"🔍🔍🔍 [PDF属性集提取] PDF文件是否存在: {os.path.exists(pdf_path)}")

                    pdf_attribute_set = extract_size_from_pdf_content(pdf_path)
                    logging.info(f"🔍🔍🔍 [PDF属性集提取] 提取结果: {pdf_attribute_set}")

                    if pdf_attribute_set:
                        # 检查是否与当前属性集匹配
                        logging.info(f"🔍🔍🔍 [PDF属性集匹配] 开始匹配检查: PDF={pdf_attribute_set} vs 当前={current_attribute_set}")
                        if is_size_match_attribute_set(pdf_attribute_set, current_attribute_set, target_folders):
                            filtered_files.append(filename)
                            logging.info(f"✅ SKC PDF文件 {filename} 属性集匹配: PDF={pdf_attribute_set} vs 当前={current_attribute_set}")
                        else:
                            logging.info(f"❌ SKC PDF文件 {filename} 属性集不匹配: PDF={pdf_attribute_set} vs 当前={current_attribute_set}")
                    else:
                        # 如果无法提取属性集，保留文件（保守策略）
                        filtered_files.append(filename)
                        logging.info(f"⚠️ SKC PDF文件 {filename} 无法提取属性集，保留")

            if filtered_files:
                logging.info(f"✅ 过滤后匹配当前属性集的SKC PDF文件: {filtered_files}")
                return filtered_files
            else:
                logging.warning(f"❌ 过滤后没有匹配当前属性集的SKC PDF文件，返回第一个文件")
                return [skc_matched_files[0][0]]
        else:
            # 单个文件或没有属性集信息，直接返回所有文件
            logging.info(f"✅ 单个SKC文件或无属性集信息，返回所有匹配文件: {len(skc_matched_files)} 个")
            return [filename for filename, _ in skc_matched_files]

    if not skc_matched_files:
        logging.warning(f"❌ 未找到匹配SKC {skc_base} 的小标PDF文件")

        # 尝试查找对应的SKU PDF文件
        if df is not None:
            logging.info(f"🔍 尝试查找SKC {skc_base} 对应的SKU PDF文件...")
            skc_to_sku_list, sku_to_skc, skc_to_attribute_sets, sku_to_attribute_sets = build_skc_sku_mapping(df)

            # 查找对应的SKU货号
            if skc_base in skc_to_sku_list:
                sku_list = skc_to_sku_list[skc_base]
                logging.info(f"🔍 找到对应SKU货号列表: {sku_list}")

                # 查找匹配当前属性集的SKU货号命名的PDF文件
                sku_matched_files = []
                for sku_hao in sku_list:
                    # 清理SKU货号：去除换行符、制表符和多余空格
                    sku_hao_str = str(sku_hao).strip().replace('\n', '').replace('\r', '').replace('\t', ' ')
                    sku_hao_str = ' '.join(sku_hao_str.split())  # 去除多余空格

                    if not sku_hao_str:
                        continue

                    # 检查这个SKU货号是否对应当前属性集
                    if sku_hao_str in sku_to_attribute_sets:
                        sku_attribute_sets = sku_to_attribute_sets[sku_hao_str]
                        # 检查当前属性集是否在这个SKU货号的属性集列表中
                        if current_attribute_set and current_attribute_set in sku_attribute_sets:
                            logging.info(f"🔍 查找SKU货号 {sku_hao_str} 命名的PDF文件（匹配当前属性集 {current_attribute_set}）...")

                            sku_matched_count = 0
                            for filename in os.listdir(small_pdf_folder):
                                if not filename.lower().endswith('.pdf'):
                                    continue

                                # 多种匹配方式
                                filename_lower = filename.lower()
                                sku_hao_lower = sku_hao_str.lower()

                                matched = False
                                match_type = ""

                                # 方式1：文件名以SKU货号开头
                                if filename_lower.startswith(sku_hao_lower):
                                    matched = True
                                    match_type = "开头匹配"

                                # 方式2：文件名包含SKU货号
                                elif sku_hao_lower in filename_lower:
                                    matched = True
                                    match_type = "包含匹配"

                                # 方式3：去掉文件扩展名后完全匹配
                                elif filename_lower.replace('.pdf', '') == sku_hao_lower:
                                    matched = True
                                    match_type = "完全匹配"

                                if matched:
                                    sku_matched_files.append((filename, ""))
                                    sku_matched_count += 1
                                    logging.info(f"✅ 找到SKU PDF文件({match_type}): {filename} (SKU货号: {sku_hao_str}, 属性集: {current_attribute_set})")

                            if sku_matched_count > 0:
                                logging.info(f"✅ SKU货号 {sku_hao_str} 共找到 {sku_matched_count} 个匹配的PDF文件")
                        else:
                            logging.info(f"🔍 SKU货号 {sku_hao_str} 不对应当前属性集 {current_attribute_set}，跳过")
                    else:
                        logging.info(f"🔍 SKU货号 {sku_hao_str} 没有属性集映射信息，跳过")

                # 继续查找所有匹配的SKU，不要在找到第一个后就停止

                if sku_matched_files:
                    logging.info(f"✅ 找到 {len(sku_matched_files)} 个SKU PDF文件")
                    logging.info(f"🔥🔥🔥 [调试] 匹配文件数: {len(sku_matched_files)}, 当前属性集: {current_attribute_set}")
                    logging.info(f"🔥🔥🔥 [调试] 匹配的文件: {[f[0] for f in sku_matched_files]}")

                    # 🔥 重要修复：根据当前属性集过滤PDF文件，避免多文件夹匹配问题
                    if current_attribute_set and len(sku_matched_files) > 1:
                        logging.info(f"🔍 多个PDF文件匹配，根据当前属性集过滤: {current_attribute_set}")

                        # 尝试从PDF内容中提取尺寸信息，匹配当前属性集
                        filtered_files = []
                        for filename, _ in sku_matched_files:
                            pdf_path = os.path.join(small_pdf_folder, filename)
                            if os.path.exists(pdf_path):
                                # 从PDF右上角提取属性集信息
                                pdf_attribute_set = extract_size_from_pdf_content(pdf_path)
                                if pdf_attribute_set:
                                    # 检查是否与当前属性集匹配
                                    if is_size_match_attribute_set(pdf_attribute_set, current_attribute_set, target_folders):
                                        filtered_files.append(filename)
                                        logging.info(f"✅ PDF文件 {filename} 属性集匹配: PDF={pdf_attribute_set} vs 当前={current_attribute_set}")
                                    else:
                                        logging.info(f"❌ PDF文件 {filename} 属性集不匹配: PDF={pdf_attribute_set} vs 当前={current_attribute_set}")
                                else:
                                    # 如果无法提取属性集，保留文件（保守策略）
                                    filtered_files.append(filename)
                                    logging.info(f"⚠️ PDF文件 {filename} 无法提取属性集，保留")

                        if filtered_files:
                            logging.info(f"✅ 过滤后匹配当前属性集的PDF文件: {filtered_files}")
                            return filtered_files
                        else:
                            logging.warning(f"❌ 过滤后没有匹配当前属性集的PDF文件")
                            # 如果过滤后没有文件，返回第一个文件（保守策略）
                            return [sku_matched_files[0][0]]
                    else:
                        # 单个文件或没有属性集信息，直接返回所有文件
                        logging.info(f"✅ 直接返回所有匹配的PDF文件: {len(sku_matched_files)} 个")
                        return [filename for filename, _ in sku_matched_files]
                else:
                    logging.warning(f"❌ 所有SKU货号都未找到对应的PDF文件")
                    return None
            else:
                logging.warning(f"❌ 未找到SKC {skc_base} 对应的SKU货号")
                return None
        else:
            return None

    # 第二步：直接返回所有匹配的文件，不在这里进行合并判断
    # 合并逻辑将在移动到目标文件夹后进行
    if len(skc_matched_files) >= 1:
        if len(skc_matched_files) == 1:
            logging.info(f"✅ 找到1个匹配文件: {skc_matched_files[0][0]}")
            return [skc_matched_files[0][0]]  # 统一返回列表格式
        else:
            logging.info(f"✅ 找到 {len(skc_matched_files)} 个匹配文件，将先移动再合并")
            return [filename for filename, _ in skc_matched_files]

    # 第四步：如果有多个不同SKC的匹配文件，通过尺码匹配选择最合适的
    logging.info(f"🔍 找到 {len(skc_matched_files)} 个不同SKC的匹配文件，开始尺码匹配...")

    # 从目标文件夹中提取尺码信息
    target_sizes = []
    for folder_path in target_folders:
        folder_name = os.path.basename(folder_path)
        size_info = extract_size_from_folder_name(folder_name)
        if size_info:
            target_sizes.append((folder_path, size_info))
            logging.info(f"  目标文件夹尺码: {folder_name} -> {size_info}")

    if not target_sizes:
        logging.warning(f"⚠️ 无法从目标文件夹中提取尺码信息，返回所有匹配文件")
        return [filename for filename, _ in skc_matched_files]

    # 分析每个PDF文件的尺码信息
    pdf_size_info = []
    for filename, page_number in skc_matched_files:
        pdf_path = os.path.join(small_pdf_folder, filename)
        extracted_size = extract_size_from_pdf_file(pdf_path)

        if extracted_size:
            standardized_pdf_size = parse_and_standardize_size(extracted_size)
            pdf_size_info.append((filename, standardized_pdf_size, extracted_size))
            logging.info(f"  {filename}: 提取尺码='{extracted_size}' -> 标准化='{standardized_pdf_size}'")
        else:
            pdf_size_info.append((filename, None, None))
            logging.info(f"  {filename}: 无法提取尺码信息")

    # 为当前处理的目标文件夹找到最匹配的PDF
    best_candidate = None
    best_score = 0
    current_target_size = None

    # 从当前属性集中提取尺码信息
    if current_attribute_set:
        current_target_size = extract_size_from_folder_name(current_attribute_set)
        if not current_target_size:
            # 如果属性集中没有尺码，尝试从目标文件夹中提取
            for folder_path, target_size in target_sizes:
                folder_name = os.path.basename(folder_path)
                if current_attribute_set.lower() in folder_name.lower():
                    current_target_size = target_size
                    break

    # 如果还是没有找到，使用第一个目标文件夹的尺码
    if not current_target_size and target_sizes:
        current_target_size = target_sizes[0][1]

    logging.info(f"  当前属性集: {current_attribute_set}")
    logging.info(f"  当前目标尺码: {current_target_size}")

    for filename, standardized_pdf_size, original_size in pdf_size_info:
        if standardized_pdf_size:
            score = calculate_size_similarity(standardized_pdf_size, current_target_size)
            logging.info(f"    {filename} ({standardized_pdf_size}) 与 {current_target_size} 的相似度: {score}")

            if score > best_score:
                best_score = score
                best_candidate = filename

    if best_candidate and best_score >= 10:  # 完美匹配
        logging.info(f"✅ 完美匹配: {best_candidate} (分数: {best_score})")
        return [best_candidate]
    elif best_candidate and best_score >= 6:  # 良好匹配
        logging.info(f"✅ 良好匹配: {best_candidate} (分数: {best_score})")
        return [best_candidate]
    elif best_candidate and best_score >= 4:  # 可接受匹配
        logging.info(f"⚠️ 可接受匹配: {best_candidate} (分数: {best_score})")
        return [best_candidate]
    elif best_candidate and best_score > 0:  # 勉强匹配
        logging.info(f"⚠️ 勉强匹配: {best_candidate} (分数: {best_score})")
        return [best_candidate]

    # 如果没有找到任何匹配，详细记录诊断信息
    logging.warning(f"❌ 未找到合适的尺码匹配:")
    logging.warning(f"   SKC: {skc_base}")
    logging.warning(f"   目标尺码: {current_target_size}")
    logging.warning(f"   候选文件详情:")

    for filename, standardized_pdf_size, original_size in pdf_size_info:
        if original_size:
            logging.warning(f"     {filename}: 原始='{original_size}' -> 标准化='{standardized_pdf_size}'")
        else:
            logging.warning(f"     {filename}: 无法提取尺码信息")

    # 如果完全没有尺码信息，返回所有匹配文件
    if skc_matched_files:
        logging.warning(f"⚠️ 无法进行尺码匹配，返回所有 {len(skc_matched_files)} 个匹配文件")
        return [filename for filename, _ in skc_matched_files]

    return None

def extract_size_from_folder_name(folder_name):
    """
    从文件夹名称中提取尺码信息（动态支持映射表格式）

    Args:
        folder_name: 文件夹名称

    Returns:
        str: 提取的尺码信息，如果没有找到返回None
    """
    if not folder_name:
        return None

    # 使用动态生成的尺寸模式
    size_patterns = generate_dynamic_size_patterns()

    for pattern in size_patterns:
        try:
            match = re.search(pattern, folder_name, re.IGNORECASE)
            if match:
                # 特殊处理：复杂格式的厘米提取
                if 'in/' in pattern and 'cm\\*' in pattern:
                    # 这是复杂格式模式，提取两个厘米数字
                    if len(match.groups()) >= 2:
                        width_cm = match.group(1)
                        height_cm = match.group(2)
                        size_info = f"{width_cm}x{height_cm}cm"
                        logging.info(f"🔍 [文件夹尺寸] 复杂格式提取: '{folder_name}' -> '{size_info}' (模式: {pattern[:50]}...)")
                        return size_info

                # 获取第一个捕获组
                size_info = match.group(1) if match.groups() else match.group(0)

                # 特殊处理：如果匹配到完整的复杂格式，解析它
                if 'in/' in size_info and 'cm*' in size_info:
                    parsed_size = parse_complex_size_format(size_info)
                    if parsed_size:
                        logging.info(f"🔍 [文件夹尺寸] 复杂格式解析: '{folder_name}' -> '{parsed_size}' (模式: {pattern[:50]}...)")
                        return parsed_size

                # 如果没有单位，默认添加cm
                if size_info and not re.search(r'(cm|inch|厘米|公分|英寸)', size_info, re.IGNORECASE):
                    # 检查是否是纯数字格式（如30x40）
                    if re.match(r'^\d+(\.\d+)?x\d+(\.\d+)?$', size_info):
                        size_info += "cm"

                if size_info:
                    logging.info(f"🔍 [文件夹尺寸] 从文件夹名提取尺码: '{folder_name}' -> '{size_info}' (模式: {pattern[:50]}...)")
                    return size_info
        except re.error as e:
            # 如果正则表达式有问题，跳过这个模式
            logging.debug(f"🔍 [文件夹尺寸] 正则表达式错误，跳过模式: {pattern[:50]}... 错误: {e}")
            continue

    # 如果动态模式都没匹配到，使用基础模式作为后备
    basic_patterns = [
        r'(\d+x\d+cm)',           # 30x40cm
        r'(\d+\.\d+x\d+\.\d+cm)', # 11.8x15.7cm
        r'(\d+x\d+inch)',         # 12x16inch
        r'(\d+\.\d+x\d+\.\d+inch)', # 11.8x15.7inch
        r'(\d+x\d+)',             # 30x40 (无单位，假设为cm)
        # 新增：支持复杂格式 A-15.8in/40cm*11.8in/30cm
        r'(\d+\.?\d*in/\d+\.?\d*cm\*\d+\.?\d*in/\d+\.?\d*cm)',  # 15.8in/40cm*11.8in/30cm
        r'(\d+\.?\d*cm\*\d+\.?\d*cm)',  # 40cm*30cm
        r'(\d+\.?\d*/\d+\.?\d*cm\*\d+\.?\d*/\d+\.?\d*cm)',  # 15.8/40cm*11.8/30cm
    ]

    for pattern in basic_patterns:
        match = re.search(pattern, folder_name, re.IGNORECASE)
        if match:
            size_info = match.group(1)
            # 如果没有单位，默认添加cm
            if not re.search(r'(cm|inch)', size_info, re.IGNORECASE):
                size_info += "cm"

            # 特殊处理复杂格式：A-15.8in/40cm*11.8in/30cm -> 40x30cm
            if 'in/' in size_info and 'cm*' in size_info:
                size_info = parse_complex_size_format(size_info)

            logging.info(f"🔍 [文件夹尺寸] 从文件夹名提取尺码(基础模式): '{folder_name}' -> '{size_info}'")
            return size_info

    # 特殊处理：尝试解析复杂的属性集格式
    complex_size = parse_complex_size_format(folder_name)
    if complex_size:
        logging.info(f"🔍 [文件夹尺寸] 复杂格式解析: '{folder_name}' -> '{complex_size}'")
        return complex_size

    logging.debug(f"🔍 [文件夹尺寸] 无法从文件夹名提取尺码: '{folder_name}'")
    return None

def parse_complex_size_format(text):
    """
    解析复杂的尺寸格式，如：A-15.8in/40cm*11.8in/30cm -> 40x30cm

    Args:
        text: 包含复杂尺寸格式的文本

    Returns:
        str: 标准化的尺寸格式，如果无法解析返回None
    """
    if not text:
        return None

    # 匹配格式：15.8in/40cm*11.8in/30cm
    pattern = r'(\d+\.?\d*)in/(\d+\.?\d*)cm\*(\d+\.?\d*)in/(\d+\.?\d*)cm'
    match = re.search(pattern, text)

    if match:
        # 提取厘米值：40cm 和 30cm
        width_cm = match.group(2)
        height_cm = match.group(4)
        result = f"{width_cm}x{height_cm}cm"
        logging.info(f"🔍 [复杂格式] 解析: '{text}' -> '{result}'")
        return result

    # 匹配格式：40cm*30cm
    pattern2 = r'(\d+\.?\d*)cm\*(\d+\.?\d*)cm'
    match2 = re.search(pattern2, text)

    if match2:
        width_cm = match2.group(1)
        height_cm = match2.group(2)
        result = f"{width_cm}x{height_cm}cm"
        logging.info(f"🔍 [复杂格式] 解析简单格式: '{text}' -> '{result}'")
        return result

    return None

def calculate_size_similarity(size1, size2):
    """
    计算两个尺码的相似度

    Args:
        size1: 第一个尺码（如 "30x40cm"）
        size2: 第二个尺码（如 "40x60cm"）

    Returns:
        int: 相似度分数，10为完全匹配，0为完全不匹配
    """
    if not size1 or not size2:
        return 0

    # 标准化两个尺码
    normalized_size1 = normalize_size_format(size1)
    normalized_size2 = normalize_size_format(size2)

    logging.info(f"尺码相似度比较: '{size1}' -> '{normalized_size1}' vs '{size2}' -> '{normalized_size2}'")

    # 完全匹配
    if normalized_size1 == normalized_size2:
        return 10

    # 提取数字进行比较
    numbers1 = re.findall(r'\d+', normalized_size1)
    numbers2 = re.findall(r'\d+', normalized_size2)

    if len(numbers1) >= 2 and len(numbers2) >= 2:
        # 转换为整数并排序
        dims1 = sorted([int(x) for x in numbers1[:2]])
        dims2 = sorted([int(x) for x in numbers2[:2]])

        # 计算差异
        diff1 = abs(dims1[0] - dims2[0])
        diff2 = abs(dims1[1] - dims2[1])
        total_diff = diff1 + diff2

        # 根据差异计算分数
        if total_diff == 0:
            return 10  # 完全匹配
        elif total_diff <= 5:
            return 8   # 非常接近
        elif total_diff <= 10:
            return 6   # 比较接近
        elif total_diff <= 20:
            return 4   # 有些接近
        else:
            return 1   # 差异较大

    return 0

def find_folders_with_skc(target_base_folder, skc_base):
    """
    查找目标基础文件夹中所有包含指定SKC的文件夹

    Args:
        target_base_folder: 目标基础文件夹路径
        skc_base: SKC编号

    Returns:
        list: 包含SKC的文件夹路径列表
    """
    matching_folders = []

    if not os.path.exists(target_base_folder):
        return matching_folders

    try:
        for folder_name in os.listdir(target_base_folder):
            folder_path = os.path.join(target_base_folder, folder_name)
            if os.path.isdir(folder_path):
                # 检查文件夹内是否有对应SKC的文件
                if folder_contains_skc(folder_path, skc_base):
                    matching_folders.append(folder_path)
                    logging.info(f"找到包含SKC {skc_base} 的文件夹: {folder_name}")
    except Exception as e:
        logging.error(f"查找SKC文件夹时出错: {e}")

    return matching_folders

def diagnose_unmatched_small_pdfs(small_pdf_folder, matched_count, total_count):
    """
    诊断未匹配的小标PDF文件

    Args:
        small_pdf_folder: 小标PDF文件夹路径
        matched_count: 已匹配的文件数量
        total_count: 总文件数量
    """
    if not os.path.exists(small_pdf_folder):
        return

    unmatched_count = total_count - matched_count
    logging.warning(f"📊 小标PDF匹配统计:")
    logging.warning(f"   总文件数: {total_count}")
    logging.warning(f"   已匹配: {matched_count}")
    logging.warning(f"   未匹配: {unmatched_count}")

    if unmatched_count > 0:
        logging.warning(f"🔍 分析未匹配文件的原因:")

        # 分析所有PDF文件
        pdf_files = [f for f in os.listdir(small_pdf_folder) if f.lower().endswith('.pdf')]

        no_size_info = []
        invalid_format = []

        for filename in pdf_files[:15]:  # 分析前15个文件作为示例
            pdf_path = os.path.join(small_pdf_folder, filename)

            # 检查文件名格式（使用更宽松的检查）
            has_skc = bool(re.search(r'\d{9,11}', filename))
            if not has_skc:
                invalid_format.append(filename)
                continue

            # 尝试提取尺码信息
            extracted_size = extract_size_from_pdf_file(pdf_path)
            if not extracted_size:
                no_size_info.append(filename)
            else:
                standardized = parse_and_standardize_size(extracted_size)
                logging.warning(f"   {filename}: '{extracted_size}' -> '{standardized}'")

        if invalid_format:
            logging.warning(f"   文件名格式不符: {invalid_format}")

        if no_size_info:
            logging.warning(f"   无法提取尺码: {no_size_info}")

def rename_small_pdf_to_skc_format(file_path, skc_base):
    """
    将小标PDF文件重命名为SKC.pdf格式，去掉页数
    如果目标文件已存在，则使用带后缀的命名方式

    Args:
        file_path: 当前文件的完整路径
        skc_base: SKC编号

    Returns:
        str: 新文件名，如果重命名失败返回None
    """
    if not os.path.exists(file_path):
        logging.error(f"重命名失败: 文件不存在 {file_path}")
        return None

    # 获取文件所在目录和当前文件名
    directory = os.path.dirname(file_path)
    current_filename = os.path.basename(file_path)

    # 生成新文件名：SKC.pdf
    new_filename = f"{skc_base}.pdf"
    new_file_path = os.path.join(directory, new_filename)

    # 如果新文件名与当前文件名相同，不需要重命名
    if current_filename == new_filename:
        logging.info(f"文件名已是标准格式，无需重命名: {current_filename}")
        return new_filename

    # 检查目标文件是否已存在
    if os.path.exists(new_file_path):
        # 如果目标文件已存在，生成带后缀的文件名
        counter = 2
        while True:
            alt_filename = f"{skc_base}_{counter}.pdf"
            alt_file_path = os.path.join(directory, alt_filename)
            if not os.path.exists(alt_file_path):
                new_filename = alt_filename
                new_file_path = alt_file_path
                logging.info(f"目标文件已存在，使用备选文件名: {new_filename}")
                break
            counter += 1
            if counter > 10:  # 防止无限循环
                logging.error(f"无法找到合适的文件名，跳过重命名: {current_filename}")
                return None

    try:
        # 执行重命名
        os.rename(file_path, new_file_path)
        logging.info(f"重命名成功: {current_filename} -> {new_filename}")
        return new_filename
    except Exception as e:
        logging.error(f"重命名失败: {current_filename} -> {new_filename}, 错误: {e}")
        return None

def rename_small_pdf_to_sku_format(file_path, sku_hao):
    """
    将小标PDF文件重命名为SKU货号.pdf格式，去掉页数
    如果目标文件已存在，则使用带后缀的命名方式

    Args:
        file_path: 当前文件的完整路径
        sku_hao: SKU货号

    Returns:
        str: 新文件名，如果重命名失败返回None
    """
    if not os.path.exists(file_path):
        logging.error(f"重命名失败: 文件不存在 {file_path}")
        return None

    # 获取文件所在目录和当前文件名
    directory = os.path.dirname(file_path)
    current_filename = os.path.basename(file_path)

    # 生成新文件名：SKU货号.pdf
    new_filename = f"{sku_hao}.pdf"
    new_file_path = os.path.join(directory, new_filename)

    # 如果新文件名与当前文件名相同，不需要重命名
    if current_filename == new_filename:
        logging.info(f"文件名已是标准格式，无需重命名: {current_filename}")
        return new_filename

    # 检查目标文件是否已存在
    if os.path.exists(new_file_path):
        # 如果目标文件已存在，生成带后缀的文件名
        counter = 2
        while True:
            alt_filename = f"{sku_hao}_{counter}.pdf"
            alt_file_path = os.path.join(directory, alt_filename)
            if not os.path.exists(alt_file_path):
                new_filename = alt_filename
                new_file_path = alt_file_path
                logging.info(f"目标文件已存在，使用备选文件名: {new_filename}")
                break
            counter += 1
            if counter > 10:  # 防止无限循环
                logging.error(f"无法找到合适的文件名，跳过重命名: {current_filename}")
                return None

    try:
        # 执行重命名
        os.rename(file_path, new_file_path)
        logging.info(f"重命名成功: {current_filename} -> {new_filename}")
        return new_filename
    except Exception as e:
        logging.error(f"重命名失败: {current_filename} -> {new_filename}, 错误: {e}")
        return None

def rename_all_small_pdfs_in_target_folders(target_base_folder):
    """
    遍历所有目标文件夹，将小标PDF文件重命名为SKC.pdf格式

    Args:
        target_base_folder: 目标基础文件夹路径
    """
    logging.info(f"📝 [重命名] 开始执行重命名函数")
    logging.info(f"📝 [重命名] 目标基础文件夹: {target_base_folder}")

    if not os.path.exists(target_base_folder):
        logging.warning(f"📝 [重命名] 目标文件夹不存在: {target_base_folder}")
        return

    total_renamed = 0
    total_processed = 0
    folders_found = 0

    logging.info(f"📝 [重命名] 扫描目标文件夹: {target_base_folder}")

    try:
        # 列出所有子文件夹
        all_items = os.listdir(target_base_folder)
        logging.info(f"📝 [重命名] 找到 {len(all_items)} 个项目")

        # 遍历所有子文件夹
        for folder_name in all_items:
            folder_path = os.path.join(target_base_folder, folder_name)
            if not os.path.isdir(folder_path):
                logging.info(f"📝 [重命名] 跳过非文件夹: {folder_name}")
                continue

            folders_found += 1
            logging.info(f"📝 [重命名] 检查文件夹: {folder_name}")

            # 查找小标子文件夹
            small_label_folder = os.path.join(folder_path, "小标")
            if not os.path.exists(small_label_folder):
                logging.info(f"📝 [重命名] 没有小标子文件夹: {folder_name}")
                continue

            logging.info(f"📝 [重命名] 处理文件夹: {folder_name}")

            # 列出小标文件夹中的文件
            small_label_files = os.listdir(small_label_folder)
            logging.info(f"📝 [重命名] 小标文件夹中有 {len(small_label_files)} 个文件: {small_label_files}")

            # 处理小标文件夹中的PDF文件
            for filename in small_label_files:
                if not filename.lower().endswith('.pdf'):
                    logging.info(f"📝 [重命名] 跳过非PDF文件: {filename}")
                    continue

                total_processed += 1
                file_path = os.path.join(small_label_folder, filename)
                logging.info(f"📝 [重命名] 处理PDF文件: {filename}")

                # 提取SKC编号
                skc_base = extract_skc_from_filename(filename)
                logging.info(f"📝 [重命名] 提取SKC: {filename} -> {skc_base}")

                if skc_base:
                    new_filename = rename_small_pdf_to_skc_format(file_path, skc_base)
                    if new_filename and new_filename != filename:
                        total_renamed += 1
                        logging.info(f"📝 [重命名] 重命名成功: {filename} -> {new_filename}")
                    elif new_filename == filename:
                        logging.info(f"📝 [重命名] 已是标准格式: {filename}")
                    else:
                        logging.warning(f"📝 [重命名] 重命名失败: {filename}")
                else:
                    logging.warning(f"📝 [重命名] 无法提取SKC: {filename}")

    except Exception as e:
        logging.error(f"📝 [重命名] 重命名过程中出错: {e}")
        import traceback
        logging.error(f"📝 [重命名] 错误详情: {traceback.format_exc()}")

    logging.info(f"📝 [重命名] 重命名完成统计:")
    logging.info(f"📝 [重命名] - 扫描文件夹数: {folders_found}")
    logging.info(f"📝 [重命名] - 处理PDF文件数: {total_processed}")
    logging.info(f"📝 [重命名] - 重命名成功数: {total_renamed}")

    return total_processed, total_renamed

def simple_rename_small_pdfs(target_base_folder):
    """
    简单直接的重命名函数：遍历所有小标文件夹，去掉PDF文件名中的-页数
    支持多层文件夹结构：属性集文件夹/数量文件夹/小标文件夹
    """
    logging.info(f"📝 [重命名] 开始执行简单重命名函数")
    logging.info(f"📝 [重命名] 目标基础文件夹: {target_base_folder}")

    if not os.path.exists(target_base_folder):
        logging.warning(f"📝 [重命名] 目标文件夹不存在: {target_base_folder}")
        return

    renamed_count = 0
    total_pdf_files = 0
    small_label_folders_found = 0

    # 使用os.walk递归遍历所有子文件夹，查找所有小标文件夹
    for root, dirs, files in os.walk(target_base_folder):
        # 检查当前文件夹是否是小标文件夹
        if os.path.basename(root) == "小标":
            small_label_folders_found += 1
            logging.info(f"📝 [重命名] 找到小标文件夹: {root}")

            # 列出小标文件夹中的PDF文件
            pdf_files = [f for f in files if f.endswith('.pdf')]
            logging.info(f"📝 [重命名] 小标文件夹中有 {len(pdf_files)} 个PDF文件: {pdf_files}")

            # 处理每个PDF文件
            for filename in pdf_files:
                total_pdf_files += 1
                logging.info(f"📝 [重命名] 处理PDF文件: {filename}")

                # 检查是否包含-页数格式
                if '-' in filename:
                    logging.info(f"📝 [重命名] 文件包含页数，需要重命名: {filename}")

                    # 提取SKC部分（去掉-页数.pdf）
                    parts = filename.split('-')
                    if len(parts) >= 2 and parts[0].isdigit():
                        skc = parts[0]
                        new_filename = f"{skc}.pdf"

                        old_path = os.path.join(root, filename)
                        new_path = os.path.join(root, new_filename)

                        logging.info(f"📝 [重命名] 计划重命名: {filename} -> {new_filename}")

                        # 如果新文件名已存在，跳过
                        if os.path.exists(new_path):
                            logging.info(f"📝 [重命名] 跳过重命名（目标文件已存在）: {filename}")
                            continue

                        try:
                            os.rename(old_path, new_path)
                            logging.info(f"📝 [重命名] 重命名成功: {filename} -> {new_filename}")
                            renamed_count += 1
                        except Exception as e:
                            logging.error(f"📝 [重命名] 重命名失败: {filename} -> {new_filename}, 错误: {e}")
                    else:
                        logging.info(f"📝 [重命名] 文件格式不符合SKC-页数格式: {filename}")
                else:
                    logging.info(f"📝 [重命名] 文件已是标准格式（无页数）: {filename}")

    logging.info(f"📝 [重命名] 重命名统计:")
    logging.info(f"📝 [重命名] - 找到小标文件夹数: {small_label_folders_found}")
    logging.info(f"📝 [重命名] - 总PDF文件数: {total_pdf_files}")
    logging.info(f"📝 [重命名] - 重命名文件数: {renamed_count}")
    logging.info(f"📝 简单重命名完成，共重命名了 {renamed_count} 个文件")

def smart_rename_small_pdfs_based_on_images(target_base_folder, df):
    """
    智能重命名小标PDF文件：根据图片命名方式决定小标PDF的命名方式
    - 如果图片命名为SKC，小标命名为SKC
    - 如果图片命名为SKU货号，小标命名为SKU货号

    Args:
        target_base_folder: 目标基础文件夹路径
        df: 拣货单DataFrame，用于SKC-SKU映射
    """
    logging.info(f"📝 [智能重命名] 开始执行智能重命名函数")
    logging.info(f"📝 [智能重命名] 目标基础文件夹: {target_base_folder}")

    if not os.path.exists(target_base_folder):
        logging.warning(f"📝 [智能重命名] 目标文件夹不存在: {target_base_folder}")
        return

    if df is None or df.empty:
        logging.warning(f"📝 [智能重命名] 拣货单数据为空，使用简单重命名")
        simple_rename_small_pdfs(target_base_folder)
        return

    # 建立SKC-SKU映射关系
    skc_to_sku_list, sku_to_skc, skc_to_attribute_sets, sku_to_attribute_sets = build_skc_sku_mapping(df)

    renamed_count = 0
    total_pdf_files = 0
    small_label_folders_found = 0

    # 使用os.walk递归遍历所有子文件夹，查找所有小标文件夹
    for root, dirs, files in os.walk(target_base_folder):
        # 检查当前文件夹是否是小标文件夹
        if os.path.basename(root) == "小标":
            small_label_folders_found += 1
            logging.info(f"📝 [智能重命名] 找到小标文件夹: {root}")

            # 获取父文件夹路径，用于分析图片命名方式
            parent_folder = os.path.dirname(root)

            # 分析该文件夹中的图片命名方式
            image_naming_type = analyze_image_naming_in_folder(parent_folder)
            logging.info(f"📝 [智能重命名] 文件夹 {parent_folder} 的图片命名方式: {image_naming_type}")

            # 列出小标文件夹中的PDF文件
            pdf_files = [f for f in files if f.endswith('.pdf')]
            logging.info(f"📝 [智能重命名] 小标文件夹中有 {len(pdf_files)} 个PDF文件: {pdf_files}")

            # 处理每个PDF文件
            for filename in pdf_files:
                total_pdf_files += 1
                logging.info(f"📝 [智能重命名] 处理PDF文件: {filename}")

                # 根据图片命名方式决定重命名策略
                if image_naming_type == "SKU":
                    # 图片命名为SKU货号，检查小标是否已经是SKU货号格式
                    if re.search(r'[a-zA-Z]', filename.replace('.pdf', '')):
                        # 文件名包含字母，已经是SKU货号格式，跳过重命名
                        logging.info(f"📝 [智能重命名] 文件已是SKU货号格式，跳过重命名: {filename}")
                    else:
                        # 小标也应该命名为SKU货号
                        renamed = rename_small_pdf_to_sku_based_on_context(root, filename, parent_folder, skc_to_sku_list, sku_to_skc)
                        if renamed:
                            renamed_count += 1
                else:
                    # 图片命名为SKC或其他，小标命名为SKC（默认行为）
                    renamed = rename_small_pdf_to_skc_based_on_context(root, filename)
                    if renamed:
                        renamed_count += 1

    logging.info(f"📝 [智能重命名] 重命名统计:")
    logging.info(f"📝 [智能重命名] - 找到小标文件夹数: {small_label_folders_found}")
    logging.info(f"📝 [智能重命名] - 总PDF文件数: {total_pdf_files}")
    logging.info(f"📝 [智能重命名] - 重命名文件数: {renamed_count}")
    logging.info(f"📝 智能重命名完成，共重命名了 {renamed_count} 个文件")

def analyze_image_naming_in_folder(folder_path):
    """
    分析文件夹中图片的命名方式

    Args:
        folder_path: 文件夹路径

    Returns:
        str: "SKC" 或 "SKU" 或 "MIXED" 或 "UNKNOWN"
    """
    if not os.path.exists(folder_path):
        return "UNKNOWN"

    skc_count = 0
    sku_count = 0

    # 遍历文件夹中的图片文件
    for filename in os.listdir(folder_path):
        if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
            # 检查是否是SKC命名（9-11位数字开头）
            if re.match(r'^\d{9,11}', filename):
                skc_count += 1
            # 检查是否是SKU货号命名（包含字母和数字的组合）
            elif re.match(r'^[a-zA-Z0-9]+', filename) and not re.match(r'^\d{9,11}', filename):
                sku_count += 1

    logging.info(f"📝 [命名分析] 文件夹 {folder_path}: SKC命名图片 {skc_count} 张, SKU命名图片 {sku_count} 张")

    if skc_count > sku_count:
        return "SKC"
    elif sku_count > skc_count:
        return "SKU"
    elif skc_count > 0 and sku_count > 0:
        return "MIXED"
    else:
        return "UNKNOWN"

def rename_small_pdf_to_sku_based_on_context(small_label_folder, filename, parent_folder, skc_to_sku_list, sku_to_skc):
    """
    根据上下文将小标PDF重命名为SKU货号格式

    Args:
        small_label_folder: 小标文件夹路径
        filename: PDF文件名
        parent_folder: 父文件夹路径
        skc_to_sku_list: SKC到SKU列表的映射
        sku_to_skc: SKU到SKC的映射

    Returns:
        bool: 是否成功重命名
    """
    file_path = os.path.join(small_label_folder, filename)

    # 从文件名提取数字（可能是SKC或SKU ID）
    extracted_number = extract_skc_from_filename(filename)
    if not extracted_number:
        logging.warning(f"📝 [SKU重命名] 无法从文件名提取数字: {filename}")
        return False

    # 首先尝试作为SKC查找
    if extracted_number in skc_to_sku_list:
        sku_list = skc_to_sku_list[extracted_number]
        if sku_list:
            # 使用第一个SKU货号进行重命名
            sku_hao = sku_list[0]
            logging.info(f"📝 [SKU重命名] SKC {extracted_number} 对应SKU货号: {sku_hao}")

            new_filename = rename_small_pdf_to_sku_format(file_path, sku_hao)
            if new_filename and new_filename != filename:
                logging.info(f"📝 [SKU重命名] 重命名成功: {filename} -> {new_filename}")
                return True
            elif new_filename == filename:
                logging.info(f"📝 [SKU重命名] 已是标准格式: {filename}")
                return False
            else:
                logging.warning(f"📝 [SKU重命名] 重命名失败: {filename}")
                return False
        else:
            logging.warning(f"📝 [SKU重命名] SKC {extracted_number} 没有对应的SKU货号")
            return False
    else:
        # 如果不是SKC，尝试作为SKU ID查找对应的SKU货号
        logging.info(f"📝 [SKU重命名] {extracted_number} 不是SKC，尝试作为SKU ID查找...")

        # 遍历所有SKC的SKU列表，查找匹配的SKU ID
        for skc, sku_list in skc_to_sku_list.items():
            for i in range(1, len(sku_list), 2):  # SKU ID在奇数位置
                if i < len(sku_list) and sku_list[i] == extracted_number:
                    # 找到匹配的SKU ID，获取对应的SKU货号
                    sku_hao = sku_list[i-1]  # SKU货号在前一个位置
                    logging.info(f"📝 [SKU重命名] SKU ID {extracted_number} 对应SKU货号: {sku_hao}")

                    new_filename = rename_small_pdf_to_sku_format(file_path, sku_hao)
                    if new_filename and new_filename != filename:
                        logging.info(f"📝 [SKU重命名] 重命名成功: {filename} -> {new_filename}")
                        return True
                    elif new_filename == filename:
                        logging.info(f"📝 [SKU重命名] 已是标准格式: {filename}")
                        return False
                    else:
                        logging.warning(f"📝 [SKU重命名] 重命名失败: {filename}")
                        return False

        logging.warning(f"📝 [SKU重命名] 未找到 {extracted_number} 对应的SKC或SKU ID映射")
        return False

def rename_small_pdf_to_skc_based_on_context(small_label_folder, filename):
    """
    根据上下文将小标PDF重命名为SKC格式

    Args:
        small_label_folder: 小标文件夹路径
        filename: PDF文件名

    Returns:
        bool: 是否成功重命名
    """
    file_path = os.path.join(small_label_folder, filename)

    # 检查是否包含-页数格式
    if '-' in filename:
        logging.info(f"📝 [SKC重命名] 文件包含页数，需要重命名: {filename}")

        # 提取SKC部分（去掉-页数.pdf）
        parts = filename.split('-')
        if len(parts) >= 2 and parts[0].isdigit():
            skc = parts[0]
            new_filename = f"{skc}.pdf"

            old_path = file_path
            new_path = os.path.join(small_label_folder, new_filename)

            logging.info(f"📝 [SKC重命名] 计划重命名: {filename} -> {new_filename}")

            # 如果新文件名已存在，跳过
            if os.path.exists(new_path):
                logging.info(f"📝 [SKC重命名] 跳过重命名（目标文件已存在）: {filename}")
                return False

            try:
                os.rename(old_path, new_path)
                logging.info(f"📝 [SKC重命名] 重命名成功: {filename} -> {new_filename}")
                return True
            except Exception as e:
                logging.error(f"📝 [SKC重命名] 重命名失败: {filename} -> {new_filename}, 错误: {e}")
                return False
        else:
            logging.info(f"📝 [SKC重命名] 文件格式不符合SKC-页数格式: {filename}")
            return False
    else:
        logging.info(f"📝 [SKC重命名] 文件已是标准格式（无页数）: {filename}")
        return False

def extract_skc_from_filename(filename):
    """
    从文件名中提取SKC编号

    Args:
        filename: 文件名

    Returns:
        str: SKC编号，如果提取失败返回None
    """
    # 去掉文件扩展名
    name_without_ext = filename.rsplit('.', 1)[0]

    # 尝试各种格式
    patterns = [
        r'^(\d{9,11})-\d+$',  # SKC-页数格式
        r'^(\d{9,11})$',      # 纯SKC格式
        r'(\d{9,11})',        # 包含SKC的任意格式
    ]

    for pattern in patterns:
        match = re.search(pattern, name_without_ext)
        if match:
            return match.group(1)

    return None

def calculate_attribute_match_score(cleaned_suffix, attribute_set, target_base_folder):
    """
    计算文件后缀与属性集的匹配分数

    Args:
        cleaned_suffix: 清理后的文件后缀（尺码信息）
        attribute_set: 当前处理的属性集
        target_base_folder: 目标基础文件夹路径

    Returns:
        int: 匹配分数，分数越高匹配度越好
    """
    if not cleaned_suffix:
        return 1

    logging.info(f"🔍 属性集匹配: 文件后缀='{cleaned_suffix}', 当前属性集='{attribute_set}'")

    # 清理当前属性集
    cleaned_attribute_set = clean_attribute_set(attribute_set)

    # 方法1：直接匹配当前属性集
    if cleaned_suffix == cleaned_attribute_set:
        logging.info(f"✅ 直接匹配当前属性集: '{cleaned_suffix}' == '{cleaned_attribute_set}'")
        return 10

    if cleaned_suffix in cleaned_attribute_set or cleaned_attribute_set in cleaned_suffix:
        logging.info(f"✅ 包含匹配当前属性集: '{cleaned_suffix}' <-> '{cleaned_attribute_set}'")
        return 8

    # 方法2：通过映射表格匹配
    # 查找文件后缀在映射表格A列中的匹配
    for original_attr, target_attr in _template_mapping.items():
        cleaned_original = clean_attribute_set(original_attr)
        cleaned_target = clean_attribute_set(target_attr)

        # 文件后缀匹配映射表A列
        if cleaned_suffix == cleaned_original:
            logging.info(f"✅ 文件后缀匹配映射表A列: '{cleaned_suffix}' == '{cleaned_original}' -> '{target_attr}'")
            # 检查映射结果是否与当前属性集匹配
            if cleaned_target == cleaned_attribute_set:
                logging.info(f"✅ 映射结果匹配当前属性集: '{cleaned_target}' == '{cleaned_attribute_set}'")
                return 10  # 完美匹配
            else:
                # 检查映射结果的尺寸是否与当前属性集的尺寸匹配
                if check_size_compatibility(target_attr, attribute_set):
                    logging.info(f"✅ 映射结果尺寸兼容当前属性集")
                    return 9
                # 检查是否存在对应的文件夹
                elif check_folder_exists_for_attribute(target_attr, target_base_folder):
                    logging.info(f"✅ 映射结果对应文件夹存在: '{target_attr}'")
                    return 7
                else:
                    logging.info(f"⚠️ 映射结果对应文件夹不存在: '{target_attr}'")
                    return 3

        # 文件后缀包含在映射表A列中
        if cleaned_suffix in cleaned_original or cleaned_original in cleaned_suffix:
            logging.info(f"✅ 文件后缀包含匹配映射表A列: '{cleaned_suffix}' <-> '{cleaned_original}' -> '{target_attr}'")
            if cleaned_target == cleaned_attribute_set:
                return 8
            elif check_size_compatibility(target_attr, attribute_set):
                return 7
            elif check_folder_exists_for_attribute(target_attr, target_base_folder):
                return 5
            else:
                return 2

    # 方法3：部分匹配
    suffix_parts = cleaned_suffix.lower().split()
    attr_parts = cleaned_attribute_set.lower().split()
    common_parts = set(suffix_parts) & set(attr_parts)

    if common_parts:
        logging.info(f"✅ 部分匹配: 共同部分={common_parts}")
        return 4

    logging.info(f"❌ 无匹配: '{cleaned_suffix}' 与 '{cleaned_attribute_set}' 不匹配")
    return 1

def check_folder_exists_for_attribute(target_attribute, target_base_folder):
    """
    检查目标属性集对应的文件夹是否存在

    Args:
        target_attribute: 目标属性集
        target_base_folder: 目标基础文件夹路径

    Returns:
        bool: 文件夹是否存在
    """
    if not os.path.exists(target_base_folder):
        return False

    # 生成可能的文件夹名称
    possible_folder_names = []

    # 使用get_template_mapping获取映射后的属性集
    mapped_attribute = get_template_mapping(target_attribute)
    if mapped_attribute and mapped_attribute != "unknown":
        possible_folder_names.append(mapped_attribute)

    # 也检查原始属性集
    possible_folder_names.append(target_attribute)

    # 检查文件夹是否存在
    for folder_name in os.listdir(target_base_folder):
        folder_path = os.path.join(target_base_folder, folder_name)
        if os.path.isdir(folder_path):
            for possible_name in possible_folder_names:
                if possible_name.lower() in folder_name.lower():
                    logging.info(f"✅ 找到对应文件夹: '{folder_name}' (匹配: '{possible_name}')")
                    return True

    return False

def check_size_compatibility(target_attr, current_attr):
    """
    检查两个属性集的尺寸是否兼容

    Args:
        target_attr: 目标属性集
        current_attr: 当前属性集

    Returns:
        bool: 尺寸是否兼容
    """
    try:
        # 从两个属性集中提取尺寸信息
        target_size = extract_size_from_attribute(target_attr)
        current_size = extract_size_from_attribute(current_attr)

        if target_size and current_size:
            # 比较尺寸是否相同
            if target_size == current_size:
                logging.info(f"✅ 尺寸完全匹配: {target_size}")
                return True

            # 检查是否是相同尺寸的不同表示方式
            if normalize_size_format(target_size) == normalize_size_format(current_size):
                logging.info(f"✅ 尺寸标准化后匹配: {target_size} <-> {current_size}")
                return True

        return False
    except Exception as e:
        logging.error(f"检查尺寸兼容性时出错: {e}")
        return False

def extract_size_from_attribute(attribute):
    """
    从属性集中提取尺寸信息

    Args:
        attribute: 属性集字符串

    Returns:
        str: 提取的尺寸信息，如果没有找到返回None
    """
    if not attribute:
        return None

    # 匹配各种尺寸格式
    size_patterns = [
        r'(\d+x\d+cm)',           # 30x40cm
        r'(\d+\.\d+x\d+\.\d+inch)', # 11.8x15.7inch
        r'(\d+x\d+inch)',         # 12x16inch
        r'(\d+x\d+)',             # 30x40 (无单位)
    ]

    for pattern in size_patterns:
        match = re.search(pattern, attribute, re.IGNORECASE)
        if match:
            return match.group(1)

    return None

def normalize_size_format(size_str):
    """
    标准化尺寸格式，便于比较

    Args:
        size_str: 尺寸字符串

    Returns:
        str: 标准化后的尺寸格式
    """
    if not size_str:
        return ""

    # 提取数字部分
    numbers = re.findall(r'\d+(?:\.\d+)?', size_str)
    if len(numbers) >= 2:
        # 转换为厘米并排序（确保比较时顺序一致）
        width = float(numbers[0])
        height = float(numbers[1])

        # 如果是英寸，转换为厘米
        if 'inch' in size_str.lower():
            width = round(width * 2.54)
            height = round(height * 2.54)

        # 排序确保一致性（小的在前）
        sizes = sorted([int(width), int(height)])
        return f"{sizes[0]}x{sizes[1]}cm"

    return size_str.lower()

def calculate_size_match_score_with_mapping(file_size_cleaned, attribute_set):
    """
    使用映射表格匹配尺寸信息

    Args:
        file_size_cleaned: 清理后的文件尺寸信息
        attribute_set: 属性集信息

    Returns:
        int: 匹配分数，分数越高匹配度越好
    """
    if not file_size_cleaned:
        return 1

    logging.info(f"🔍 尺寸匹配: 文件尺寸='{file_size_cleaned}', 属性集='{attribute_set}'")

    # 清理属性集
    cleaned_attribute_set = clean_attribute_set(attribute_set)

    # 在映射表格中查找匹配
    # 检查文件尺寸是否在映射表格的A列中
    for original_attr, target_attr in _template_mapping.items():
        cleaned_original = clean_attribute_set(original_attr)

        # 完全匹配：文件尺寸 == 映射表A列
        if file_size_cleaned == cleaned_original:
            logging.info(f"✅ 完全匹配映射表A列: '{file_size_cleaned}' == '{cleaned_original}' -> '{target_attr}'")
            # 检查目标属性集是否匹配当前属性集
            if cleaned_attribute_set == clean_attribute_set(target_attr):
                return 10  # 完美匹配
            else:
                return 8   # 尺寸匹配但属性集不匹配

        # 包含匹配：文件尺寸包含在映射表A列中，或反之
        if (file_size_cleaned in cleaned_original or cleaned_original in file_size_cleaned):
            logging.info(f"✅ 包含匹配映射表A列: '{file_size_cleaned}' <-> '{cleaned_original}' -> '{target_attr}'")
            if cleaned_attribute_set == clean_attribute_set(target_attr):
                return 7   # 包含匹配且属性集匹配
            else:
                return 5   # 包含匹配但属性集不匹配

    # 直接检查文件尺寸是否包含在当前属性集中
    if file_size_cleaned in cleaned_attribute_set or cleaned_attribute_set in file_size_cleaned:
        logging.info(f"✅ 直接匹配属性集: '{file_size_cleaned}' <-> '{cleaned_attribute_set}'")
        return 6

    # 模糊匹配：检查是否有相似的部分
    file_parts = file_size_cleaned.lower().split()
    attr_parts = cleaned_attribute_set.lower().split()

    common_parts = set(file_parts) & set(attr_parts)
    if common_parts:
        logging.info(f"✅ 部分匹配: 共同部分={common_parts}")
        return 3

    logging.info(f"❌ 无匹配: '{file_size_cleaned}' 与 '{cleaned_attribute_set}' 不匹配")
    return 1


def build_skc_sku_mapping(df):
    """
    从拣货单数据中建立SKC和SKU货号的映射关系
    处理拣货单中同一SKC跨多行的情况（第一行有商品信息，后续行可能为空）

    Args:
        df: 拣货单DataFrame，包含商品信息、SKU ID、SKU货号等列

    Returns:
        dict: SKC到SKU货号列表的映射字典，以及SKU货号到SKC的反向映射
    """
    skc_to_sku_list = {}  # SKC -> SKU货号列表
    sku_to_skc = {}  # SKU货号 -> SKC
    skc_to_attribute_sets = {}  # SKC -> 属性集列表
    sku_to_attribute_sets = {}  # SKU货号 -> 属性集列表

    if df is None or df.empty:
        return skc_to_sku_list, sku_to_skc, skc_to_attribute_sets, sku_to_attribute_sets

    logging.info(f"🔗 开始建立SKC-SKU映射关系，总共{len(df)}行数据")

    current_skc = None  # 记录当前处理的SKC

    for index, row in df.iterrows():
        product_info = row.get('商品信息', '')
        sku_id = row.get('SKU ID', '')
        sku_hao = row.get('SKU货号', '')
        attribute_set = row.get('属性集', '')

        logging.info(f"🔗 处理第{index+1}行: 商品信息='{product_info}', SKU货号='{sku_hao}', 属性集='{attribute_set}'")

        # 尝试从当前行提取SKC编号
        extracted_skc = None
        if isinstance(product_info, str) and product_info.strip():
            skc_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
            if skc_match:
                extracted_skc = skc_match.group(1)
                current_skc = extracted_skc  # 更新当前SKC
                logging.info(f"🔗 第{index+1}行: 提取到新SKC={extracted_skc}")

        # 如果当前行没有SKC但有SKU货号，使用上一行的SKC
        if not extracted_skc and current_skc and (sku_hao or sku_id):
            extracted_skc = current_skc
            logging.info(f"🔗 第{index+1}行: 使用上一行的SKC={extracted_skc}")

        # 如果有SKC（无论是当前行提取的还是继承的），处理映射
        if extracted_skc:
            skc = extracted_skc

            # 建立SKC到SKU列表的映射
            # 同时保存SKU货号和SKU ID，因为小标PDF可能以任一种方式命名
            sku_values = []

            # 添加SKU货号
            if isinstance(sku_hao, str) and sku_hao.strip():
                sku_hao_clean = str(sku_hao).strip().replace('\n', '').replace('\r', '').replace('\t', ' ')
                sku_hao_clean = ' '.join(sku_hao_clean.split())
                if sku_hao_clean:
                    sku_values.append(sku_hao_clean)
                    logging.info(f"🔗 第{index+1}行: 添加SKU货号={sku_hao_clean}")
            elif sku_hao and str(sku_hao).strip():  # 处理数字类型的SKU货号
                sku_hao_clean = str(sku_hao).strip()
                if sku_hao_clean:
                    sku_values.append(sku_hao_clean)
                    logging.info(f"🔗 第{index+1}行: 添加SKU货号(数字类型)={sku_hao_clean}")

            # 添加SKU ID
            if isinstance(sku_id, str) and sku_id.strip():
                sku_id_clean = str(sku_id).strip().replace('\n', '').replace('\r', '').replace('\t', ' ')
                sku_id_clean = ' '.join(sku_id_clean.split())
                if sku_id_clean:
                    sku_values.append(sku_id_clean)
                    logging.info(f"🔗 第{index+1}行: 添加SKU ID={sku_id_clean}")
            elif sku_id and str(sku_id).strip():  # 处理数字类型的SKU ID
                sku_id_clean = str(sku_id).strip()
                if sku_id_clean:
                    sku_values.append(sku_id_clean)
                    logging.info(f"🔗 第{index+1}行: 添加SKU ID(数字类型)={sku_id_clean}")

            # 将所有SKU值添加到映射中
            for sku_value_clean in sku_values:
                if skc not in skc_to_sku_list:
                    skc_to_sku_list[skc] = []
                if sku_value_clean not in skc_to_sku_list[skc]:
                    skc_to_sku_list[skc].append(sku_value_clean)
                    logging.info(f"🔗 第{index+1}行: 建立映射 SKC {skc} -> SKU {sku_value_clean}")
                sku_to_skc[sku_value_clean] = skc

            # 建立SKC到属性集的映射
            if isinstance(attribute_set, str) and attribute_set.strip():
                # 清理属性集：去除换行符、制表符和多余空格
                attribute_set_clean = str(attribute_set).strip().replace('\n', '').replace('\r', '').replace('\t', ' ')
                attribute_set_clean = ' '.join(attribute_set_clean.split())

                if attribute_set_clean:
                    if skc not in skc_to_attribute_sets:
                        skc_to_attribute_sets[skc] = []
                    if attribute_set_clean not in skc_to_attribute_sets[skc]:
                        skc_to_attribute_sets[skc].append(attribute_set_clean)
                        logging.info(f"🔗 第{index+1}行: 建立属性集映射 SKC {skc} -> 属性集 '{attribute_set_clean}'")

                    # 建立SKU值到属性集的映射
                    if sku_values:
                        for sku_value_clean in sku_values:
                            if sku_value_clean not in sku_to_attribute_sets:
                                sku_to_attribute_sets[sku_value_clean] = []
                            if attribute_set_clean not in sku_to_attribute_sets[sku_value_clean]:
                                sku_to_attribute_sets[sku_value_clean].append(attribute_set_clean)
                                logging.info(f"🔗 第{index+1}行: 建立属性集映射 SKU {sku_value_clean} -> 属性集 '{attribute_set_clean}'")
        else:
            logging.warning(f"🔗 第{index+1}行: 无法确定SKC，跳过此行")

    total_sku_mappings = sum(len(sku_list) for sku_list in skc_to_sku_list.values())
    logging.info(f"🔗 建立映射关系完成: SKC->SKU货号 {len(skc_to_sku_list)} 个SKC, 总计 {total_sku_mappings} 个SKU货号")
    logging.info(f"🔗 属性集映射: SKC->属性集 {len(skc_to_attribute_sets)} 条, SKU货号->属性集 {len(sku_to_attribute_sets)} 条")

    # 调试信息：显示所有映射关系
    logging.info(f"🔗 调试：所有SKC映射关系:")
    for skc, sku_list in skc_to_sku_list.items():
        logging.info(f"🔗 调试：  SKC {skc} -> SKU列表 {sku_list}")
        if skc in skc_to_attribute_sets:
            logging.info(f"🔗 调试：  SKC {skc} -> 属性集列表 {skc_to_attribute_sets[skc]}")

    return skc_to_sku_list, sku_to_skc, skc_to_attribute_sets, sku_to_attribute_sets

def move_files_with_suffix(source_folder, skc_base, df=None):
    """
    查找与SKC相关的图片文件，支持多种后缀格式，返回所有找到的路径列表
    现在支持SKU货号命名格式
    """
    found_files = []

    # 首先尝试查找SKC命名的图片
    if os.path.isdir(source_folder):
        for filename in os.listdir(source_folder):
            # 检查文件名是否以 skc_base 开头，且以 .jpg 结尾 (忽略大小写)
            if filename.lower().startswith(skc_base.lower()) and filename.lower().endswith('.jpg'):
                file_path = os.path.join(source_folder, filename)
                if os.path.isfile(file_path): # 确保是文件而不是文件夹
                     found_files.append(file_path)

    # 如果没有找到SKC命名的图片，尝试查找SKU货号命名的图片
    if not found_files and df is not None:
        logging.info(f"🔍 未找到SKC {skc_base} 命名的图片，尝试查找SKU货号命名的图片...")
        skc_to_sku_list, sku_to_skc, skc_to_attribute_sets, sku_to_attribute_sets = build_skc_sku_mapping(df)

        if skc_base in skc_to_sku_list:
            sku_list = skc_to_sku_list[skc_base]
            logging.info(f"🔍 找到对应SKU货号列表: {sku_list}")

            # 查找所有SKU货号命名的图片
            for sku_hao in sku_list:
                # 🔧 重要修复：使用更精确的SKU货号验证
                sku_hao_str = str(sku_hao).strip()

                # 使用统一的SKU货号验证函数
                if not is_valid_sku_hao(sku_hao_str):
                    logging.info(f"🔍 跳过无效SKU货号: {sku_hao_str} (不符合SKU货号格式)")
                    continue

                sku_images = find_images_by_sku_hao(source_folder, sku_hao)
                if sku_images:
                    logging.info(f"✅ 找到 {len(sku_images)} 张SKU货号 {sku_hao} 命名的图片")
                    found_files.extend(sku_images)
                else:
                    logging.info(f"ℹ️ 未找到SKU货号 {sku_hao} 命名的图片")

            if not found_files:
                logging.warning(f"❌ 所有有效SKU货号都未找到对应的图片")
        else:
            logging.warning(f"❌ 未找到SKC {skc_base} 对应的SKU货号")

    return found_files

def is_image_matching_attribute_set(filename, attribute_set):
    """
    检查图片文件名是否匹配当前属性集

    Args:
        filename: 图片文件名
        attribute_set: 属性集（如16inchX16inch, 20inchX20inch, 24inchX24inch）

    Returns:
        bool: 是否匹配
    """
    if not filename or not attribute_set:
        return False

    filename_lower = filename.lower()
    attribute_lower = attribute_set.lower()

    # 从属性集中提取尺寸信息
    # 例如：16inchX16inch -> 16x16, 20inchX20inch -> 20x20
    size_match = re.search(r'(\d+)inch[xX×](\d+)inch', attribute_lower)
    if size_match:
        width = size_match.group(1)
        height = size_match.group(2)
        expected_size = f"{width}x{height}"

        # 检查文件名中是否包含对应的尺寸
        # 支持多种格式：-16x16.jpg, -16X16.jpg, -16×16.jpg
        size_patterns = [
            f"-{width}x{height}",
            f"-{width}X{height}",
            f"-{width}×{height}",
            f"_{width}x{height}",
            f"_{width}X{height}",
            f"_{width}×{height}"
        ]

        for pattern in size_patterns:
            if pattern.lower() in filename_lower:
                logging.info(f"📏 尺寸匹配: {filename} 包含 {pattern} -> 属性集 {attribute_set}")
                return True

    # 如果没有明确的尺寸匹配，检查是否是传统格式的属性集匹配
    # 传统格式：通过文件名中的序号匹配属性集
    # 例如：28969221362-1.jpg 对应第1个属性集，28969221362-2.jpg 对应第2个属性集

    # 提取文件名中的序号（如果有的话）
    # 匹配模式：SKC-数字.jpg 或 SKC-数字-尺寸.jpg
    number_match = re.search(r'-(\d+)(?:-\d+x\d+)?\.jpg$', filename_lower)
    if number_match:
        file_number = int(number_match.group(1))

        # 这里需要根据属性集的顺序来判断
        # 简化处理：如果文件名包含 -1, -2, -3 等，认为是传统格式，直接返回True
        # 让调用方根据具体的属性集顺序来决定是否匹配
        if 1 <= file_number <= 10:  # 假设最多10个属性集
            logging.info(f"📏 传统格式匹配: {filename} 包含序号 {file_number}")
            return True

    # 如果既没有尺寸信息，也没有序号，可能是基础SKC图片（如 28969221362.jpg）
    # 这种情况下也返回True，让它参与分组
    if re.match(r'^\d+\.jpg$', filename_lower):
        logging.info(f"📏 基础SKC图片: {filename}")
        return True

    return False

def has_size_info_in_filename(filename):
    """
    检查文件名是否包含尺寸信息（新命名格式）

    Args:
        filename: 图片文件名

    Returns:
        bool: 是否包含尺寸信息
    """
    if not filename:
        return False

    filename_lower = filename.lower()

    # 检查是否包含尺寸信息的模式
    # 例如：-16x16.jpg, -20x20.jpg, _16x16.jpg 等
    size_patterns = [
        r'-\d+x\d+\.jpg',
        r'-\d+X\d+\.jpg',
        r'-\d+×\d+\.jpg',
        r'_\d+x\d+\.jpg',
        r'_\d+X\d+\.jpg',
        r'_\d+×\d+\.jpg'
    ]

    for pattern in size_patterns:
        if re.search(pattern, filename_lower):
            return True

    return False



def get_image_group_from_attribute_set(attribute_set):
    """
    根据属性集确定图片应该归类到哪个组

    Args:
        attribute_set: 属性集（如16inchX16inch, 20inchX20inch, 24inchX24inch）

    Returns:
        str: 对应的图片组名称（如"40x60cm", "40x40cm", "30x40cm"）
    """
    if not attribute_set:
        return None

    attribute_lower = attribute_set.lower()

    # 从属性集中提取尺寸信息
    # 例如：16inchX16inch -> 16x16, 20inchX20inch -> 20x20
    size_match = re.search(r'(\d+)inch[xX×](\d+)inch', attribute_lower)
    if size_match:
        width = int(size_match.group(1))
        height = int(size_match.group(2))

        # 根据英寸尺寸映射到厘米组
        # 16x16 inch ≈ 40x40 cm
        # 16x24 inch ≈ 40x60 cm
        # 12x16 inch ≈ 30x40 cm
        if width == 16 and height == 24:
            return "40x60cm"
        elif width == 24 and height == 16:
            return "40x60cm"
        elif width == 16 and height == 16:
            return "40x40cm"
        elif width == 12 and height == 16:
            return "30x40cm"
        elif width == 16 and height == 12:
            return "30x40cm"
        else:
            # 对于其他尺寸，根据面积大小来判断
            area = width * height
            if area >= 384:  # 16x24 = 384
                return "40x60cm"
            elif area >= 256:  # 16x16 = 256
                return "40x40cm"
            else:
                return "30x40cm"

    # 如果无法从属性集中提取尺寸信息，返回None
    logging.warning(f"⚠️ 无法从属性集 {attribute_set} 中提取尺寸信息")
    return None

def is_valid_sku_hao(sku_hao_str):
    """
    判断是否为有效的SKU货号

    有效的SKU货号特征：
    1. 包含足够多的真正英文字母（至少2个，不包括尺寸中的x）
    2. 不是纯数字
    3. 不是SKC编号格式（9-11位纯数字）
    4. 不是SKC+尺寸后缀格式（如：37892931206-40x60）
    5. 真正的SKU货号应该像：3pc11.8x15.7inch 这样的格式

    Args:
        sku_hao_str: SKU货号字符串

    Returns:
        bool: 是否为有效的SKU货号
    """
    if not sku_hao_str or not isinstance(sku_hao_str, str):
        return False

    sku_hao_str = sku_hao_str.strip()

    # 如果是纯数字，不是SKU货号
    if sku_hao_str.isdigit():
        return False

    # 检查是否为SKC+尺寸后缀格式（如：37892931206-40x60, 37892931206-1-40x60）
    # 这种格式应该被识别为SKC编号，而不是SKU货号
    skc_size_patterns = [
        r'^\d{9,11}[-_](\d+x\d+)$',           # 37892931206-40x60
        r'^\d{9,11}[-_]\d+[-_](\d+x\d+)$',    # 37892931206-1-40x60
        r'^\d{9,11}[-_]\d+$',                 # 37892931206-1
    ]

    for pattern in skc_size_patterns:
        if re.match(pattern, sku_hao_str, re.IGNORECASE):
            return False

    # 检查是否以长数字开头（可能是SKC编号的变种）
    if re.match(r'^\d{9,}', sku_hao_str):
        return False

    # 检查真正的英文字母数量（排除尺寸格式中的x）
    # 先移除常见的尺寸格式，然后计算剩余的字母
    temp_str = sku_hao_str
    # 移除常见尺寸格式：数字x数字
    temp_str = re.sub(r'\d+\.?\d*x\d+\.?\d*', '', temp_str, flags=re.IGNORECASE)
    # 移除单独的x（可能是尺寸分隔符）
    temp_str = re.sub(r'\bx\b', '', temp_str, flags=re.IGNORECASE)

    # 计算剩余的真正英文字母
    real_letter_count = len(re.findall(r'[a-zA-Z]', temp_str))

    if real_letter_count < 2:
        return False

    return True

def find_images_by_sku_hao(source_folder, sku_hao):
    """
    根据SKU货号查找图片文件，支持带-1,-2,-3,-4后缀的命名格式
    只处理真正的SKU货号（包含英文字母且不是SKC格式）

    Args:
        source_folder: 源图片文件夹
        sku_hao: SKU货号

    Returns:
        list: 找到的图片文件路径列表
    """
    found_files = []

    if not os.path.isdir(source_folder) or not sku_hao:
        return found_files

    # 清理SKU货号，确保是字符串格式
    sku_hao_str = str(sku_hao).strip()

    # 🔧 重要修复：使用更精确的SKU货号验证
    if not is_valid_sku_hao(sku_hao_str):
        logging.info(f"🔍 跳过无效SKU货号: {sku_hao_str} (不符合SKU货号格式)")
        return found_files

    logging.info(f"🔍 处理有效SKU货号: {sku_hao_str} (真正的SKU货号格式)")

    for filename in os.listdir(source_folder):
        if not filename.lower().endswith('.jpg'):
            continue

        file_path = os.path.join(source_folder, filename)
        if not os.path.isfile(file_path):
            continue

        # 检查多种SKU货号命名格式
        filename_lower = filename.lower()
        sku_hao_lower = sku_hao_str.lower()

        matched = False
        match_type = ""

        # 方式1：完全匹配（去掉.jpg后缀）
        if filename_lower.replace('.jpg', '') == sku_hao_lower:
            matched = True
            match_type = "完全匹配"

        # 方式2：基础SKU货号匹配（支持带-1,-2,-3,-4后缀）
        elif filename_lower.startswith(sku_hao_lower):
            # 检查是否为有效的后缀格式
            remaining = filename_lower[len(sku_hao_lower):]
            if remaining == '.jpg':
                # 无后缀的基础格式
                matched = True
                match_type = "基础格式"
            elif remaining in ['-1.jpg', '-2.jpg', '-3.jpg', '-4.jpg']:
                # 带-1,-2,-3,-4后缀的格式
                matched = True
                match_type = f"后缀格式{remaining}"
            elif remaining.startswith('-') and remaining.endswith('.jpg'):
                # 其他后缀格式（如尺寸后缀等）
                suffix = remaining[1:-4]  # 去掉'-'和'.jpg'
                matched = True
                match_type = f"其他后缀({suffix})"

        # 方式3：包含匹配（作为备用）
        elif sku_hao_lower in filename_lower:
            matched = True
            match_type = "包含匹配"

        if matched:
            found_files.append(file_path)
            logging.info(f"🔍 找到SKU货号图片: {filename} (SKU货号: {sku_hao_str}, 匹配方式: {match_type})")

    return found_files

def move_file(source_path, target_folder, file_extension=None):
    """
    复制单个文件，如果存在的话（已改为复制操作）
    file_extension参数保留用于兼容性，但当前未使用
    """
    file_name = os.path.basename(source_path)
    target_path = os.path.join(target_folder, file_name)
    if os.path.exists(source_path):
        os.makedirs(target_folder, exist_ok=True)
        if not os.path.exists(target_path):
            try:
                shutil.copy2(source_path, target_path)  # 改为复制操作
                logging.info(f"成功复制文件: {source_path} 到 {target_path}")
            except Exception as e:
                logging.error(f"复制文件失败：{source_path} 到 {target_path}，原因：{e}")
        else:
            logging.warning(f"目标文件已存在，跳过：{target_path}")
    else:
        logging.warning(f"文件未找到：{source_path}")

def copy_file(source_path, target_folder, file_extension=None):
    """
    复制单个文件，如果存在的话
    """
    file_name = os.path.basename(source_path)
    target_path = os.path.join(target_folder, file_name)
    if os.path.exists(source_path):
        os.makedirs(target_folder, exist_ok=True)
        if not os.path.exists(target_path):
            try:
                shutil.copy2(source_path, target_path)  # 使用copy2进行真正的复制
                logging.info(f"成功复制文件: {source_path} 到 {target_path}")
            except Exception as e:
                logging.error(f"复制文件失败：{source_path} 到 {target_path}，原因：{e}")
        else:
            logging.warning(f"目标文件已存在，跳过：{target_path}")
    else:
        logging.warning(f"文件未找到：{source_path}")

    # 避免未使用参数警告
    _ = file_extension

def process_small_pdf_move_then_merge(small_pdf_folder, skc_base, target_folders, attribute_set, df, small_label_target_folder, progress_tracker, index):
    """
    🔥 新的小标PDF处理函数：强制执行先移动再合并的逻辑

    Args:
        small_pdf_folder: 小标PDF源文件夹
        skc_base: SKC编号
        target_folders: 目标文件夹列表
        attribute_set: 属性集
        df: 拣货单数据
        small_label_target_folder: 小标目标文件夹
        progress_tracker: 进度跟踪器
        index: 当前行索引

    Returns:
        bool: 是否成功处理
    """
    logging.info(f"🔥🔥🔥🔥🔥 [新函数确认] 开始处理小标PDF：先移动再合并 🔥🔥🔥🔥🔥")
    print(f"🔥🔥🔥🔥🔥 [控制台确认] 新函数被调用：SKC={skc_base} 🔥🔥🔥🔥🔥")

    # 使用匹配函数查找小标PDF
    matched_result = find_matching_small_pdf_by_skc_and_size(small_pdf_folder, skc_base, target_folders, attribute_set, df)

    if not matched_result:
        logging.warning(f"🔥 [新函数] 未找到匹配的小标PDF文件")
        return False

    # 处理返回结果：检查是否为SKU货号命名格式
    is_sku_naming = False
    sku_rename_info = []

    if isinstance(matched_result, list) and len(matched_result) > 0:
        # 检查第一个元素是否为元组（SKU命名格式）
        if isinstance(matched_result[0], tuple) and len(matched_result[0]) == 3:
            # SKU货号命名格式：[(original_name, new_name, sku_number), ...]
            is_sku_naming = True
            sku_rename_info = matched_result
            matched_filenames = [item[0] for item in matched_result]  # 原始文件名
            logging.info(f"🏷️ [新函数] 找到SKU货号命名的小标PDF文件: {len(matched_filenames)} 个")
        else:
            # 普通SKC命名格式
            matched_filenames = matched_result
            logging.info(f"🔥 [新函数] 找到匹配的小标PDF文件: {len(matched_filenames)} 个")
    else:
        matched_filenames = [matched_result] if matched_result else []
        logging.info(f"🔥 [新函数] 找到匹配的小标PDF: {matched_result}")

    # 检查目标文件夹是否存在
    if not os.path.exists(small_label_target_folder):
        os.makedirs(small_label_target_folder, exist_ok=True)
        logging.info(f"🔥 [新函数] 创建小标目标文件夹: {small_label_target_folder}")

    # 🔥 第一步：先移动PDF文件到对应文件夹
    logging.info(f"🔥 [新函数] 第一步：移动 {len(matched_filenames)} 个PDF文件到目标文件夹")
    moved_pdf_paths = []

    for i, matched_filename in enumerate(matched_filenames):
        source_small_pdf_path = os.path.join(small_pdf_folder, matched_filename)
        if os.path.exists(source_small_pdf_path):
            # 确定目标文件名
            if is_sku_naming and i < len(sku_rename_info):
                # SKU货号命名：使用新文件名
                target_filename = sku_rename_info[i][1]  # new_name
                sku_number = sku_rename_info[i][2]       # sku_number
                logging.info(f"🏷️ [新函数] SKU命名重命名: {matched_filename} -> {target_filename} (SKU: {sku_number})")
            else:
                # 普通SKC命名：保持原文件名
                target_filename = matched_filename

            target_moved_path = os.path.join(small_label_target_folder, target_filename)
            try:
                shutil.move(source_small_pdf_path, target_moved_path)
                moved_pdf_paths.append(target_moved_path)
                logging.info(f"🔥 [新函数] ✅ 移动成功: {matched_filename} -> {target_filename}")
            except Exception as e:
                logging.error(f"🔥 [新函数] ❌ 移动失败: {source_small_pdf_path}, 错误: {e}")
        else:
            logging.error(f"🔥 [新函数] ❌ 源文件不存在: {source_small_pdf_path}")

    if not moved_pdf_paths:
        logging.error(f"🔥 [新函数] ❌ 没有成功移动任何PDF文件")
        return False

    logging.info(f"🔥 [新函数] 第一步完成：成功移动了 {len(moved_pdf_paths)} 个PDF文件")

    # 🔥 第二步：合并同一文件夹中的多个PDF文件
    if len(moved_pdf_paths) == 1:
        # 只有一个文件，直接进行重命名
        logging.info(f"🔥 [新函数] 第二步：只有一个文件，跳过合并")
        final_pdf_path = moved_pdf_paths[0]
    else:
        # 多个文件，需要合并
        logging.info(f"🔥 [新函数] 第二步：开始合并 {len(moved_pdf_paths)} 个PDF文件")

        # 确定合并后的临时文件名
        temp_merged_filename = f"merged_temp_{skc_base}.pdf"
        temp_merged_path = os.path.join(small_label_target_folder, temp_merged_filename)

        # 合并PDF文件
        if merge_pdf_files(moved_pdf_paths, temp_merged_path):
            logging.info(f"🔥 [新函数] ✅ 合并成功: {temp_merged_filename}")

            # 删除原始的已移动文件
            for moved_path in moved_pdf_paths:
                try:
                    os.remove(moved_path)
                    logging.info(f"🔥 [新函数] 🗑️ 删除已合并的文件: {os.path.basename(moved_path)}")
                except Exception as e:
                    logging.error(f"🔥 [新函数] ❌ 删除已合并文件失败: {moved_path}, 错误: {e}")

            final_pdf_path = temp_merged_path
        else:
            logging.error(f"🔥 [新函数] ❌ 合并失败")
            return False

    # 🔥 第三步：重命名为最终格式
    logging.info(f"🔥 [新函数] 第三步：重命名为最终格式")

    # 确定最终的文件名：根据命名类型决定如何提取基础名称
    first_filename = matched_filenames[0]

    # 检查是否为SKU货号命名格式（包含字母）
    if is_sku_naming and len(sku_rename_info) > 0:
        # SKU货号命名：查找对应的SKC并使用SKC命名
        original_sku_number = sku_rename_info[0][2]  # sku_number (第三个元素)

        # 从文件夹路径中提取SKC
        corresponding_skc = None
        folder_path_parts = small_label_target_folder.split(os.sep)
        for part in folder_path_parts:
            # 查找包含SKC的文件夹名称部分
            skc_match = re.search(r'(\d{9,11})', part)
            if skc_match:
                corresponding_skc = skc_match.group(1)
                break

        if corresponding_skc:
            final_filename = f"{corresponding_skc}.pdf"
            logging.info(f"🔥 [新函数] SKU货号命名，使用对应的SKC命名: {original_sku_number} -> {corresponding_skc}")
        else:
            # 如果找不到对应的SKC，使用SKU货号
            final_filename = f"{original_sku_number}.pdf"
            logging.info(f"🔥 [新函数] SKU货号命名，未找到对应SKC，使用SKU货号: {final_filename}")
    else:
        # 传统SKC命名：提取SKC，去掉页数后缀
        base_match = re.match(r'([^-]+)', first_filename)
        if base_match:
            base_name = base_match.group(1)
            final_filename = f"{base_name}.pdf"
        else:
            # 如果无法提取，使用原始文件名
            final_filename = first_filename
        logging.info(f"🔥 [新函数] 传统SKC命名，提取基础名称: {final_filename}")

    final_target_path = os.path.join(small_label_target_folder, final_filename)

    # 如果最终文件名与当前文件名不同，进行重命名
    if final_pdf_path != final_target_path:
        try:
            os.rename(final_pdf_path, final_target_path)
            logging.info(f"🔥 [新函数] 📝 重命名成功: {os.path.basename(final_pdf_path)} -> {final_filename}")
        except Exception as e:
            logging.error(f"🔥 [新函数] ❌ 重命名失败: {e}")
    else:
        logging.info(f"🔥 [新函数] 📝 文件名已是最终格式: {final_filename}")

    logging.info(f"🔥 [新函数] 处理完成：成功处理小标PDF文件 {final_filename}")
    return True



def should_move_small_pdf(df, skc_base, current_index):
    """
    简化版：判断是否应该移动小标PDF文件（而不是复制）

    逻辑：统计同一个SKC在拣货单中出现的次数
    - 如果只出现1次 → 直接移动
    - 如果出现多次 → 前几次复制，最后一次移动

    Args:
        df: 拣货单数据
        skc_base: 当前SKC编号
        current_index: 当前处理的行索引

    Returns:
        bool: True表示应该移动（最后一次使用），False表示应该复制
    """
    if df is None or df.empty:
        logging.warning(f"SKC {skc_base} 拣货单数据为空，默认移动")
        return True

    # 简单统计：找到该SKC在拣货单中的所有出现位置
    skc_indices = []

    for idx, row in df.iterrows():
        product_info = row.get('商品信息', '')

        if not isinstance(product_info, str) or not product_info.strip():
            continue

        # 提取SKC编号
        skc_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if skc_match and skc_match.group(1) == skc_base:
            skc_indices.append(idx)

    if not skc_indices:
        logging.warning(f"SKC {skc_base} 在拣货单中未找到，默认移动")
        return True

    if len(skc_indices) == 1:
        # 只出现1次，直接移动
        logging.info(f"SKC {skc_base} 只出现1次，直接移动")
        return True

    # 多次出现，判断当前是否是最后一次
    try:
        current_position = skc_indices.index(current_index)
        is_last = (current_position == len(skc_indices) - 1)

        logging.info(f"SKC {skc_base} 出现{len(skc_indices)}次，当前第{current_position + 1}次")
        logging.info(f"SKC {skc_base} 所有位置: {skc_indices}")
        logging.info(f"SKC {skc_base} 当前位置{current_index}, {'移动' if is_last else '复制'}")

        return is_last

    except ValueError:
        logging.warning(f"SKC {skc_base} 当前索引{current_index}不在SKC列表中，默认移动")
        return True

def diagnose_small_pdf_issue(df, small_pdf_folder, skc_base):
    """
    专门诊断小标PDF问题的函数
    """
    logging.info(f"🔍 开始诊断小标PDF问题...")
    logging.info(f"🔍 目标SKC: {skc_base}")
    logging.info(f"🔍 小标PDF文件夹: {small_pdf_folder}")

    # 1. 检查小标PDF文件夹是否存在
    if not os.path.exists(small_pdf_folder):
        logging.error(f"❌ 小标PDF文件夹不存在: {small_pdf_folder}")
        return False

    if not os.path.isdir(small_pdf_folder):
        logging.error(f"❌ 小标PDF路径不是文件夹: {small_pdf_folder}")
        return False

    # 2. 列出文件夹中的所有文件
    try:
        all_files = os.listdir(small_pdf_folder)
        logging.info(f"🔍 小标PDF文件夹中共有 {len(all_files)} 个文件")

        if not all_files:
            logging.error(f"❌ 小标PDF文件夹为空")
            return False

        # 显示所有文件
        for i, filename in enumerate(all_files, 1):
            file_path = os.path.join(small_pdf_folder, filename)
            file_size = os.path.getsize(file_path) if os.path.isfile(file_path) else 0
            logging.info(f"🔍 文件{i}: {filename} ({file_size} 字节)")

        # 3. 检查PDF文件
        pdf_files = [f for f in all_files if f.lower().endswith('.pdf')]
        logging.info(f"🔍 其中PDF文件 {len(pdf_files)} 个: {pdf_files}")

        if not pdf_files:
            logging.error(f"❌ 小标PDF文件夹中没有PDF文件")
            return False

        # 4. 检查SKC匹配
        logging.info(f"🔍 检查SKC匹配情况...")
        matched_files = []

        for filename in pdf_files:
            # 检查文件名格式
            match = re.fullmatch(r'\d{9,11}\.pdf', filename.lower())
            if match:
                file_skc = filename.split('.')[0]
                logging.info(f"🔍 PDF文件: {filename} -> 提取SKC: {file_skc}")

                # 检查SKC匹配
                if skc_base == file_skc:
                    matched_files.append(filename)
                    logging.info(f"✅ 完全匹配: {skc_base} == {file_skc}")
                elif len(skc_base) == 9 and file_skc.startswith(skc_base) and len(file_skc) == 11:
                    matched_files.append(filename)
                    logging.info(f"✅ 前缀匹配: {skc_base} 匹配 {file_skc}")
                else:
                    logging.info(f"❌ 不匹配: {skc_base} != {file_skc}")
            else:
                logging.info(f"❌ 文件名格式不符: {filename}")

        if matched_files:
            logging.info(f"✅ 找到匹配的小标PDF文件: {matched_files}")
            return True
        else:
            logging.error(f"❌ 没有找到匹配SKC {skc_base} 的小标PDF文件")

            # 提供建议
            logging.info(f"💡 建议检查:")
            logging.info(f"   1. SKC编号是否正确: {skc_base}")
            logging.info(f"   2. 小标PDF文件名是否为纯数字格式 (如: {skc_base}.pdf)")
            logging.info(f"   3. 现有PDF文件: {pdf_files}")

            return False

    except Exception as e:
        logging.error(f"❌ 诊断过程中出错: {e}")
        return False

def check_and_cleanup_small_pdf_folder(df, small_pdf_folder, target_base_folder):
    """
    检查每个数量文件夹里是否都有对应SKC的小标PDF，如果都有则删除原小标PDF文件夹

    Args:
        df: 拣货单数据
        small_pdf_folder: 原小标PDF文件夹路径
        target_base_folder: 目标基础文件夹路径
    """
    if not os.path.exists(small_pdf_folder) or not os.path.isdir(small_pdf_folder):
        logging.info("小标PDF文件夹不存在，跳过清理检查")
        return

    if not os.path.exists(target_base_folder) or not os.path.isdir(target_base_folder):
        logging.info("目标文件夹不存在，跳过清理检查")
        return

    logging.info("🧹 开始检查数量文件夹中的小标PDF...")

    # 1. 收集拣货单中的所有SKC
    required_skcs = set()
    for _, row in df.iterrows():
        product_info = row.get('商品信息', '')
        if not isinstance(product_info, str) or not product_info.strip():
            continue

        # 提取SKC编号
        skc_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if skc_match:
            required_skcs.add(skc_match.group(1))

    logging.info(f"🧹 拣货单中需要的SKC: {sorted(required_skcs)}")

    if not required_skcs:
        logging.info("🧹 拣货单中没有找到SKC，跳过检查")
        return

    # 2. 遍历所有属性集文件夹，检查数量文件夹中的小标PDF
    all_quantity_folders_have_pdfs = True
    missing_pdfs = []

    try:
        for main_folder_name in os.listdir(target_base_folder):
            main_folder_path = os.path.join(target_base_folder, main_folder_name)

            if not os.path.isdir(main_folder_path):
                continue

            # 跳过特殊文件夹
            if main_folder_name in ['小标PDF', '大标']:
                continue

            logging.info(f"🧹 检查属性集文件夹: {main_folder_name}")

            # 遍历该属性集下的数量文件夹
            for quantity_folder_name in os.listdir(main_folder_path):
                quantity_folder_path = os.path.join(main_folder_path, quantity_folder_name)

                if not os.path.isdir(quantity_folder_path):
                    continue

                # 检查是否是数量文件夹（如"2张"、"3套"）
                if not re.match(r'\d+[张套]', quantity_folder_name):
                    continue

                logging.info(f"🧹   检查数量文件夹: {quantity_folder_name}")

                # 检查该数量文件夹中的小标PDF
                small_pdf_subfolder = os.path.join(quantity_folder_path, '小标')
                if not os.path.exists(small_pdf_subfolder):
                    logging.warning(f"🧹     数量文件夹中没有小标子文件夹: {quantity_folder_name}")
                    all_quantity_folders_have_pdfs = False
                    missing_pdfs.append(f"{main_folder_name}/{quantity_folder_name}/小标 (文件夹不存在)")
                    continue

                # 检查小标PDF子文件夹中的文件
                pdf_files_in_quantity = []
                try:
                    for pdf_file in os.listdir(small_pdf_subfolder):
                        if pdf_file.lower().endswith('.pdf'):
                            pdf_files_in_quantity.append(pdf_file)

                    logging.info(f"🧹     找到小标PDF文件: {pdf_files_in_quantity}")

                    if not pdf_files_in_quantity:
                        logging.warning(f"🧹     数量文件夹中没有小标PDF文件: {quantity_folder_name}")
                        all_quantity_folders_have_pdfs = False
                        missing_pdfs.append(f"{main_folder_name}/{quantity_folder_name}/小标 (无PDF文件)")

                except Exception as e:
                    logging.error(f"🧹     检查小标子文件夹失败: {e}")
                    all_quantity_folders_have_pdfs = False
                    missing_pdfs.append(f"{main_folder_name}/{quantity_folder_name}/小标 (读取失败)")

    except Exception as e:
        logging.error(f"🧹 检查过程中出错: {e}")
        return

    # 3. 决定是否删除原文件夹
    if all_quantity_folders_have_pdfs and not missing_pdfs:
        logging.info("🧹 所有数量文件夹都有对应的小标PDF，可以安全删除原文件夹")
        try:
            shutil.rmtree(small_pdf_folder)
            logging.info(f"✅ 已删除小标PDF文件夹: {small_pdf_folder}")
        except Exception as e:
            logging.error(f"❌ 删除小标PDF文件夹失败: {e}")
    else:
        logging.warning(f"🧹 有数量文件夹缺少小标PDF，保留原文件夹")
        if missing_pdfs:
            logging.warning(f"🧹 缺少小标PDF的位置:")
            for missing in missing_pdfs:
                logging.warning(f"🧹   - {missing}")

def round_size(value):
    """
    四舍五入尺寸，返回字符串格式
    """
    return str(round(float(value), 1))

# 表格模版识别配置
_template_mapping = {}

# 全局变量：存储最近处理的DataFrame，用于更新属性集功能
_last_processed_df = None

# ====== 新增：表格映射本地记忆功能 ======
if getattr(sys, 'frozen', False):
    BASE_DIR = os.path.dirname(sys.executable)
else:
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
TEMPLATE_MAPPING_FILE = os.path.join(BASE_DIR, 'template_mapping.json')

def save_template_mapping_to_local():
    """保存当前_template_mapping到统一配置文件"""
    try:
        config = load_unified_config()
        config["template_mapping"] = _template_mapping
        save_unified_config(config)
        logging.info(f"已保存表格映射到统一配置，共{len(_template_mapping)}条")

        # 不再保存到旧文件，只使用统一配置
    except Exception as e:
        logging.warning(f"保存表格映射失败: {e}")

def load_template_mapping_from_local():
    """从统一配置文件加载_template_mapping"""
    global _template_mapping
    try:
        config = load_unified_config()
        template_mapping = config.get("template_mapping", {})

        if template_mapping:
            _template_mapping.clear()
            _template_mapping.update(template_mapping)
            logging.info(f"已从统一配置加载表格映射，共{len(_template_mapping)}条")
        else:
            # 兼容性：如果统一配置中没有，尝试从旧文件读取
            if os.path.exists(TEMPLATE_MAPPING_FILE):
                with open(TEMPLATE_MAPPING_FILE, 'r', encoding='utf-8') as f:
                    old_mapping = json.load(f)
                    _template_mapping.clear()
                    _template_mapping.update(old_mapping)
                logging.info(f"已从旧文件加载表格映射: {TEMPLATE_MAPPING_FILE}，共{len(_template_mapping)}条")

                # 迁移到统一配置
                save_template_mapping_to_local()
    except Exception as e:
        logging.warning(f"加载表格映射失败: {e}")

def load_template_mapping(file_path, clear_existing=False):
    """加载表格模版映射

    Args:
        file_path: 文件路径
        clear_existing: 是否清空现有映射，默认False（累计模式）
    """
    global _template_mapping
    try:
        pd = lazy_import_pandas()
        if not pd:
            return False

        # 如果指定清空现有映射
        if clear_existing:
            _template_mapping.clear()
            logging.info("已清空现有映射")

        # 读取Excel或CSV文件
        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            df = pd.read_excel(file_path)
        else:
            df = pd.read_csv(file_path, encoding='utf-8-sig')

        logging.info(f"成功读取文件: {file_path}")
        logging.info(f"文件包含 {len(df)} 行数据")
        logging.info(f"列名: {list(df.columns)}")

        # 假设A列是拣货单原属性集，B列是需要识别匹配的属性集名称
        if len(df.columns) >= 2:
            new_count = 0
            update_count = 0
            cleaned_count = 0
            skipped_count = 0
            error_count = 0

            for index, row in df.iterrows():
                try:
                    original_attr_raw = str(row.iloc[0])  # A列原始数据
                    target_attr_raw = str(row.iloc[1])    # B列原始数据

                    # 跳过空行
                    if pd.isna(row.iloc[0]) or pd.isna(row.iloc[1]):
                        skipped_count += 1
                        logging.info(f"第{index+1}行：跳过空行")
                        continue

                    # 清理A列数据（去除换行符、多余空格等）
                    original_attr_cleaned = clean_attribute_set(original_attr_raw)
                    target_attr_cleaned = clean_attribute_set(target_attr_raw)

                    # 记录清理情况
                    if original_attr_raw != original_attr_cleaned:
                        cleaned_count += 1
                        logging.info(f"第{index+1}行A列清理: '{original_attr_raw}' -> '{original_attr_cleaned}'")

                    if target_attr_raw != target_attr_cleaned:
                        logging.info(f"第{index+1}行B列清理: '{target_attr_raw}' -> '{target_attr_cleaned}'")

                    # 跳过空值
                    if not original_attr_cleaned or not target_attr_cleaned or original_attr_cleaned == 'nan' or target_attr_cleaned == 'nan':
                        skipped_count += 1
                        logging.info(f"第{index+1}行：跳过空值 - A列:'{original_attr_cleaned}', B列:'{target_attr_cleaned}'")
                        continue

                    # 检查是否是新映射还是更新现有映射
                    if original_attr_cleaned in _template_mapping:
                        if _template_mapping[original_attr_cleaned] != target_attr_cleaned:
                            logging.info(f"更新映射: '{original_attr_cleaned}' -> '{target_attr_cleaned}' (原: '{_template_mapping[original_attr_cleaned]}')")
                            update_count += 1
                        else:
                            logging.info(f"映射已存在: '{original_attr_cleaned}' -> '{target_attr_cleaned}'")
                            continue
                    else:
                        logging.info(f"新增映射: '{original_attr_cleaned}' -> '{target_attr_cleaned}'")
                        new_count += 1

                    _template_mapping[original_attr_cleaned] = target_attr_cleaned

                except Exception as e:
                    logging.warning(f"处理第{index+1}行时出错: {e}")
                    continue

            total_loaded = new_count + update_count
            logging.info(f"映射加载完成: 新增 {new_count} 条，更新 {update_count} 条，总计加载 {total_loaded} 条")
            if cleaned_count > 0:
                logging.info(f"数据清理: 共清理了 {cleaned_count} 条A列数据的格式问题（换行符、多余空格等）")
            logging.info(f"当前映射表总数: {len(_template_mapping)} 条")
            save_template_mapping_to_local()  # 新增：导入后自动保存
            return total_loaded
        else:
            logging.error("表格格式错误：需要至少2列数据")
            return 0
    except Exception as e:
        logging.error(f"加载表格模版失败: {e}")
        return 0

def clear_template_mapping():
    """清空所有映射"""
    global _template_mapping
    count = len(_template_mapping)
    _template_mapping.clear()
    logging.info(f"已清空 {count} 条映射")
    save_template_mapping_to_local()  # 新增：清空后自动保存
    return count

def replace_date_time_in_template(template_result):
    """替换模板结果中的日期和时间段为当前自动获取的值"""
    if not template_result or template_result == "unknown":
        return template_result

    # 获取当前日期和时间段
    auto_date, auto_time = get_auto_date_and_time()

    # 替换日期模式（如 4-15, 6-14 等）
    date_pattern = r'\d{1,2}-\d{1,2}'
    template_result = re.sub(date_pattern, auto_date, template_result)

    # 替换时间段模式
    # 处理 "上/下午" 格式
    if "上/下午" in template_result:
        template_result = template_result.replace("上/下午", auto_time)
    # 处理单独的 "上午" 或 "下午"
    elif "上午" in template_result:
        template_result = template_result.replace("上午", auto_time)
    elif "下午" in template_result:
        template_result = template_result.replace("下午", auto_time)

    return template_result

def replace_operator_shop_in_template(template_result, folder_config):
    """替换模板结果中的操作员和店铺号为用户输入的值"""
    if not template_result or template_result == "unknown":
        return template_result

    # 获取用户输入的操作员和店铺号
    operator = folder_config.get("operator", "").strip()
    shop_number = folder_config.get("shop_number", "").strip()

    # 替换操作员（如果用户有输入）
    if operator:
        template_result = template_result.replace("彭于晏", operator)

    # 替换店铺号（如果用户有输入）
    if shop_number:
        template_result = template_result.replace("店铺号", shop_number)

    return template_result

def clean_attribute_set(attribute_set):
    """彻底清理属性集中的换行符和特殊字符"""
    if not attribute_set or not isinstance(attribute_set, str):
        return ""

    # 1. 去除所有类型的换行符和制表符
    cleaned = re.sub(r'[\n\r\t\f\v]+', '', attribute_set)

    # 2. 将多个连续空格替换为单个空格
    cleaned = re.sub(r'\s+', ' ', cleaned)

    # 3. 去除首尾空格
    cleaned = cleaned.strip()

    return cleaned

def extract_pc_and_size_from_target(target_attribute):
    """从目标属性集中提取pc数和尺寸信息"""
    if not target_attribute or not isinstance(target_attribute, str):
        return 1, None, None

    # 提取pc数（如1pc, 3pc）
    pc_match = re.search(r'(\d+)pc', target_attribute.lower())
    pc_count = int(pc_match.group(1)) if pc_match else 1

    # 提取厘米尺寸（如30x40cm, 40x60cm, 45x45cm, 55x68cm）
    cm_match = re.search(r'(\d+)x(\d+)cm', target_attribute.lower())
    if cm_match:
        width = int(cm_match.group(1))
        height = int(cm_match.group(2))
        size_type = f"{width}x{height}"
    else:
        width, height, size_type = None, None, None

    logging.info(f"🔍 目标属性集分析: '{target_attribute}' -> pc数: {pc_count}, 尺寸: {size_type}")

    return pc_count, size_type, (width, height)

def get_template_mapping(attribute_set):
    """根据表格模版获取属性集映射"""
    if not attribute_set or not isinstance(attribute_set, str):
        return "unknown"

    # 彻底清理输入的属性集
    cleaned_attribute_set = clean_attribute_set(attribute_set)

    logging.info(f"🔧 属性集清理: '{attribute_set}' -> '{cleaned_attribute_set}'")

    # 直接匹配
    if cleaned_attribute_set in _template_mapping:
        result = _template_mapping[cleaned_attribute_set]
        logging.info(f"✅ 直接匹配成功: '{cleaned_attribute_set}' -> '{result}'")
        # 替换日期和时间段
        result = replace_date_time_in_template(result)
        return result

    # 模糊匹配（去除空格和大小写）
    cleaned_input = cleaned_attribute_set.lower()
    for original, target in _template_mapping.items():
        # 同样清理映射表中的原始键
        cleaned_original = clean_attribute_set(original).lower()
        if cleaned_input == cleaned_original:
            result = target
            logging.info(f"✅ 模糊匹配成功: '{cleaned_input}' == '{cleaned_original}' -> '{result}'")
            # 替换日期和时间段
            result = replace_date_time_in_template(result)
            return result

    # 包含匹配
    for original, target in _template_mapping.items():
        cleaned_original = clean_attribute_set(original).lower()
        if cleaned_input in cleaned_original or cleaned_original in cleaned_input:
            result = target
            logging.info(f"✅ 包含匹配成功: '{cleaned_input}' <-> '{cleaned_original}' -> '{result}'")
            # 替换日期和时间段
            result = replace_date_time_in_template(result)
            return result

    logging.warning(f"❌ 未找到映射，使用原属性集名: '{cleaned_attribute_set}'")
    # 返回清理后的原属性集名，用于创建文件夹
    return cleaned_attribute_set

def get_size_folder(attribute_set, folder_config=None):
    """使用表格模版识别属性集"""
    logging.info(f"--- 处理属性集: {attribute_set} ---")

    if not attribute_set or not isinstance(attribute_set, str):
        logging.warning("属性集为空或无效")
        return "unknown"

    # 使用表格模版映射
    result = get_template_mapping(attribute_set)

    # 如果有配置信息，替换操作员和店铺号
    # 注意：现在get_template_mapping返回原属性集名而不是"unknown"，所以需要检查是否是映射结果
    if folder_config and result != clean_attribute_set(attribute_set):
        # 说明找到了映射，可以替换操作员和店铺号
        result = replace_operator_shop_in_template(result, folder_config)

    logging.info(f"属性集 '{attribute_set}' 通过表格模版识别为: '{result}'")

    return result

# 删除复杂的尺寸识别函数，改用表格模版识别

# 删除复杂的尺寸匹配函数，改用表格模版识别

# 新增：根据"合计"精准检测多规格行
def is_multi_spec_by_total(row):
    """
    检查该行所有字段是否包含"合计"二字，兼容Series和ndarray
    """
    try:
        iterable = row.values if hasattr(row, 'values') else row
        for v in iterable:
            if isinstance(v, str) and '合计' in v:
                return True
    except Exception:
        # 兜底：直接遍历 row
        for v in row:
            if isinstance(v, str) and '合计' in v:
                return True
    return False

# 新增：多规格拆分函数
def split_multi_specs(attribute_set):
    """
    拆分一行中的多规格描述，返回每个规格的字符串列表。
    支持常见分隔符：换行、分号、逗号、A-等。
    """
    if not isinstance(attribute_set, str):
        return []
    # 先用换行、分号、逗号拆分
    specs = re.split(r'[\n;,，；]+', attribute_set)
    # 再进一步用A-分割（但保留A-）
    result = []
    for spec in specs:
        parts = re.split(r'(?=A-)', spec)
        for part in parts:
            clean = part.strip()
            if clean:
                result.append(clean)
    return [s for s in result if s]

class EnhancedProgressTracker:
    """增强的进度跟踪器 - 支持更细致的进度显示"""

    def __init__(self, progress_bar, progress_label, total_items):
        self.progress_bar = progress_bar
        self.progress_label = progress_label
        self.total_items = total_items
        self.current_item = 0
        self.current_sub_step = 0
        self.total_sub_steps = 0
        self.stats = {
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'duplicates': 0,
            'images_processed': 0,
            'pdfs_processed': 0,
            'folders_created': 0
        }
        self.current_phase = "准备中"
        self.start_time = None
        self.last_update_time = None
        self.update_interval = 0.1  # 最小更新间隔（秒）

    def start_processing(self):
        """开始处理"""
        import time
        self.start_time = time.time()
        self.update_display("开始处理", "正在初始化...")

    def update_progress(self, current_item, phase, detail="", status=None, sub_step=0, total_sub_steps=0):
        """更新进度 - 支持子步骤"""
        import time

        # 检查更新频率限制
        current_time = time.time()
        if self.last_update_time and (current_time - self.last_update_time) < self.update_interval:
            return  # 跳过过于频繁的更新

        self.current_item = current_item
        self.current_phase = phase
        self.current_sub_step = sub_step
        self.total_sub_steps = total_sub_steps
        self.last_update_time = current_time

        if status and status in self.stats:
            self.stats[status] += 1

        # 计算更精确的进度百分比（包含子步骤）
        if self.total_items > 0:
            base_progress = (current_item / self.total_items) * 100

            # 如果有子步骤，计算子步骤进度
            if total_sub_steps > 0:
                sub_progress = (sub_step / total_sub_steps) * (100 / self.total_items)
                progress_percent = base_progress + sub_progress
            else:
                progress_percent = base_progress

            # 确保进度不超过100%
            progress_percent = min(progress_percent, 100)

            if self.progress_bar:
                try:
                    self.progress_bar['value'] = progress_percent
                except:
                    pass

        # 更新显示文本
        self.update_display(phase, detail)

    def update_sub_progress(self, sub_step, total_sub_steps, sub_detail=""):
        """更新子步骤进度"""
        self.update_progress(
            self.current_item,
            self.current_phase,
            sub_detail,
            sub_step=sub_step,
            total_sub_steps=total_sub_steps
        )

    def update_display(self, phase, detail):
        """更新显示文本"""
        import time

        # 计算耗时
        elapsed_time = ""
        if self.start_time:
            elapsed = time.time() - self.start_time
            elapsed_time = f" | 耗时: {elapsed:.1f}s"

        # 构建状态文本
        status_text = (
            f"📊 进度: {self.current_item}/{self.total_items} "
            f"| 🔄 {phase}"
            f"{elapsed_time}\n"
            f"✅ 成功: {self.stats['success']} "
            f"❌ 失败: {self.stats['failed']} "
            f"⏭️ 跳过: {self.stats['skipped']} "
            f"🔄 重复: {self.stats['duplicates']}\n"
            f"📝 {detail}"
        )

        # 检查progress_label是否存在且不为None
        if self.progress_label is not None:
            try:
                self.progress_label.config(text=status_text)
                # 强制更新界面
                if hasattr(self.progress_label, 'update'):
                    self.progress_label.update()
            except Exception as e:
                logging.warning(f"更新进度标签失败: {e}")
        else:
            # 如果没有进度标签，输出到日志
            logging.info(f"进度更新: {phase} - {detail}")

    def finish_processing(self):
        """完成处理"""
        import time

        total_time = ""
        if self.start_time:
            total_elapsed = time.time() - self.start_time
            total_time = f"总耗时: {total_elapsed:.1f}s"

        final_text = (
            f"🎉 处理完成！\n"
            f"✅ 成功: {self.stats['success']} "
            f"❌ 失败: {self.stats['failed']} "
            f"⏭️ 跳过: {self.stats['skipped']} "
            f"🔄 重复: {self.stats['duplicates']}\n"
            f"⏱️ {total_time}"
        )

        # 检查progress_label是否存在且不为None
        if self.progress_label is not None:
            try:
                self.progress_label.config(text=final_text)
            except Exception as e:
                logging.warning(f"更新完成状态失败: {e}")
        else:
            logging.info(f"处理完成: {final_text}")

        # 检查progress_bar是否存在且不为None
        if self.progress_bar is not None:
            try:
                self.progress_bar['value'] = 100
            except Exception as e:
                logging.warning(f"更新进度条失败: {e}")

def process_files(df, target_base_folder, source_folder, small_pdf_folder, progress_bar, progress_label, progress_callback=None, folder_config=None):
    os.makedirs(target_base_folder, exist_ok=True)
    total_rows = len(df)

    # 记录处理开始前已存在的文件夹
    existing_folders = set()
    if os.path.exists(target_base_folder):
        existing_folders = set(os.listdir(target_base_folder))
        logging.info(f"处理开始前已存在 {len(existing_folders)} 个文件夹")

    # 加载文件夹命名配置
    if folder_config is None:
        folder_config = load_folder_config()

    # 预先统计总体信息用于命名（基于小标PDF数量）
    total_stats = calculate_total_stats(df, source_folder, small_pdf_folder)

    # 创建增强的进度跟踪器
    progress_tracker = EnhancedProgressTracker(progress_bar, progress_label, total_rows)
    progress_tracker.start_processing()

    # 添加SKC使用跟踪，确保每个SKC只出现在一个文件夹中
    skc_usage_tracker = {}  # {skc: {'folder': folder_path, 'attribute_set': attribute_set, 'pdf_file': pdf_filename}}

    i = 0
    processed_count = 0

    while i < total_rows:
        row = df.iloc[i]
        product_info = row.get('商品信息')
        attribute_set = row.get('属性集')

        # 跳过无效行
        if (not isinstance(product_info, str) or not product_info.strip()) and (not isinstance(attribute_set, str) or not attribute_set.strip()):
            progress_tracker.update_progress(
                processed_count,
                "跳过无效行",
                f"第{i+1}行数据无效，跳过处理",
                "skipped"
            )
            i += 1
            continue

        # 检查是否为多规格组起点
        if isinstance(product_info, str) and product_info.strip():
            # 提取SKC用于显示
            skc_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
            skc_display = skc_match.group(1) if skc_match else "未知SKC"

            progress_tracker.update_progress(
                processed_count,
                "分析商品组",
                f"正在分析SKC{skc_display}的规格组..."
            )

            # 新组起点
            group = [row]
            j = i + 1
            while j < total_rows:
                next_row = df.iloc[j]
                next_attr = next_row.get('属性集')
                next_prod = next_row.get('商品信息')
                # 合计行
                if any(isinstance(v, str) and '合计' in v for v in next_row):
                    # group为多规格组，合计行不用管
                    progress_tracker.update_progress(
                        processed_count,
                        "处理多规格组",
                        f"SKC{skc_display}包含{len(group)}个规格，开始逐个处理..."
                    )

                    for spec_idx, spec_row in enumerate(group, 1):
                        processed_count += 1
                        progress_tracker.update_progress(
                            processed_count,
                            f"处理规格{spec_idx}/{len(group)}",
                            f"SKC{skc_display}-规格{spec_idx}: {spec_row.get('属性集', '未知属性')[:30]}..."
                        )

                        # 继承首行的商品信息、SKC等，属性集用spec_row自己的
                        try:
                            process_single_spec(
                                spec_row,
                                product_info,  # 继承首行
                                target_base_folder,
                                source_folder,
                                small_pdf_folder,
                                progress_bar,
                                progress_label,
                                index=i+spec_idx-1,
                                spec_idx=spec_idx,
                                total_rows=total_rows,
                                progress_callback=progress_callback,
                                progress_tracker=progress_tracker,
                                folder_config=folder_config,
                                total_stats=total_stats,
                                df=df
                            )
                            progress_tracker.stats['success'] += 1
                        except Exception as e:
                            logging.error(f"处理规格{spec_idx}失败: {e}")
                            progress_tracker.stats['failed'] += 1
                            progress_tracker.update_progress(
                                processed_count,
                                f"规格{spec_idx}处理失败",
                                f"错误: {str(e)[:50]}..."
                            )

                    i = j + 1
                    break
                # 组内行（商品信息为空但属性集有内容）
                elif (not isinstance(next_prod, str) or not next_prod.strip()) and isinstance(next_attr, str) and next_attr.strip():
                    group.append(next_row)
                    j += 1
                else:
                    # 不是本组，单规格处理
                    processed_count += 1
                    progress_tracker.update_progress(
                        processed_count,
                        "处理单规格",
                        f"SKC{skc_display}: {attribute_set[:30] if attribute_set else '未知属性'}..."
                    )

                    try:
                        process_single_spec(
                            row,
                            product_info,
                            target_base_folder,
                            source_folder,
                            small_pdf_folder,
                            progress_bar,
                            progress_label,
                            index=i,
                            spec_idx=1,
                            total_rows=total_rows,
                            progress_callback=progress_callback,
                            progress_tracker=progress_tracker,
                            folder_config=folder_config,
                            total_stats=total_stats,
                            df=df
                        )
                        progress_tracker.stats['success'] += 1
                    except Exception as e:
                        logging.error(f"处理单规格失败: {e}")
                        progress_tracker.stats['failed'] += 1
                        progress_tracker.update_progress(
                            processed_count,
                            "单规格处理失败",
                            f"错误: {str(e)[:50]}..."
                        )

                    i += 1
                    break
            else:
                # 到结尾也没遇到合计，单规格处理
                processed_count += 1
                progress_tracker.update_progress(
                    processed_count,
                    "处理单规格(末尾)",
                    f"SKC{skc_display}: {attribute_set[:30] if attribute_set else '未知属性'}..."
                )

                try:
                    process_single_spec(
                        row,
                        product_info,
                        target_base_folder,
                        source_folder,
                        small_pdf_folder,
                        progress_bar,
                        progress_label,
                        index=i,
                        spec_idx=1,
                        total_rows=total_rows,
                        progress_callback=progress_callback,
                        progress_tracker=progress_tracker,
                        folder_config=folder_config,
                        total_stats=total_stats,
                        df=df,
                        skc_usage_tracker=skc_usage_tracker
                    )
                    progress_tracker.stats['success'] += 1
                except Exception as e:
                    logging.error(f"处理单规格失败: {e}")
                    progress_tracker.stats['failed'] += 1
                    progress_tracker.update_progress(
                        processed_count,
                        "单规格处理失败",
                        f"错误: {str(e)[:50]}..."
                    )

                i += 1
        else:
            # 不是新组起点，跳过
            progress_tracker.update_progress(
                processed_count,
                "跳过空行",
                f"第{i+1}行：非新组起点，跳过",
                "skipped"
            )
            i += 1

    # 处理完成
    progress_tracker.finish_processing()

    # 统计小标PDF匹配情况
    if os.path.exists(small_pdf_folder):
        total_small_pdfs = len([f for f in os.listdir(small_pdf_folder) if f.lower().endswith('.pdf')])
        matched_small_pdfs = progress_tracker.stats.get('small_pdf_matched', 0)

        logging.info(f"📊 小标PDF匹配统计:")
        logging.info(f"   总文件数: {total_small_pdfs}")
        logging.info(f"   已匹配: {matched_small_pdfs}")
        logging.info(f"   未匹配: {total_small_pdfs - matched_small_pdfs}")

        # 如果有未匹配的文件，进行诊断
        if total_small_pdfs - matched_small_pdfs > 0:
            diagnose_unmatched_small_pdfs(small_pdf_folder, matched_small_pdfs, total_small_pdfs)

    # 所有移动完成后，统一重命名小标PDF文件，根据图片命名方式决定小标命名
    logging.info(f"📝 开始统一重命名小标PDF文件，根据图片命名方式决定小标命名...")
    smart_rename_small_pdfs_based_on_images(target_base_folder, df)

    # 智能清理小标PDF文件夹
    check_and_cleanup_small_pdf_folder(df, small_pdf_folder, target_base_folder)

    # 删除大标PDF文件夹（如果存在）
    big_pdf_folder = os.path.join(target_base_folder, "大标")
    if os.path.exists(big_pdf_folder):
        try:
            import shutil
            shutil.rmtree(big_pdf_folder)
            logging.info(f"✅ 已自动删除大标PDF文件夹: {big_pdf_folder}")
        except Exception as e:
            logging.warning(f"删除大标PDF文件夹时出错: {e}")

    # 检查是否有配置输入，只要有操作员或店铺号输入就需要修改总数
    # 注意：日期和时间段现在是自动获取的，所以主要检查用户输入的配置
    operator = folder_config.get("operator", "").strip()
    shop_number = folder_config.get("shop_number", "").strip()

    # 只要用户输入了操作员或店铺号，就触发数量统计
    has_config = any([operator, shop_number])

    # 如果没有用户输入，但有自动获取的日期时间，也可以触发（为了兼容性）
    if not has_config:
        date = folder_config.get("date", "").strip()
        time_period = folder_config.get("time_period", "").strip()
        has_config = any([date, time_period])

    if has_config:
        logging.info("检测到配置输入，开始数量统计...")

        # 获取处理后新创建的文件夹
        current_folders = set()
        if os.path.exists(target_base_folder):
            current_folders = set(os.listdir(target_base_folder))

        newly_created_folders = current_folders - existing_folders
        logging.info(f"本次处理新创建了 {len(newly_created_folders)} 个文件夹")

        # 只统计新创建的文件夹对应的拣货单数量
        folder_quantity_stats = calculate_folder_quantities_from_picking_list(target_base_folder, df, source_folder, newly_created_folders)

        if folder_quantity_stats:
            logging.info(f"✅ 找到 {len(folder_quantity_stats)} 个文件夹的统计数据，显示数量统计弹窗...")
            for folder_name, stats in folder_quantity_stats.items():
                logging.info(f"  📁 {folder_name}: {stats}")

            confirmed_stats = show_quantity_confirmation_dialog(folder_quantity_stats)

            if confirmed_stats is not None:
                # 用户确认了数量，更新文件夹名称中的"总X"部分
                logging.info("用户确认数量，开始更新文件夹名称...")
                update_folder_names_with_confirmed_quantities(target_base_folder, confirmed_stats)
                logging.info("数量统计和文件夹更新完成！")
            else:
                logging.info("用户取消了数量确认，保持原有文件夹名称")
        else:
            logging.warning("❌ 没有找到统计数据，跳过数量确认")
            logging.info("调试信息：检查目标文件夹内容...")
            if os.path.exists(target_base_folder):
                folder_contents = os.listdir(target_base_folder)
                logging.info(f"目标文件夹内容: {folder_contents}")
            else:
                logging.error(f"目标文件夹不存在: {target_base_folder}")
    else:
        logging.info("没有配置输入，跳过数量统计和弹窗")

def process_single_spec(row, product_info, target_base_folder, source_folder, small_pdf_folder, progress_bar, progress_label, index, spec_idx, total_rows, progress_callback=None, progress_tracker=None, folder_config=None, total_stats=None, df=None, skc_usage_tracker=None):
    # 这里就是原process_files主循环内的单规格处理逻辑
    # 用row['属性集']，用product_info（继承首行）
    # 文件夹名建议加_spec{spec_idx}避免重名
    # 其余逻辑同原来
    attribute_set_raw = row.get('属性集')
    quantity = row.get('数量')

    # 清理属性集：去除换行符、制表符和多余空格
    if isinstance(attribute_set_raw, str) and attribute_set_raw.strip():
        attribute_set = str(attribute_set_raw).strip().replace('\n', '').replace('\r', '').replace('\t', ' ')
        attribute_set = ' '.join(attribute_set.split())
        logging.info(f"🧹 清理属性集: '{attribute_set_raw}' -> '{attribute_set}'")
    else:
        attribute_set = attribute_set_raw

    # 加载文件夹命名配置
    if folder_config is None:
        folder_config = load_folder_config()

    # ========== 以下为原有单规格逻辑，attribute_set用row自己的，product_info用传入的 ===========
    # 提取 SKC 编号
    if progress_tracker:
        progress_tracker.update_progress(
            progress_tracker.current_item,
            "提取SKC编号",
            f"正在从商品信息中提取SKC编号..."
        )

    skc_number_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
    if not skc_number_match:
        logging.warning(f"第{index+2}行：未找到SKC编号，跳过")
        if progress_tracker:
            progress_tracker.update_progress(
                progress_tracker.current_item,
                "SKC提取失败",
                f"第{index+2}行：未找到有效的SKC编号",
                "failed"
            )
        return
    skc_base = skc_number_match.group(1)
    # 检查数据是否为空（使用更通用的方法）
    def is_empty_value(value):
        if value is None:
            return True
        if isinstance(value, str) and not value.strip():
            return True
        try:
            # 延迟导入pandas来检查NaN
            pd = lazy_import_pandas()
            if pd and pd.isna(value):
                return True
        except:
            pass
        return False

    if is_empty_value(quantity) or is_empty_value(attribute_set):
        logging.warning(f"第{index+2}行：缺少数量或属性集，跳过")
        if progress_tracker:
            progress_tracker.update_progress(
                progress_tracker.current_item,
                "数据缺失",
                f"第{index+2}行：缺少数量或属性集信息",
                "failed"
            )
        return

    if progress_tracker:
        progress_tracker.update_progress(
            progress_tracker.current_item,
            "识别尺寸",
            f"正在识别属性集: {attribute_set[:30]}..."
        )

    intended_size = get_size_folder(attribute_set, folder_config)
    logging.info(f"第{index+2}行-规格{spec_idx}：属性集 '{attribute_set}' 识别为预期尺寸 '{intended_size}'")

    # 特殊调试：如果包含21.65和26.77，输出详细信息
    if attribute_set and ("21.65" in str(attribute_set) and "26.77" in str(attribute_set)):
        logging.info(f"🔍 围裙调试 - 原始属性集: '{attribute_set}'")
        logging.info(f"🔍 围裙调试 - 识别结果: '{intended_size}'")
        logging.info(f"🔍 围裙调试 - 期望结果: 'apron_55x68cm'")
        if intended_size != "apron_55x68cm":
            logging.warning(f"⚠️ 围裙识别失败！应该识别为 apron_55x68cm 但识别为 {intended_size}")

    # 特殊调试：如果包含two-side和18，输出详细信息
    if attribute_set and ("two-side" in str(attribute_set).lower() and "18" in str(attribute_set)):
        logging.info(f"🔍 双面抱枕调试 - 原始属性集: '{attribute_set}'")
        logging.info(f"🔍 双面抱枕调试 - 识别结果: '{intended_size}'")
        logging.info(f"🔍 双面抱枕调试 - 期望结果: 'two_sided_18x18inch'")
        if intended_size != "two_sided_18x18inch":
            logging.warning(f"⚠️ 双面抱枕识别失败！应该识别为 two_sided_18x18inch 但识别为 {intended_size}")
    # 在源文件夹中查找与SKC相关的图片文件，并根据文件名后缀分组
    if progress_tracker:
        progress_tracker.update_progress(
            progress_tracker.current_item,
            "查找图片文件",
            f"正在查找SKC{skc_base}的图片文件..."
        )

    # 获取目标属性集映射
    target_attribute = get_template_mapping(attribute_set)

    # 从目标属性集中提取pc数和尺寸信息
    template_pc_count, size_type, size_dimensions = extract_pc_and_size_from_target(target_attribute)

    # 从实际图片文件中检测pc数
    actual_jpg_count, actual_pc_count = count_images_and_pc(source_folder, skc_base)

    # 优先使用实际检测到的pc数，如果检测失败则使用模板中的pc数
    if actual_pc_count > 0:
        pc_count = actual_pc_count
        logging.info(f"🔍 使用实际检测的pc数: {actual_pc_count}pc (模板中为{template_pc_count}pc)")
    else:
        pc_count = template_pc_count
        logging.info(f"🔍 使用模板中的pc数: {template_pc_count}pc (实际检测失败)")

    logging.info(f"🔍 SKC {skc_base} 属性集: '{attribute_set}' -> 目标: '{target_attribute}'")
    logging.info(f"🔍 最终信息: pc数={pc_count}, 尺寸类型={size_type}, 图片数量={actual_jpg_count}")

    # 🔧 新增：详细记录属性集匹配信息，用于调试多PC/单PC混合问题
    logging.info(f"🔍 [PC匹配调试] 当前属性集: '{attribute_set}'")
    logging.info(f"🔍 [PC匹配调试] 目标属性集: '{target_attribute}'")
    logging.info(f"🔍 [PC匹配调试] 模板PC数: {template_pc_count}, 实际PC数: {actual_pc_count}, 最终PC数: {pc_count}")

    # 检查PC数是否匹配，如果不匹配给出警告
    if template_pc_count != actual_pc_count and actual_pc_count > 0:
        logging.warning(f"⚠️ [PC匹配警告] PC数不匹配！模板: {template_pc_count}pc, 实际: {actual_pc_count}pc")
        logging.warning(f"⚠️ [PC匹配警告] 这可能导致多PC图片被错误归类到单PC文件夹中")

    image_groups = {
        "30x40cm": [], # 无特定后缀的默认为 30x40cm，或者-1,-2,-3后缀
        "40x40cm": [], # 文件名包含 -40x40.jpg
        "40x60cm": []  # 文件名包含 -40x60.jpg
    }
    all_related_images = []

    # 首先尝试查找SKC命名的图片 - 使用老逻辑但保留属性集匹配，支持递归搜索子文件夹
    if os.path.isdir(source_folder):
        # 递归搜索源文件夹及其子文件夹
        for root, dirs, files in os.walk(source_folder):
            for filename in files:
                file_path = os.path.join(root, filename)
                # 检查文件名是否以 skc_base 开头，且以 .jpg 结尾 (忽略大小写)
                if filename.lower().startswith(skc_base.lower()) and filename.lower().endswith('.jpg'):
                    logging.info(f"🔍 找到匹配图片: {file_path}")  # 添加调试信息

                    # 判断是传统SKC命名还是新SKU货号命名
                    # 传统SKC命名：文件名以纯数字SKC开头，且符合SKC命名格式
                    filename_without_ext = filename.lower().replace('.jpg', '')

                    # 检查是否为传统SKC命名格式
                    is_traditional_skc = False
                    if skc_base.isdigit() and filename.lower().startswith(skc_base.lower()):
                        # 检查各种传统SKC命名格式
                        skc_patterns = [
                            rf'^{re.escape(skc_base.lower())}$',                    # 37892931206
                            rf'^{re.escape(skc_base.lower())}-\d+$',               # 37892931206-1
                            rf'^{re.escape(skc_base.lower())}-\d+-\d+x\d+$',       # 37892931206-1-40x60
                            rf'^{re.escape(skc_base.lower())}-\d+x\d+$',           # 37892931206-40x60
                        ]

                        for pattern in skc_patterns:
                            if re.match(pattern, filename_without_ext):
                                is_traditional_skc = True
                                break

                    if is_traditional_skc:
                        # 传统SKC命名（纯数字，文件名不含字母）：区分单规格和多规格处理
                        logging.info(f"🔍 识别为传统SKC命名: {filename}")

                        # 检查是否有后缀（多规格）
                        has_suffix = bool(re.search(r'-\d+', filename) or
                                        "-40x60" in filename.lower() or "_40x60" in filename.lower() or
                                        "-40x40" in filename.lower() or "_40x40" in filename.lower())

                        if has_suffix:
                            # 多规格图片：使用复杂匹配逻辑，按文件名后缀分组
                            # 🔧 改进：增加PC数验证，确保多PC图片不会进入单PC文件夹
                            if "-40x60" in filename.lower() or "_40x60" in filename.lower():
                                image_groups["40x60cm"].append(file_path)
                                logging.info(f"🔍 [图片分组] 40x60cm: {filename}")
                            elif "-40x40" in filename.lower() or "_40x40" in filename.lower():
                                image_groups["40x40cm"].append(file_path)
                                logging.info(f"🔍 [图片分组] 40x40cm: {filename}")
                            elif re.search(r'-[1-4]\.jpg', filename.lower()):
                                # -1,-2,-3,-4后缀的图片归类为30x40cm
                                image_groups["30x40cm"].append(file_path)
                                logging.info(f"🔍 [图片分组] 30x40cm (序号): {filename}")
                            else:
                                # 其他有后缀的图片，默认为30x40cm
                                image_groups["30x40cm"].append(file_path)
                                logging.info(f"🔍 [图片分组] 30x40cm (其他): {filename}")
                        else:
                            # 单规格图片（没有后缀）：使用简单匹配逻辑，直接加入all_related_images
                            all_related_images.append(file_path)
                            logging.info(f"🔍 传统SKC单规格图片（简单匹配）: {filename}")
                            # 同时也加入30x40cm组以保持兼容性
                            image_groups["30x40cm"].append(file_path)
                    else:
                        # 新SKU货号命名（包含字母）：直接匹配，不进行分组
                        logging.info(f"🔍 识别为新SKU货号命名: {filename}")
                        if is_image_matching_attribute_set(filename, attribute_set):
                            all_related_images.append(file_path)
                            logging.info(f"✅ SKU货号图片直接匹配（不分组）: {filename} -> {attribute_set}")
                        else:
                            logging.info(f"❌ SKU货号图片不匹配属性集: {filename} -> {attribute_set}")

    # 检查 intended_size 是否是完整的文件夹名称（包含特级JIT等字符）
    is_full_folder_name = "特级JIT" in intended_size or "木框帆布画" in intended_size or len(intended_size) > 50

    # 对于传统SKC命名，根据当前预期尺寸将对应的图片加入all_related_images
    if skc_base.isdigit():
        logging.info(f"🔍 [调试] 传统SKC命名检查:")
        logging.info(f"    - intended_size: {intended_size}")
        logging.info(f"    - is_full_folder_name: {is_full_folder_name}")
        logging.info(f"    - image_groups keys: {list(image_groups.keys())}")
        logging.info(f"    - all_related_images (单规格): {len(all_related_images)} 张")
        for size, images in image_groups.items():
            logging.info(f"    - {size}: {len(images)} 张图片 {[os.path.basename(img) for img in images]}")

        # 如果已经有单规格图片，直接使用简单匹配逻辑
        if all_related_images:
            logging.info(f"🔍 传统SKC单规格：使用简单匹配逻辑，共 {len(all_related_images)} 张图片")
            logging.info(f"    - 单规格图片: {[os.path.basename(img) for img in all_related_images]}")
        elif is_full_folder_name:
            # intended_size 是完整文件夹名称，使用所有找到的图片
            total_images = sum(len(images) for images in image_groups.values())
            if total_images > 0:
                # 将所有分组的图片合并到 all_related_images
                for images in image_groups.values():
                    all_related_images.extend(images)
                logging.info(f"🔍 传统SKC（完整文件夹名）：使用所有找到的图片，共 {len(all_related_images)} 张")
                logging.info(f"    - 合并的图片: {[os.path.basename(img) for img in all_related_images]}")
            else:
                logging.warning(f"🔍 传统SKC（完整文件夹名）：未找到任何SKC命名的图片，将尝试查找SKU货号命名的图片")
        else:
            # 多规格图片：使用复杂匹配逻辑
            # 🔧 修复：从完整的文件夹名称中提取实际的尺寸部分
            actual_size = None
            if intended_size:
                if "30x40cm" in intended_size:
                    actual_size = "30x40cm"
                elif "40x60cm" in intended_size:
                    actual_size = "40x60cm"
                elif "40x40cm" in intended_size:
                    actual_size = "40x40cm"

            logging.info(f"🔧 [修复] 从文件夹名称 '{intended_size}' 中提取实际尺寸: {actual_size}")

            # 🔧 新增：PC数验证逻辑，确保多PC图片不会进入单PC文件夹
            if actual_size and actual_size in image_groups and image_groups[actual_size]:
                # 检查当前属性集的PC数要求
                expected_pc_count = pc_count
                actual_image_count = len(image_groups[actual_size])

                logging.info(f"🔍 [PC验证] 期望PC数: {expected_pc_count}, 实际图片数: {actual_image_count}")

                # 如果PC数不匹配，给出警告但仍然处理（避免过于严格导致无法处理）
                if expected_pc_count > 1 and actual_image_count == 1:
                    logging.warning(f"⚠️ [PC验证警告] 期望多PC({expected_pc_count}pc)但只找到1张图片，可能存在图片缺失")
                elif expected_pc_count == 1 and actual_image_count > 1:
                    logging.warning(f"⚠️ [PC验证警告] 期望单PC(1pc)但找到{actual_image_count}张图片，可能存在图片混合")

                all_related_images = image_groups[actual_size].copy()  # 只使用当前尺寸的图片
                logging.info(f"🔍 传统SKC多规格：将 {len(image_groups[actual_size])} 张 {actual_size} 尺寸的图片加入处理列表")
                logging.info(f"    - 选中的图片: {[os.path.basename(img) for img in all_related_images]}")
            else:
                # 严格模式：如果没有找到对应尺寸的图片，直接跳过，不使用回退逻辑
                all_related_images = []
                logging.warning(f"🔍 传统SKC多规格：未找到 {actual_size} 尺寸的图片，跳过此属性集以避免图片混合")
                logging.warning(f"🚨 严格模式：不使用回退逻辑，确保图片不会被错误移动")
                logging.warning(f"    - actual_size: {actual_size}")
                logging.warning(f"    - actual_size in image_groups: {actual_size in image_groups if actual_size else False}")
                if actual_size and actual_size in image_groups:
                    logging.warning(f"    - image_groups[{actual_size}] 长度: {len(image_groups[actual_size])}")
                if not is_full_folder_name:
                    return  # 只有在非完整文件夹名称时返回，完整文件夹名称时继续尝试SKU货号查找

    # 如果没有找到SKC命名的图片，尝试查找SKU货号命名的图片
    if not all_related_images and df is not None:
        logging.info(f"🔍 未找到SKC {skc_base} 命名的图片，尝试查找SKU货号命名的图片...")
        skc_to_sku_list, sku_to_skc, skc_to_attribute_sets, sku_to_attribute_sets = build_skc_sku_mapping(df)

        if skc_base in skc_to_sku_list:
            sku_list = skc_to_sku_list[skc_base]
            logging.info(f"🔍 找到对应SKU货号列表: {sku_list}")

            # 查找所有SKU货号命名的图片，但只处理匹配当前属性集的SKU货号
            for sku_hao in sku_list:
                # 检查这个SKU货号是否对应当前属性集
                if sku_hao in sku_to_attribute_sets:
                    sku_attribute_sets = sku_to_attribute_sets[sku_hao]
                    # 检查当前属性集是否在这个SKU货号的属性集列表中
                    if attribute_set in sku_attribute_sets:
                        sku_images = find_images_by_sku_hao(source_folder, sku_hao)
                        if sku_images:
                            logging.info(f"✅ 找到 {len(sku_images)} 张SKU货号 {sku_hao} 命名的图片，匹配当前属性集 {attribute_set}")
                            for file_path in sku_images:
                                filename = os.path.basename(file_path)
                                all_related_images.append(file_path)

                                # SKU货号命名的图片直接匹配，不进行分组
                                logging.info(f"📏 SKU货号图片直接匹配（不分组）: {filename}")
                        else:
                            logging.info(f"ℹ️ 未找到SKU货号 {sku_hao} 命名的图片")
                    else:
                        logging.info(f"🔍 SKU货号 {sku_hao} 不对应当前属性集 {attribute_set}，跳过")
                else:
                    logging.info(f"🔍 SKU货号 {sku_hao} 没有属性集映射信息，跳过")

            if not all_related_images:
                logging.warning(f"❌ 没有找到匹配当前属性集 {attribute_set} 的SKU货号图片")
        else:
            logging.warning(f"❌ 未找到SKC {skc_base} 对应的SKU货号")

    total_image_count = len(all_related_images)
    if total_image_count > 0:
        logging.info(f"第{index+2}行：找到 {total_image_count} 张相关图片")
    else:
        logging.warning(f"第{index+2}行：未找到任何相关图片")
    for size, paths in image_groups.items():
         if paths:
             logging.info(f"  - {size} 组找到 {len(paths)} 张图片")

    # 获取数量部分的字符串 (X套 或 X张) - 恢复原来的逻辑
    try:
        quantity_num = int(float(quantity))
        # 恢复原来的命名逻辑：基于图片数量判断
        quantity_subfolder_name = f"{quantity_num}套" if total_image_count > 1 else f"{quantity_num}张"
    except (ValueError, TypeError):
        logging.warning(f"第{index+2}行：数量 '{quantity}' 无效，跳过")
        return

    # 根据预期的尺寸和对应的图片数量构建主文件夹名称
    images_to_copy = [] # 需要复制的图片列表
    count_for_intended_size = 0

    # ====== 黑框/金框特殊命名逻辑 ======
    if intended_size == "black_30x40":
        size_for_image_match = "30x40cm"
        logging.info(f"第{index+2}行：属性集匹配 '11.8x15.7inch-H'，识别为黑框")
        if size_for_image_match in image_groups:
            images_to_copy = image_groups[size_for_image_match]
            count_for_intended_size = len(images_to_copy)
    elif intended_size == "gold_30x40":
        size_for_image_match = "30x40cm"
        logging.info(f"第{index+2}行：属性集匹配 'G-11.8x15.7inch'，识别为金框")
        if size_for_image_match in image_groups:
            images_to_copy = image_groups[size_for_image_match]
            count_for_intended_size = len(images_to_copy)
    # ====== 18x18inch 特殊命名逻辑 ======
    elif intended_size == "18x18inch":
        # 统一命名为正方形抱枕格式
        images_to_copy = all_related_images  # 复制所有相关图片
        count_for_intended_size = total_image_count
        logging.info(f"第{index+2}行：属性集匹配 '18x18inch'，识别为正方形抱枕")
    # ====== 新增特殊尺寸逻辑 ======
    elif intended_size == "12inch":
        # 12inch木板钟
        images_to_copy = all_related_images  # 复制所有相关图片
        count_for_intended_size = total_image_count
        logging.info(f"第{index+2}行：属性集匹配 '12inch'，识别为木板钟")
    elif intended_size == "apron_55x68cm":
        # 围裙
        images_to_copy = all_related_images  # 复制所有相关图片
        count_for_intended_size = total_image_count
        logging.info(f"第{index+2}行：属性集匹配 '21.65x26.77inch'，识别为围裙55x68cm")
    elif intended_size == "two_sided_18x18inch":
        # 双面抱枕
        images_to_copy = all_related_images  # 复制所有相关图片
        count_for_intended_size = total_image_count
        logging.info(f"第{index+2}行：属性集匹配 'Two-sided 18x18inch'，识别为双面抱枕45x45cm")
    # ====== 原有逻辑 ======
    elif intended_size != "unknown" and not is_full_folder_name and intended_size in image_groups:
        # 传统SKC命名：如果识别到标准尺寸 - 保持6.13的核心逻辑
        count_for_intended_size = len(image_groups[intended_size])
        if count_for_intended_size > 0:
             # 如果该尺寸分组有图片，则确定要复制的图片
             images_to_copy = image_groups[intended_size] # 复制该尺寸分组的图片
             logging.info(f"第{index+2}行：传统SKC命名，根据预期尺寸 '{intended_size}' 和图片数量 {count_for_intended_size} 构建文件夹名")
             logging.info(f"🔍 将复制 {len(images_to_copy)} 张 {intended_size} 尺寸的图片: {[os.path.basename(img) for img in images_to_copy]}")
        else:
             # 如果该尺寸分组没有图片，记录警告但不回退到所有图片
             logging.warning(f"第{index+2}行：传统SKC命名，预期尺寸 '{intended_size}' 分组没有找到图片，跳过此属性集。")
             return  # 直接跳过，不创建文件夹

    elif all_related_images: # 新命名格式、完整文件夹名或其他情况：如果有相关图片，直接使用
         if is_full_folder_name and intended_size != "unknown" and intended_size in image_groups:
             # 完整文件夹名称但有明确尺寸要求：只复制匹配尺寸的图片
             images_to_copy = image_groups[intended_size]
             count_for_intended_size = len(images_to_copy)
             logging.info(f"第{index+2}行：完整文件夹名称，但根据属性集要求只复制 {intended_size} 尺寸的图片，共 {count_for_intended_size} 张。")
             logging.info(f"🔍 将复制 {len(images_to_copy)} 张 {intended_size} 尺寸的图片: {[os.path.basename(img) for img in images_to_copy]}")
         else:
             # 使用总图片数量
             images_to_copy = all_related_images # 复制所有相关图片
             count_for_intended_size = total_image_count
             if is_full_folder_name:
                 logging.info(f"第{index+2}行：完整文件夹名称，使用总图片数量 {total_image_count}，复制所有相关图片。")
             else:
                 logging.info(f"第{index+2}行：新命名格式或其他情况，使用总图片数量 {total_image_count}，复制所有相关图片。")
         logging.info(f"🔍 调试：all_related_images 数量: {len(all_related_images)}")
         logging.info(f"🔍 调试：images_to_copy 数量: {len(images_to_copy)}")
    else:
         # 没有找到任何相关图片
         logging.warning(f"第{index+2}行：未找到与SKC '{skc_base}' 相关的任何图片，跳过文件复制和文件夹创建")
         logging.warning(f"🔍 调试：all_related_images 为空: {len(all_related_images) if all_related_images else 0}")
         logging.warning(f"🔍 调试：image_groups 内容: {[(k, len(v)) for k, v in image_groups.items()]}")
         logging.warning(f"🔍 调试：is_full_folder_name: {is_full_folder_name}")
         return # 跳过此行的后续处理

    # 生成文件夹名称 - 支持简单命名和复杂命名，传入实际pc数
    logging.info(f"🔍 调试：准备生成文件夹名称")
    logging.info(f"🔍 调试：intended_size={intended_size}, count_for_intended_size={count_for_intended_size}")
    logging.info(f"🔍 调试：attribute_set={attribute_set}, pc_count={pc_count}")
    logging.info(f"🔍 调试：is_full_folder_name={is_full_folder_name}")

    if is_full_folder_name:
        # 如果 intended_size 已经是完整的文件夹名称，直接使用
        main_folder_name = intended_size
        logging.info(f"第{index+2}行：使用完整文件夹名称: {main_folder_name}")
    else:
        # 否则通过函数生成文件夹名称
        main_folder_name = generate_folder_name(intended_size, count_for_intended_size, folder_config, total_stats, False, attribute_set, pc_count)
        logging.info(f"第{index+2}行：生成文件夹名称: {main_folder_name}")

    if not main_folder_name or main_folder_name.strip() == "":
        logging.error(f"❌ 文件夹名称生成失败，为空或无效")
        return

    # 构建完整的目标路径（确保路径格式正确）
    main_folder_path = os.path.normpath(os.path.join(target_base_folder, main_folder_name))
    quantity_folder_path = os.path.normpath(os.path.join(main_folder_path, quantity_subfolder_name))
    images_target_folder = quantity_folder_path # 图片直接放到数量文件夹下
    small_label_target_folder = os.path.normpath(os.path.join(quantity_folder_path, '小标')) # 小标PDF放到数量文件夹下的小标子文件夹

    # 记录路径信息用于调试
    logging.info(f"第{index+2}行：路径构建完成")
    logging.info(f"  主文件夹路径: {main_folder_path}")
    logging.info(f"  数量文件夹路径: {quantity_folder_path}")
    logging.info(f"  小标文件夹路径: {small_label_target_folder}")

    # 创建必要的文件夹 (确保按层级创建)
    if progress_tracker:
        progress_tracker.update_progress(
            progress_tracker.current_item,
            "创建文件夹",
            f"正在创建文件夹: {main_folder_name}"
        )

    try:
        # 确保路径格式正确，处理中文字符
        main_folder_path = os.path.normpath(main_folder_path)
        quantity_folder_path = os.path.normpath(quantity_folder_path)
        small_label_target_folder = os.path.normpath(small_label_target_folder)

        # 检查路径长度，避免Windows路径长度限制
        if len(main_folder_path) > 250:
            logging.warning(f"路径过长，可能导致创建失败: {main_folder_path}")

        os.makedirs(main_folder_path, exist_ok=True)
        logging.info(f"✅ 成功创建主文件夹: {main_folder_path}")

        os.makedirs(quantity_folder_path, exist_ok=True)
        logging.info(f"✅ 成功创建数量文件夹: {quantity_folder_path}")

        os.makedirs(small_label_target_folder, exist_ok=True)
        logging.info(f"✅ 成功创建小标文件夹: {small_label_target_folder}")

    except Exception as e:
        logging.error(f"❌ 创建文件夹失败: {e}")
        logging.error(f"   主文件夹路径: {main_folder_path}")
        logging.error(f"   数量文件夹路径: {quantity_folder_path}")
        logging.error(f"   小标文件夹路径: {small_label_target_folder}")

        # 尝试使用简化的文件夹名称
        safe_main_folder_name = re.sub(r'[^\w\s-]', '_', main_folder_name)
        safe_main_folder_path = os.path.join(target_base_folder, safe_main_folder_name)
        safe_quantity_folder_path = os.path.join(safe_main_folder_path, quantity_subfolder_name)
        safe_small_label_target_folder = os.path.join(safe_quantity_folder_path, '小标')

        try:
            os.makedirs(safe_main_folder_path, exist_ok=True)
            os.makedirs(safe_quantity_folder_path, exist_ok=True)
            os.makedirs(safe_small_label_target_folder, exist_ok=True)

            # 更新路径变量
            main_folder_path = safe_main_folder_path
            quantity_folder_path = safe_quantity_folder_path
            small_label_target_folder = safe_small_label_target_folder

            logging.info(f"✅ 使用安全路径创建成功: {safe_main_folder_path}")

        except Exception as e2:
            logging.error(f"❌ 使用安全路径也创建失败: {e2}")
            return  # 如果还是失败，跳过此行处理

    # 复制筛选后的图片到目标文件夹
    if not images_to_copy:
         logging.warning(f"第{index+2}行：没有图片需要复制到 '{images_target_folder}'")
         if progress_tracker:
             progress_tracker.update_progress(
                 progress_tracker.current_item,
                 "无图片复制",
                 f"SKC{skc_base}没有找到需要复制的图片"
             )
         # 即使没有图片，也要复制小标PDF
    else:
        if progress_tracker:
            progress_tracker.update_progress(
                progress_tracker.current_item,
                "复制图片",
                f"正在复制{len(images_to_copy)}张图片..."
            )

        for i, img_path in enumerate(images_to_copy, 1):
            if progress_tracker:
                progress_tracker.update_progress(
                    progress_tracker.current_item,
                    f"复制图片 {i}/{len(images_to_copy)}",
                    f"正在复制: {os.path.basename(img_path)}"
                )

            target_img_filename = os.path.basename(img_path)
            target_img_path = os.path.join(images_target_folder, target_img_filename)

            # 🚨 严格过滤器：确保图片尺寸与预期尺寸匹配，防止图片混合
            should_move_image = True

            # 检查是否为单规格图片（没有后缀）
            is_single_spec = not bool(re.search(r'-\d+', target_img_filename) or
                                    "-40x60" in target_img_filename.lower() or "_40x60" in target_img_filename.lower() or
                                    "-40x40" in target_img_filename.lower() or "_40x40" in target_img_filename.lower())

            # 检查是否为新命名格式（真正的SKU货号命名）
            filename_without_ext = target_img_filename.lower().replace('.jpg', '')
            is_new_sku_naming = is_valid_sku_hao(filename_without_ext)

            if is_single_spec:
                # 单规格图片：使用简单匹配逻辑，不进行严格过滤
                should_move_image = True
                logging.info(f"🔍 单规格图片（简单匹配）: {target_img_filename}")
            elif is_new_sku_naming:
                # 新SKU货号命名：已通过属性集匹配，跳过严格过滤
                should_move_image = True
                logging.info(f"🔍 新SKU货号命名（已通过属性集匹配）: {target_img_filename}")
            elif intended_size and skc_base and skc_base.isdigit():
                # 传统SKC命名的多规格图片：使用严格过滤器
                # 🔧 修复：从完整的文件夹名称中提取实际的尺寸部分
                actual_size_for_filter = None
                if "30x40cm" in intended_size:
                    actual_size_for_filter = "30x40cm"
                elif "40x60cm" in intended_size:
                    actual_size_for_filter = "40x60cm"
                elif "40x40cm" in intended_size:
                    actual_size_for_filter = "40x40cm"

                # 对于传统SKC命名，检查图片尺寸是否匹配预期尺寸
                if actual_size_for_filter == "30x40cm":
                    # 30x40cm应该是没有特定尺寸后缀的图片，或者有-1,-2,-3后缀的图片
                    if "-40x60" in target_img_filename.lower() or "_40x60" in target_img_filename.lower():
                        should_move_image = False
                        logging.warning(f"🚨 严格过滤器：跳过40x60cm图片 {target_img_filename}，当前处理30x40cm属性集")
                    elif "-40x40" in target_img_filename.lower() or "_40x40" in target_img_filename.lower():
                        should_move_image = False
                        logging.warning(f"🚨 严格过滤器：跳过40x40cm图片 {target_img_filename}，当前处理30x40cm属性集")
                elif actual_size_for_filter == "40x60cm":
                    # 40x60cm应该是有-40x60或_40x60后缀的图片
                    if not ("-40x60" in target_img_filename.lower() or "_40x60" in target_img_filename.lower()):
                        should_move_image = False
                        logging.warning(f"🚨 严格过滤器：跳过非40x60cm图片 {target_img_filename}，当前处理40x60cm属性集")
                elif actual_size_for_filter == "40x40cm":
                    # 40x40cm应该是有-40x40或_40x40后缀的图片
                    if not ("-40x40" in target_img_filename.lower() or "_40x40" in target_img_filename.lower()):
                        should_move_image = False
                        logging.warning(f"🚨 严格过滤器：跳过非40x40cm图片 {target_img_filename}，当前处理40x40cm属性集")
            else:
                # 新命名格式（SKU货号）或其他情况：允许复制，因为已经通过了属性集匹配
                should_move_image = True
                logging.info(f"🔍 新命名格式或其他格式图片（已通过属性集匹配）: {target_img_filename}")

            if not should_move_image:
                logging.warning(f"🚨 严格过滤器：跳过图片 {target_img_filename}，尺寸不匹配预期尺寸 {intended_size}")
                continue

            if os.path.exists(img_path):
                 if not os.path.exists(target_img_path):
                     try:
                         shutil.copy2(img_path, target_img_path)  # 改为复制操作
                         logging.info(f"✅ 成功复制图片: {target_img_filename} (尺寸匹配: {intended_size})")
                     except Exception as e:
                         logging.error(f"复制图片失败：{img_path} 到 {target_img_path}，原因：{e}")
                 else:
                     logging.warning(f"目标图片已存在，跳过：{target_img_path}")
            else:
                 logging.warning(f"图片未找到：{img_path}")

    # 查找并复制对应的小标PDF文件 (使用新的匹配逻辑)
    if progress_tracker:
        progress_tracker.update_progress(
            progress_tracker.current_item,
            "查找小标PDF",
            f"正在查找SKC{skc_base}的小标PDF文件..."
        )

    found_and_copied_small_pdf = False

    # 检查SKC是否已经被其他文件夹使用
    if skc_usage_tracker and skc_base in skc_usage_tracker:
        existing_usage = skc_usage_tracker[skc_base]
        logging.warning(f"⚠️ SKC {skc_base} 已被使用:")
        logging.warning(f"   已存在文件夹: {existing_usage['folder']}")
        logging.warning(f"   已存在属性集: {existing_usage['attribute_set']}")
        logging.warning(f"   已存在PDF文件: {existing_usage['pdf_file']}")
        logging.warning(f"   当前属性集: {attribute_set}")
        logging.warning(f"   跳过当前处理，避免重复")
        return  # 跳过处理，避免SKC重复出现在多个文件夹

    # 查找所有包含相同SKC的目标文件夹
    all_target_folders = find_folders_with_skc(target_base_folder, skc_base)

    # 🔥 重要修复：当匹配到多个文件夹时，根据当前属性集选择正确的文件夹
    if len(all_target_folders) > 1:
        logging.info(f"🔍 匹配到多个文件夹 ({len(all_target_folders)} 个)，根据当前属性集选择: {attribute_set}")

        # 🔥 根据文件夹数量决定是否需要属性集判断
        if len(all_target_folders) == 1:
            # 只有一个文件夹，直接使用，不需要属性集判断
            target_folders = all_target_folders
            logging.info(f"✅ 唯一文件夹，直接使用: {os.path.basename(all_target_folders[0])}")
        else:
            # 有多个文件夹，需要根据属性集进行判断
            logging.info(f"🔍 找到 {len(all_target_folders)} 个文件夹，开始属性集判断...")
            filtered_folders = []
            for folder_path in all_target_folders:
                folder_name = os.path.basename(folder_path)

                # 🔥 智能匹配：从属性集中提取尺寸信息进行匹配
                is_match = False
                if attribute_set:
                    # 获取属性集对应的映射结果
                    target_mapping = get_template_mapping(attribute_set)
                    if target_mapping:
                        # 检查文件夹名是否包含映射结果的关键信息
                        # 提取映射结果中的尺寸信息进行匹配
                        mapping_size = extract_size_from_folder_name(target_mapping)
                        folder_size = extract_size_from_folder_name(folder_name)

                        if mapping_size and folder_size:
                            # 比较尺寸
                            if normalize_size_format(mapping_size) == normalize_size_format(folder_size):
                                is_match = True
                                logging.info(f"✅ 文件夹匹配当前属性集 (尺寸匹配: {mapping_size} == {folder_size}): {folder_name}")
                            else:
                                logging.info(f"❌ 文件夹不匹配当前属性集 (尺寸不匹配: {mapping_size} != {folder_size}): {folder_name}")
                        else:
                            # 如果无法提取尺寸，使用字符串包含匹配
                            if any(part in folder_name for part in target_mapping.split() if len(part) > 3):
                                is_match = True
                                logging.info(f"✅ 文件夹匹配当前属性集 (字符串匹配): {folder_name}")
                            else:
                                logging.info(f"❌ 文件夹不匹配当前属性集 (字符串不匹配): {folder_name}")
                    else:
                        # 如果无法获取映射，使用原始属性集进行匹配
                        if attribute_set in folder_name:
                            is_match = True
                            logging.info(f"✅ 文件夹匹配当前属性集 (直接匹配): {folder_name}")
                        else:
                            logging.info(f"❌ 文件夹不匹配当前属性集 (直接不匹配): {folder_name}")

                if is_match:
                    filtered_folders.append(folder_path)

            if filtered_folders:
                target_folders = filtered_folders
                logging.info(f"✅ 过滤后的目标文件夹: {len(target_folders)} 个")
            else:
                # 如果过滤后没有匹配的文件夹，使用第一个文件夹（保守策略）
                target_folders = [all_target_folders[0]]
                logging.warning(f"❌ 过滤后没有匹配的文件夹，使用第一个: {os.path.basename(all_target_folders[0])}")
    else:
        target_folders = all_target_folders
        logging.info(f"✅ 单个目标文件夹: {len(target_folders)} 个")

    # 🔥🔥🔥 强制使用新的先移动再合并逻辑 🔥🔥🔥
    logging.info(f"🔥🔥🔥 [强制确认] 开始执行新的小标PDF处理逻辑：先移动再合并")

    # 🔥🔥🔥 调试：检查小标PDF文件夹路径
    logging.info(f"🔥🔥🔥 [调试] 小标PDF文件夹路径: {small_pdf_folder}")
    logging.info(f"🔥🔥🔥 [调试] 小标PDF文件夹是否存在: {os.path.exists(small_pdf_folder)}")
    logging.info(f"🔥🔥🔥 [调试] 小标PDF文件夹是否为目录: {os.path.isdir(small_pdf_folder)}")
    if os.path.exists(small_pdf_folder):
        files_in_folder = os.listdir(small_pdf_folder)
        pdf_files = [f for f in files_in_folder if f.lower().endswith('.pdf')]
        logging.info(f"🔥🔥🔥 [调试] 小标PDF文件夹中的PDF文件: {pdf_files}")

    # 调用新的小标PDF处理函数
    found_and_copied_small_pdf = process_small_pdf_move_then_merge(
        small_pdf_folder, skc_base, target_folders, attribute_set, df,
        small_label_target_folder, progress_tracker, index
    )

    if not found_and_copied_small_pdf:
         logging.warning(f"第{index+2}行：未找到与SKC '{skc_base}' 匹配的小标PDF文件进行移动")
         logging.warning(f"   属性集: {attribute_set}")
         logging.warning(f"   目标文件夹: {small_label_target_folder}")

         # 列出小标PDF文件夹中所有匹配SKC的文件
         if os.path.exists(small_pdf_folder):
             matching_files = []
             for filename in os.listdir(small_pdf_folder):
                 if filename.lower().endswith('.pdf') and skc_base in filename:
                     matching_files.append(filename)
             if matching_files:
                 logging.warning(f"   可用的SKC匹配文件: {matching_files}")
             else:
                 logging.warning(f"   小标PDF文件夹中没有匹配SKC {skc_base} 的文件")

         if progress_tracker:
             progress_tracker.update_progress(
                 progress_tracker.current_item,
                 "小标PDF缺失",
                 f"SKC{skc_base}未找到对应的小标PDF文件"
             )

    # 更新进度 - 这部分放在外层循环，更新总进度
    progress_value = (index + 1) / total_rows * 100

    # 优先使用progress_callback（现代化UI）
    if progress_callback:
        try:
            progress_callback(progress_value)
            logging.debug(f"通过callback更新进度: {progress_value:.2f}%")
        except Exception as e:
            logging.warning(f"通过callback更新进度失败: {e}")
    else:
        # 兼容不同类型的进度条（传统UI）
        try:
            # 尝试现代化UI的方式（ttkbootstrap）
            if hasattr(progress_bar, 'variable') and progress_bar.variable:
                progress_bar.variable.set(progress_value)
                logging.debug(f"通过variable更新进度: {progress_value:.2f}%")
            # 尝试字典式访问（传统UI）
            elif hasattr(progress_bar, '__setitem__'):
                progress_bar['value'] = progress_value
                logging.debug(f"通过字典更新进度: {progress_value:.2f}%")
            # 尝试直接设置value属性
            elif hasattr(progress_bar, 'value'):
                progress_bar.value = progress_value
                logging.debug(f"通过属性更新进度: {progress_value:.2f}%")

            # 更新进度条显示
            if hasattr(progress_bar, 'update'):
                progress_bar.update()

        except Exception as e:
            logging.warning(f"更新进度条失败: {e}")

    # 更新进度标签
    if progress_label:
        try:
            progress_label.config(text=f"{progress_value:.2f}%")
        except Exception as e:
            logging.warning(f"更新进度标签失败: {e}")

def clean_folder_name(name):
    """
    清理文件夹名字中的非法字符并限制长度
    """
    if not name:
        return ""

    # 清理非法字符，包括Windows不允许的字符
    cleaned = re.sub(r'[<>:"/\\|?*\n\r]', '', str(name)).strip()

    # 替换一些常见的问题字符
    cleaned = cleaned.replace('/', '-')  # 斜杠替换为横杠
    cleaned = cleaned.replace('\\', '-')  # 反斜杠替换为横杠
    cleaned = cleaned.replace(':', '-')   # 冒号替换为横杠
    cleaned = cleaned.replace('*', '')    # 星号删除
    cleaned = cleaned.replace('?', '')    # 问号删除
    cleaned = cleaned.replace('"', '')    # 双引号删除
    cleaned = cleaned.replace('<', '')    # 小于号删除
    cleaned = cleaned.replace('>', '')    # 大于号删除
    cleaned = cleaned.replace('|', '')    # 竖线删除

    # 限制长度（Windows文件夹名称限制）
    max_length = 200
    if len(cleaned) > max_length:
        cleaned = cleaned[:max_length-3] + "..."

    return cleaned

def calculate_individual_stats_from_folders(target_base_folder):
    """
    从已创建的文件夹中统计各属性集的独立信息

    新的统计逻辑：
    - 检查各个属性集下面的数量文件夹
    - 2张文件夹里1个jpg = 2张，3套文件夹里1个jpg = 3套
    - 各属性集独立统计，不全局汇总
    - 单pc显示张，多pc显示套

    Args:
        target_base_folder: 目标基础文件夹路径

    Returns:
        dict: 各属性集的统计信息
    """
    attribute_stats = {}

    if not os.path.exists(target_base_folder):
        return {}

    # 遍历所有主文件夹（属性集文件夹）
    for main_folder_name in os.listdir(target_base_folder):
        main_folder_path = os.path.join(target_base_folder, main_folder_name)

        if not os.path.isdir(main_folder_path):
            continue

        # 跳过特殊文件夹
        if main_folder_name in ['小标PDF', '大标']:
            continue

        attribute_total = 0
        is_multi_pc = False  # 是否是多pc属性集

        logging.info(f"检查属性集文件夹: {main_folder_name}")

        # 遍历该属性集下的数量文件夹
        for quantity_folder_name in os.listdir(main_folder_path):
            quantity_folder_path = os.path.join(main_folder_path, quantity_folder_name)

            if not os.path.isdir(quantity_folder_path):
                continue

            # 解析数量文件夹名称（如"2张"、"3套"）
            quantity_match = re.match(r'(\d+)[张套]', quantity_folder_name)
            if not quantity_match:
                continue

            quantity_value = int(quantity_match.group(1))

            # 检查是否是"套"文件夹，如果是则标记为多pc
            if quantity_folder_name.endswith('套'):
                is_multi_pc = True

            # 统计该数量文件夹中的jpg文件数量（只统计直接在数量文件夹下的jpg）
            jpg_count = 0
            for item in os.listdir(quantity_folder_path):
                item_path = os.path.join(quantity_folder_path, item)
                if os.path.isfile(item_path) and item.lower().endswith('.jpg'):
                    jpg_count += 1

            # 计算该数量文件夹的总数量
            folder_total = jpg_count * quantity_value
            attribute_total += folder_total

            logging.info(f"  - {quantity_folder_name}: {jpg_count}个jpg × {quantity_value} = {folder_total}")

        # 确定该属性集的显示单位
        unit = "套" if is_multi_pc else "张"

        attribute_stats[main_folder_name] = {
            "total": attribute_total,
            "unit": unit,
            "is_multi_pc": is_multi_pc
        }

        logging.info(f"  属性集 '{main_folder_name}' 总计: {attribute_total}{unit}")

    return attribute_stats

def calculate_simple_quantity_stats(df, folder_config):
    """
    简化的数量统计逻辑：基于拣货单直接统计属性集数量
    """
    try:
        attribute_stats = {}

        # 遍历拣货单，统计每个属性集的数量
        for index, row in df.iterrows():
            try:
                # 提取属性集
                attribute_set = str(row.get('属性集', '')).strip()
                if not attribute_set or attribute_set == 'nan':
                    continue

                # 获取映射后的目标属性集
                target_attribute = get_size_folder(attribute_set, folder_config)
                if target_attribute == "unknown":
                    target_attribute = f"1pc{attribute_set}"

                # 统计数量
                if target_attribute not in attribute_stats:
                    attribute_stats[target_attribute] = 0
                attribute_stats[target_attribute] += 1

            except Exception as e:
                logging.warning(f"处理第{index+1}行时出错: {e}")
                continue

        # 转换为期望的格式
        result = {}
        for attr, count in attribute_stats.items():
            # 判断单位
            unit = "张" if "1pc" in attr else "套"
            result[attr] = {
                'total_quantity': count,
                'unit': unit,
                'default_unit': unit,  # 添加缺失的字段
                'is_multi_pc': "套" == unit
            }

        logging.info(f"简化统计完成，共统计 {len(result)} 个属性集")
        for attr, stats in result.items():
            logging.info(f"  {attr}: {stats['total_quantity']}{stats['unit']}")

        return result

    except Exception as e:
        logging.error(f"简化统计过程中出错: {e}")
        return {}

def calculate_total_stats(df, source_folder, small_pdf_folder=None):
    """
    新的统计逻辑：
    - SKC匹配小标拣货单数量
    - 相同SKC累加（如果有两个一样的SKC说明是2）
    - 按属性集分组统计
    """
    # 按属性集分组统计
    attribute_stats = {}

    # 统计拣货单中各SKC的数量（相同SKC累加）
    skc_counts_in_df = {}
    for _, row in df.iterrows():
        product_info = row.get('商品信息')
        attribute_set = row.get('属性集')

        # 跳过无效行
        if (not isinstance(product_info, str) or not product_info.strip()) and (not isinstance(attribute_set, str) or not attribute_set.strip()):
            continue

        # 检查product_info是否为有效字符串
        if not isinstance(product_info, str) or not product_info.strip():
            continue

        # 提取SKC编号
        skc_number_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if not skc_number_match:
            continue

        skc_base = skc_number_match.group(1)

        # 统计拣货单中SKC出现次数
        if skc_base not in skc_counts_in_df:
            skc_counts_in_df[skc_base] = 0
        skc_counts_in_df[skc_base] += 1

        logging.info(f"拣货单中SKC {skc_base} 累计出现: {skc_counts_in_df[skc_base]} 次")

    # 按属性集分组，统计各属性集下面的SKC数量
    for _, row in df.iterrows():
        product_info = row.get('商品信息')
        attribute_set = row.get('属性集')

        # 跳过无效行
        if (not isinstance(product_info, str) or not product_info.strip()) and (not isinstance(attribute_set, str) or not attribute_set.strip()):
            continue

        # 检查product_info是否为有效字符串
        if not isinstance(product_info, str) or not product_info.strip():
            continue

        # 提取SKC编号
        skc_number_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if not skc_number_match:
            continue

        skc_base = skc_number_match.group(1)

        # 检查该SKC是否有对应的图片文件
        has_images = False
        pc_count = 1  # 默认单pc
        if os.path.isdir(source_folder):
            jpg_count, pc_count = count_images_and_pc(source_folder, skc_base)
            has_images = jpg_count > 0

        if has_images:
            # 按属性集分组
            if attribute_set not in attribute_stats:
                attribute_stats[attribute_set] = {
                    'total_quantity': 0,
                    'is_multi_pc': False,
                    'skc_count': 0,
                    'processed_skcs': set()  # 记录已处理的SKC，避免重复统计
                }

            # 避免同一个SKC在同一个属性集中重复统计
            if skc_base not in attribute_stats[attribute_set]['processed_skcs']:
                # 使用拣货单中该SKC的累计数量
                skc_quantity = skc_counts_in_df.get(skc_base, 0)

                if skc_quantity > 0:
                    # 累加该属性集的数量（基于拣货单中SKC数量）
                    attribute_stats[attribute_set]['total_quantity'] += skc_quantity
                    attribute_stats[attribute_set]['skc_count'] += 1
                    attribute_stats[attribute_set]['processed_skcs'].add(skc_base)

                    # 单pc/多pc判断：基于pc数判断
                    if pc_count > 1:
                        attribute_stats[attribute_set]['is_multi_pc'] = True
                        logging.info(f"属性集 '{attribute_set}' 标记为多pc (SKC {skc_base}: {pc_count}pc)")

                    logging.info(f"属性集 '{attribute_set}': SKC {skc_base}({pc_count}pc) 拣货单数量+{skc_quantity}")

    # 输出各属性集的统计结果
    total_quantity = 0
    total_specs = len(attribute_stats)

    for attr_set, stats in attribute_stats.items():
        quantity = stats['total_quantity']
        unit = "套" if stats['is_multi_pc'] else "张"
        total_quantity += quantity
        logging.info(f"属性集 '{attr_set}': 总{quantity}{unit} ({stats['skc_count']}个SKC) - 基于拣货单SKC数量统计")

    logging.info(f"全局统计: {total_quantity}，{total_specs}个属性集")

    return {
        "total_images": total_quantity,
        "total_specs": total_specs,
        "attribute_stats": attribute_stats  # 返回各属性集的详细统计
    }

def count_images_and_pc(source_folder, skc_base):
    """
    统计SKC的图片数量和pc数

    Args:
        source_folder: 源文件夹路径
        skc_base: SKC编号

    Returns:
        tuple: (图片数量, pc数)
    """
    if not os.path.isdir(source_folder):
        return 0, 1

    # 收集所有相关图片
    related_images = []
    pc_suffixes = set()

    for filename in os.listdir(source_folder):
        file_path = os.path.join(source_folder, filename)
        if (os.path.isfile(file_path) and
            filename.lower().startswith(skc_base.lower()) and
            filename.lower().endswith('.jpg')):

            related_images.append(filename)

            # 检查是否有pc后缀（如-1, -2, -3）
            pc_match = re.search(r'-(\d+)\.jpg$', filename.lower())
            if pc_match:
                pc_suffixes.add(int(pc_match.group(1)))

    jpg_count = len(related_images)

    # 修正pc数计算逻辑：
    # 1. 如果没有后缀，默认1pc
    # 2. 如果有后缀，pc数 = 后缀的数量（不是最大值）
    # 3. 特殊情况：如果后缀是连续的（1,2,3），则pc数 = 最大值
    # 4. 如果后缀不连续（如只有-2），则pc数 = 后缀数量
    if not pc_suffixes:
        pc_count = 1
    else:
        # 检查后缀是否连续
        sorted_suffixes = sorted(pc_suffixes)
        is_consecutive = all(sorted_suffixes[i] == sorted_suffixes[0] + i for i in range(len(sorted_suffixes)))

        if is_consecutive and sorted_suffixes[0] == 1:
            # 连续且从1开始，pc数 = 最大值
            pc_count = max(pc_suffixes)
            logging.info(f"🔍 检测到连续pc后缀从1开始: {sorted_suffixes} -> {pc_count}pc")
        else:
            # 不连续或不从1开始，pc数 = 后缀数量
            pc_count = len(pc_suffixes)
            logging.info(f"🔍 检测到非连续pc后缀: {sorted_suffixes} -> {pc_count}pc")

    logging.info(f"🔍 SKC {skc_base}: 找到{jpg_count}张图片，pc后缀: {sorted(pc_suffixes) if pc_suffixes else '无'}，判定为{pc_count}pc")

    return jpg_count, pc_count

def update_folder_names_with_individual_stats(target_base_folder, individual_stats, folder_config):
    """
    使用各属性集独立统计的数据更新文件夹名称

    Args:
        target_base_folder: 目标基础文件夹路径
        individual_stats: 各属性集的独立统计数据
        folder_config: 文件夹配置
    """
    if not os.path.exists(target_base_folder):
        return

    # 检查是否需要使用复杂命名
    date = folder_config.get("date", "").strip()
    time_period = folder_config.get("time_period", "").strip()
    operator = folder_config.get("operator", "").strip()
    shop_number = folder_config.get("shop_number", "").strip()

    # 如果配置为空，不需要更新文件夹名称
    if not any([date, time_period, operator, shop_number]):
        logging.info("配置为空，不更新文件夹名称")
        return

    # 遍历所有主文件夹，更新名称
    for main_folder_name in os.listdir(target_base_folder):
        main_folder_path = os.path.join(target_base_folder, main_folder_name)

        if not os.path.isdir(main_folder_path):
            continue

        # 跳过特殊文件夹
        if main_folder_name in ['小标PDF', '大标']:
            continue

        # 检查是否是需要更新的文件夹格式
        if "特级JIT" in main_folder_name and main_folder_name in individual_stats:
            # 已经是新格式，更新总数部分
            stats = individual_stats[main_folder_name]
            new_folder_name = update_individual_total_in_folder_name(main_folder_name, stats)
            if new_folder_name != main_folder_name:
                new_folder_path = os.path.join(target_base_folder, new_folder_name)
                try:
                    os.rename(main_folder_path, new_folder_path)
                    logging.info(f"更新文件夹名称: {main_folder_name} -> {new_folder_name}")
                except Exception as e:
                    logging.error(f"重命名文件夹失败: {e}")

def update_individual_total_in_folder_name(folder_name, stats):
    """
    更新文件夹名称中的总数部分（基于该属性集的独立统计）

    Args:
        folder_name: 原文件夹名称
        stats: 该属性集的统计数据 {"total": 数量, "unit": "张"/"套", "is_multi_pc": bool}

    Returns:
        str: 更新后的文件夹名称
    """
    total_count = stats.get("total", 0)
    unit = stats.get("unit", "张")

    # 新的总数显示
    new_total_display = f"总{total_count}{unit}"

    # 使用正则表达式替换总数部分
    pattern = r'\(总\d+[张套]\)'
    new_folder_name = re.sub(pattern, f'({new_total_display})', folder_name)

    return new_folder_name

def generate_folder_name(intended_size, image_count, config, total_stats=None, use_simple_naming=False, attribute_set=None, actual_pc_count=None):
    """
    生成文件夹命名格式，支持简单命名和复杂命名

    Args:
        intended_size: 识别的尺寸 (如 "30x40cm", "18x18inch", "black_30x40", "gold_30x40")
        image_count: 当前规格的图片数量
        config: 配置信息 (日期、时间段、操作员、店铺号)
        total_stats: 总统计信息 {"total_images": 总图片数, "total_specs": 总规格数, "attribute_stats": 各属性集统计}
        use_simple_naming: 是否使用简单命名（原来的命名方式）
        attribute_set: 当前属性集名称
        actual_pc_count: 实际检测到的pc数

    Returns:
        str: 格式化的文件夹名称
    """
    # 检查是否所有配置项都为空或默认值，如果是则使用简单命名
    date = config.get("date", "").strip()
    time_period = config.get("time_period", "").strip()
    operator = config.get("operator", "").strip()
    shop_number = config.get("shop_number", "").strip()

    # 定义需要使用新命名格式的规格
    special_naming_specs = {
        "18x18inch": True,
        "black_30x40": True,
        "gold_30x40": True,
        "30x40cm": True,
        "40x60cm": True,
        "12inch": True,  # 新增：12inch木板钟
        "apron_55x68cm": True,  # 新增：围裙
        "two_sided_18x18inch": True  # 新增：双面抱枕
    }

    # 检查当前规格是否需要使用新命名格式
    needs_special_naming = False
    if intended_size in special_naming_specs:
        # 对于特定规格，检查数量是否匹配需要新命名的情况
        if intended_size == "18x18inch" and image_count == 1:
            needs_special_naming = True
        elif intended_size == "black_30x40" and image_count == 1:
            needs_special_naming = True
        elif intended_size == "gold_30x40" and image_count == 1:
            needs_special_naming = True
        elif intended_size == "30x40cm" and image_count in [1, 2, 3, 4]:  # 添加4pc支持
            needs_special_naming = True
        elif intended_size == "40x60cm" and image_count in [1, 2, 3, 4]:  # 添加4pc支持
            needs_special_naming = True
        elif intended_size == "12inch":  # 12inch木板钟
            needs_special_naming = True
        elif intended_size == "apron_55x68cm":  # 围裙
            needs_special_naming = True
        elif intended_size == "two_sided_18x18inch":  # 双面抱枕
            needs_special_naming = True

    # 特殊处理：强制4pc30x40cm和4pc40x60cm使用复杂命名
    if attribute_set and isinstance(attribute_set, str):
        if (attribute_set.strip() == "4pc30x40cm" and intended_size == "30x40cm" and image_count == 4) or \
           (attribute_set.strip() == "4pc40x60cm" and intended_size == "40x60cm" and image_count == 4):
            needs_special_naming = True
            logging.info(f"强制使用复杂命名: {attribute_set} -> {intended_size} {image_count}pc")

    # 如果不需要特殊命名，或者配置为空，或者明确要求使用简单命名，则使用6.13的简单命名方式
    if use_simple_naming or not needs_special_naming or not any([date, time_period, operator, shop_number]):
        # 简单命名方式 - 优先使用表格模版映射
        if intended_size == "unknown" and attribute_set:
            # 获取表格模版映射的目标属性集
            target_attribute = get_template_mapping(attribute_set)
            if target_attribute and target_attribute != "unknown":
                # 直接使用B列的目标属性集名称，不加pc前缀
                return clean_folder_name(target_attribute)
            else:
                # 如果没有映射，使用清理后的原属性集名称
                cleaned_attribute_set = clean_folder_name(attribute_set)
                return cleaned_attribute_set
        elif intended_size == "18x18inch":
            return "18x18inch正方形抱枕"
        elif intended_size == "black_30x40":
            return "黑框30x40cm帆布画"
        elif intended_size == "gold_30x40":
            return "金框30x40cm帆布画"
        elif intended_size == "12inch":
            return "12inch木板钟"
        elif intended_size == "apron_55x68cm":
            return "围裙55x68cm"
        elif intended_size == "two_sided_18x18inch":
            return "双面抱枕45x45cm"
        elif intended_size in ["30x40cm", "40x60cm", "40x40cm"]:
            return f"木框帆布画{intended_size}"
        else:
            # 如果intended_size是unknown，使用表格模版映射的目标属性集名称
            if intended_size == "unknown" and attribute_set:
                # 获取表格模版映射的目标属性集
                target_attribute = get_template_mapping(attribute_set)
                if target_attribute and target_attribute != "unknown":
                    # 直接使用B列的目标属性集名称，不加pc前缀
                    return clean_folder_name(target_attribute)
                else:
                    # 如果没有映射，使用清理后的原属性集名称
                    cleaned_attribute_set = clean_folder_name(attribute_set)
                    return cleaned_attribute_set
            else:
                size_display = intended_size if intended_size != "unknown" else "未知尺寸"
                return size_display

    # 使用自动获取的值填充空的配置项
    if not date or not time_period:
        auto_date, auto_time = get_auto_date_and_time()
        if not date:
            date = auto_date
        if not time_period:
            time_period = auto_time
    if not operator:
        operator = "彭于晏"
    if not shop_number:
        shop_number = "店铺号"

    # 总数显示部分先留空，等弹窗确认后再填入
    total_display = "总X"
    logging.info(f"文件夹创建时总数部分留空: {total_display}")

    # 优先使用表格模版映射的目标属性集名称
    if intended_size == "unknown" and attribute_set:
        # 获取表格模版映射的目标属性集
        target_attribute = get_template_mapping(attribute_set)
        if target_attribute and target_attribute != "unknown":
            # 如果提供了实际pc数，替换目标属性集中的pc数
            if actual_pc_count is not None and actual_pc_count > 0:
                # 使用正则表达式替换pc数
                updated_target_attribute = re.sub(r'\d+pc', f'{actual_pc_count}pc', target_attribute)
                logging.info(f"🔍 替换pc数: '{target_attribute}' -> '{updated_target_attribute}'")
                target_attribute = updated_target_attribute

            # 使用B列的目标属性集名称构建复杂命名
            folder_name = f"特级JIT-{date}-{time_period}自送-{target_attribute}-{operator}-俞志敏组-({total_display})-{shop_number}"
        else:
            # 如果没有映射，使用清理后的原属性集名称
            cleaned_attribute_set = clean_folder_name(attribute_set)
            folder_name = f"特级JIT-{date}-{time_period}自送-{cleaned_attribute_set}-{operator}-俞志敏组-({total_display})-{shop_number}"
    # 根据不同的尺寸类型生成复杂命名（只对特定规格）
    elif intended_size == "18x18inch" and image_count == 1:
        # 正方形抱枕
        folder_name = f"特级JIT-{date}-{time_period}自送-正方形抱枕45x45cm-单面-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "black_30x40" and image_count == 1:
        # 黑框帆布画
        folder_name = f"特级JIT-{date}-{time_period}自送-黑框帆布画30x40cm-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "gold_30x40" and image_count == 1:
        # 金框帆布画
        folder_name = f"特级JIT-{date}-{time_period}自送-金框帆布画30x40cm-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "12inch":
        # 12inch木板钟
        folder_name = f"特级JIT-{date}-{time_period}自送-木板钟12inch-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "apron_55x68cm":
        # 围裙
        folder_name = f"特级JIT-{date}-{time_period}自送-围裙55x68cm-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "two_sided_18x18inch":
        # 双面抱枕
        folder_name = f"特级JIT-{date}-{time_period}自送-正方形抱枕45x45cm-双面-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "30x40cm" and image_count in [1, 2, 3, 4]:
        # 木框帆布画30x40cm
        folder_name = f"特级JIT-{date}-{time_period}自送-木框帆布画30x40cm-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "40x60cm" and image_count in [1, 2, 3, 4]:
        # 木框帆布画40x60cm
        folder_name = f"特级JIT-{date}-{time_period}自送-木框帆布画40x60cm-{operator}-俞志敏组-({total_display})-{shop_number}"
    else:
        # 其他情况，使用简单命名（不加pc前缀）
        if intended_size == "unknown" and attribute_set:
            # 获取表格模版映射的目标属性集
            target_attribute = get_template_mapping(attribute_set)
            if target_attribute and target_attribute != "unknown":
                # 直接使用B列的目标属性集名称，不加pc前缀
                return clean_folder_name(target_attribute)
            else:
                # 如果没有映射，使用清理后的原始属性集名称
                cleaned_attribute_set = re.sub(r'[\n\r]+', ' ', attribute_set).strip()
                return clean_folder_name(cleaned_attribute_set)
        elif intended_size == "18x18inch":
            return "18x18inch正方形抱枕"
        elif intended_size == "black_30x40":
            return "黑框30x40cm帆布画"
        elif intended_size == "gold_30x40":
            return "金框30x40cm帆布画"
        elif intended_size in ["30x40cm", "40x60cm", "40x40cm"]:
            return f"木框帆布画{intended_size}"
        else:
            size_display = intended_size if intended_size != "unknown" else "未知尺寸"
            return size_display

    return clean_folder_name(folder_name)

# 拆分PDF文件并根据内容重命名
def split_pdf_rename(pdf_path, output_dir, log_callback=None):
    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        return

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    try:
        reader = PyPDF2.PdfReader(pdf_path)
        num_pages = len(reader.pages)
        for page_num in range(num_pages):
            try:
                # 尝试两种方法提取文本
                # 方法1：使用PyPDF2直接提取
                page = reader.pages[page_num]
                text1 = page.extract_text()

                # 方法2：使用pdfminer提取
                text2 = extract_text_from_page(pdf_path, page_num)

                # 合并两种方法的文本
                combined_text = text1 + "\n" + text2

                # 添加调试信息
                logging.info(f"PDF第{page_num + 1}页提取的文本内容: {combined_text[:200]}...")  # 只显示前200个字符

                # 更宽松的正则表达式匹配
                skc_patterns = [
                    r"SKC[:：]?\s*(\d{9,11})",  # SKC: 或 SKC：后跟9-11位数字
                    r"SKC\s*(\d{9,11})",       # SKC 后跟数字
                    r"(\d{9,11})",             # 直接匹配9-11位数字
                ]

                pc_patterns = [
                    r"PC[:：]?\s*(\d{10,15})",  # PC: 或 PC：后跟10-15位数字（完整PC码）
                    r"PC\s*(\d{10,15})",        # PC 后跟10-15位数字
                    r"pc[:：]?\s*(\d{10,15})",  # 小写pc后跟10-15位数字
                    r"PC[:：]?\s*(\d+)",        # 兜底：PC: 或 PC：后跟任意数字
                    r"PC\s*(\d+)",              # 兜底：PC 后跟任意数字
                    r"pc[:：]?\s*(\d+)",        # 兜底：小写pc后跟任意数字
                ]

                count_patterns = [
                    r"(\d+)\s*件",             # 数字+件
                    r"(\d+)\s*个",             # 数字+个
                    r"数量[:：]?\s*(\d+)",      # 数量:数字
                    r"qty[:：]?\s*(\d+)",      # qty:数字（英文）
                ]

                # 尝试匹配SKC
                skc = "UnknownSKC"
                for pattern in skc_patterns:
                    match = re.search(pattern, combined_text, re.IGNORECASE)
                    if match:
                        skc = match.group(1)
                        break

                # 尝试匹配PC
                pc = "UnknownPC"
                for i, pattern in enumerate(pc_patterns):
                    match = re.search(pattern, combined_text, re.IGNORECASE)
                    if match:
                        pc = match.group(1)  # 只要数字，不要PC前缀
                        logging.info(f"PC匹配成功: 模式{i+1} '{pattern}' 提取到 '{pc}'")
                        break
                else:
                    logging.warning(f"PC匹配失败，文本内容: {combined_text}")
                    # 尝试查找所有可能的数字序列
                    all_numbers = re.findall(r'\d{6,}', combined_text)
                    if all_numbers:
                        logging.info(f"文本中找到的长数字序列: {all_numbers}")
                        # 选择最长的数字序列作为PC码
                        pc = max(all_numbers, key=len)
                        logging.info(f"选择最长数字序列作为PC码: {pc}")

                # 尝试匹配件数
                count = "1"
                for pattern in count_patterns:
                    match = re.search(pattern, combined_text, re.IGNORECASE)
                    if match:
                        count = match.group(1)
                        break

                writer = PyPDF2.PdfWriter()
                writer.add_page(page)
                # 保持原来的命名格式：SKC编号_PC编号_件数_页码.pdf
                output_filename = f"{skc}_{pc}_{count}_{page_num + 1}.pdf"
                output_path = os.path.join(output_dir, output_filename)
                with open(output_path, "wb") as outfile:
                    writer.write(outfile)

            except Exception as e:
                if log_callback:
                    log_callback(f"处理第{page_num + 1}页时发生错误: {e}")
                else:
                    print(f"处理第{page_num + 1}页时发生错误: {e}")
    except Exception as e:
        if log_callback:
            log_callback(f"读取PDF文件时发生错误: {e}")
        else:
            print(f"读取PDF文件时发生错误: {e}")

def extract_text_from_page(pdf_path, page_num):
    # 延迟导入pdfminer
    pdfminer_modules = lazy_import_pdfminer()
    if not pdfminer_modules:
        return ""

    StringIO = pdfminer_modules['StringIO']
    PDFParser = pdfminer_modules['PDFParser']
    PDFDocument = pdfminer_modules['PDFDocument']
    PDFResourceManager = pdfminer_modules['PDFResourceManager']
    TextConverter = pdfminer_modules['TextConverter']
    LAParams = pdfminer_modules['LAParams']
    PDFPageInterpreter = pdfminer_modules['PDFPageInterpreter']
    PDFPage = pdfminer_modules['PDFPage']

    output_string = StringIO()
    with open(pdf_path, 'rb') as in_file:
        parser = PDFParser(in_file)
        doc = PDFDocument(parser)
        rsrcmgr = PDFResourceManager()
        device = TextConverter(rsrcmgr, output_string, laparams=LAParams())
        interpreter = PDFPageInterpreter(rsrcmgr, device)
        for i, page in enumerate(PDFPage.create_pages(doc)):
            if i == page_num:
                interpreter.process_page(page)
                break
    return output_string.getvalue()

def extract_sku_from_big_pdf(pdf_path):
    """
    从大标PDF文件中提取SKU货号

    Args:
        pdf_path: PDF文件路径

    Returns:
        list: 提取到的SKU货号列表
    """
    sku_numbers = []

    try:
        # 延迟导入pdfplumber
        pdfplumber = lazy_import_pdfplumber()
        if not pdfplumber:
            logging.error("🏷️ [SKU提取] pdfplumber库不可用")
            return sku_numbers

        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                try:
                    text = page.extract_text()
                    if text:
                        # 先打印原始文本用于调试
                        logging.info(f"🏷️ [SKU提取] 第{page_num+1}页原始文本: {repr(text[:300])}...")

                        # 查找SKU货号模式 - 更全面的匹配
                        # 常见格式：SKU货号: zx10614061329-16x16
                        sku_patterns = [
                            r'SKU货号[:：]?\s*([a-zA-Z0-9\-_]+(?:-\d+x\d+)?)',
                            r'SKU[:：]?\s*([a-zA-Z0-9\-_]+(?:-\d+x\d+)?)',
                            r'货号[:：]?\s*([a-zA-Z0-9\-_]+(?:-\d+x\d+)?)',
                            r'产品编号[:：]?\s*([a-zA-Z0-9\-_]+(?:-\d+x\d+)?)',
                            r'编号[:：]?\s*([a-zA-Z0-9\-_]+(?:-\d+x\d+)?)',
                        ]

                        for pattern in sku_patterns:
                            matches = re.findall(pattern, text, re.IGNORECASE)
                            for match in matches:
                                # 清理SKU货号（去掉换行等）
                                cleaned_sku = re.sub(r'\s+', '', match).strip()
                                if cleaned_sku and len(cleaned_sku) > 3:  # 过滤太短的匹配
                                    sku_numbers.append(cleaned_sku)
                                    logging.info(f"🏷️ [SKU提取] 第{page_num+1}页找到SKU: {cleaned_sku}")

                        # 也尝试查找类似 zx10614061329-24x24 这样的模式（不带前缀）
                        # 更全面的模式匹配
                        direct_patterns = [
                            r'\b([a-zA-Z]+\d+[a-zA-Z0-9]*(?:-\d+x\d+)?)\b',  # 如 zx10614db329-24x24
                            r'\b([a-zA-Z]{2,}\d{8,}[a-zA-Z0-9\-_]*)\b',      # 更严格：至少2个字母+8个数字
                            r'\b([a-zA-Z]{1,3}\d{6,}[a-zA-Z0-9\-_]*)\b',     # 稍宽松：1-3个字母+6个数字
                            r'([a-zA-Z]+\d+[a-zA-Z]*-\d+x\d+)',              # 专门匹配带尺寸的格式
                        ]

                        for pattern in direct_patterns:
                            matches = re.findall(pattern, text)
                            for match in matches:
                                cleaned_sku = re.sub(r'\s+', '', match).strip()
                                # 更宽松的过滤条件
                                if (cleaned_sku and
                                    len(cleaned_sku) >= 6 and
                                    len(cleaned_sku) <= 50 and
                                    re.search(r'[a-zA-Z]', cleaned_sku) and
                                    re.search(r'\d{3,}', cleaned_sku) and  # 至少3个连续数字
                                    cleaned_sku not in sku_numbers and
                                    not cleaned_sku.isdigit()):  # 排除纯数字
                                    sku_numbers.append(cleaned_sku)
                                    logging.info(f"🏷️ [SKU提取] 第{page_num+1}页找到可能的SKU: {cleaned_sku}")

                                # 如果还没找到，尝试更宽泛的搜索
                        if not sku_numbers:
                            logging.info(f"🏷️ [SKU提取] 第{page_num+1}页未找到标准SKU，尝试宽泛搜索...")
                            # 查找所有可能的产品代码
                            all_potential_codes = re.findall(r'\b([a-zA-Z]+\d+[a-zA-Z0-9\-_]*)\b', text)
                            logging.info(f"🏷️ [SKU提取] 第{page_num+1}页找到潜在代码: {all_potential_codes}")
                            for code in all_potential_codes:
                                cleaned_code = re.sub(r'\s+', '', code).strip()
                                if (cleaned_code and
                                    len(cleaned_code) >= 5 and
                                    len(cleaned_code) <= 50 and
                                    re.search(r'[a-zA-Z]', cleaned_code) and
                                    re.search(r'\d{2,}', cleaned_code) and
                                    cleaned_code not in sku_numbers):
                                    sku_numbers.append(cleaned_code)
                                    logging.info(f"🏷️ [SKU提取] 第{page_num+1}页找到备选SKU: {cleaned_code}")

                        # 如果仍然没有找到，记录详细信息
                        if not sku_numbers:
                            logging.warning(f"🏷️ [SKU提取] 第{page_num+1}页完全没有找到SKU，文本内容: {text[:500]}")

                except Exception as e:
                    logging.warning(f"🏷️ [SKU提取] 处理第{page_num+1}页时出错: {e}")
                    continue

    except Exception as e:
        logging.error(f"🏷️ [SKU提取] 读取PDF文件失败: {pdf_path}, 错误: {e}")

    # 去重并返回
    unique_skus = list(set(sku_numbers))
    logging.info(f"🏷️ [SKU提取] 从 {pdf_path} 中提取到 {len(unique_skus)} 个唯一SKU货号: {unique_skus}")
    return unique_skus

def move_big_labels_to_folders(df, target_base_folder, big_pdf_folder):
    """
    根据SKC匹配大标PDF文件，并移动到对应的属性集文件夹中
    新版本：参考小标逻辑，使用SKC匹配+尺寸识别来决定移动到哪个文件夹
    """
    logging.info("🏷️ [大标移动] 开始处理大标文件移动...")
    logging.info(f"🏷️ [大标移动] 大标PDF文件夹: {big_pdf_folder}")
    logging.info(f"🏷️ [大标移动] 目标基础文件夹: {target_base_folder}")

    # 获取所有大标PDF文件
    big_pdf_files = {}
    if os.path.exists(big_pdf_folder):
        for filename in os.listdir(big_pdf_folder):
            if filename.lower().endswith('.pdf'):
                # 从文件名中提取SKC (格式: SKC_PC_Count_PageNum.pdf)
                parts = filename.split('_')
                if len(parts) >= 1:
                    skc = parts[0]
                    if skc.isdigit():
                        if skc not in big_pdf_files:
                            big_pdf_files[skc] = []
                        big_pdf_files[skc].append(filename)
                        logging.info(f"🏷️ [大标移动] 找到大标PDF: {filename} (SKC: {skc})")
                    else:
                        logging.info(f"🏷️ [大标移动] 跳过非SKC格式文件: {filename}")
                else:
                    logging.info(f"🏷️ [大标移动] 跳过格式不正确的文件: {filename}")
    else:
        logging.warning(f"🏷️ [大标移动] 大标PDF文件夹不存在: {big_pdf_folder}")
        return

    logging.info(f"🏷️ [大标移动] 总共找到 {len(big_pdf_files)} 个不同SKC的大标PDF文件")

    # 从拣货单中提取所有SKC
    skc_list = set()
    for index, row in df.iterrows():
        product_info = row.get('商品信息', '')
        logging.info(f"🔥🔥🔥 [调试] 第{index+1}行商品信息: '{product_info}'")
        if isinstance(product_info, str) and product_info.strip():
            # 提取SKC编号
            skc_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
            if skc_match:
                extracted_skc = skc_match.group(1)
                skc_list.add(extracted_skc)
                logging.info(f"🔥🔥🔥 [调试] 提取到SKC: '{extracted_skc}'")
            else:
                logging.info(f"🔥🔥🔥 [调试] 未能从商品信息中提取SKC")
        else:
            logging.info(f"🔥🔥🔥 [调试] 商品信息为空或非字符串")

    logging.info(f"🏷️ [大标移动] 拣货单中的SKC列表: {sorted(skc_list)}")
    logging.info(f"🔥🔥🔥 [调试] 拣货单SKC详细信息: {list(skc_list)}")
    logging.info(f"🔥🔥🔥 [调试] 拣货单SKC数量: {len(skc_list)}")

    moved_count = 0
    processed_skcs = set()

    # 遍历每个SKC的大标PDF文件
    for skc, pdf_files in big_pdf_files.items():
        if skc in processed_skcs:
            continue

        processed_skcs.add(skc)
        logging.info(f"\n🏷️ [大标移动] 处理SKC: {skc}")
        logging.info(f"🏷️ [大标移动] 该SKC的PDF文件: {pdf_files}")

        # 检查SKC是否在拣货单中
        if skc not in skc_list:
            logging.warning(f"🏷️ [大标移动] SKC {skc} 不在拣货单中，跳过")
            continue

        # 从PDF文件中提取尺寸信息
        pdf_size_info = []
        for pdf_file in pdf_files:
            pdf_path = os.path.join(big_pdf_folder, pdf_file)
            extracted_size = extract_size_from_pdf_file(pdf_path)

            if extracted_size:
                # 清理尺寸信息（去掉换行等）
                cleaned_size = extracted_size.replace('\n', '').replace('\r', '').strip()
                standardized_size = parse_and_standardize_size(cleaned_size)
                pdf_size_info.append((pdf_file, standardized_size, cleaned_size))
                logging.info(f"🏷️ [大标移动] {pdf_file}: 提取尺码='{cleaned_size}' -> 标准化='{standardized_size}'")
            else:
                # 尝试从拣货单中的属性集推断尺寸
                inferred_size = infer_size_from_picking_list(skc, df)
                if inferred_size:
                    standardized_size = parse_and_standardize_size(inferred_size)
                    pdf_size_info.append((pdf_file, standardized_size, inferred_size))
                    logging.info(f"🏷️ [大标移动] {pdf_file}: 从拣货单推断尺码='{inferred_size}' -> 标准化='{standardized_size}'")
                else:
                    pdf_size_info.append((pdf_file, None, None))
                    logging.info(f"🏷️ [大标移动] {pdf_file}: 无法提取尺码信息")

        # 获取目标文件夹列表
        target_folders = []
        if os.path.exists(target_base_folder):
            for item in os.listdir(target_base_folder):
                item_path = os.path.join(target_base_folder, item)
                if os.path.isdir(item_path):
                    target_folders.append(item_path)

        logging.info(f"🏷️ [大标移动] 找到 {len(target_folders)} 个目标文件夹")

        # 为每个PDF文件找到最佳匹配的文件夹
        for pdf_file, pdf_standardized_size, _ in pdf_size_info:
            best_folder = None
            best_score = 0

            # 第一步：先进行SKC匹配，找到包含对应SKC的JPG文件的文件夹
            skc_matched_folders = []
            logging.info(f"🏷️ [大标移动] 开始为PDF {pdf_file} (SKC: {skc}) 查找匹配的文件夹...")
            logging.info(f"🏷️ [大标移动] 总共有 {len(target_folders)} 个目标文件夹")

            for folder_path in target_folders:
                folder_name = os.path.basename(folder_path)
                logging.info(f"🏷️ [大标移动] 检查文件夹: {folder_name}")

                # 检查文件夹中是否有对应SKC的JPG文件（递归查找子文件夹）
                if os.path.exists(folder_path):
                    jpg_files = []
                    found_match = False

                    # 递归查找JPG文件
                    def find_jpg_files(search_path, depth=0):
                        if depth > 3:  # 限制递归深度
                            return []

                        found_jpgs = []
                        try:
                            for item in os.listdir(search_path):
                                item_path = os.path.join(search_path, item)
                                if os.path.isfile(item_path) and (item.lower().endswith('.jpg') or item.lower().endswith('.jpeg')):
                                    found_jpgs.append((item, item_path))
                                elif os.path.isdir(item_path):
                                    # 递归查找子文件夹
                                    found_jpgs.extend(find_jpg_files(item_path, depth + 1))
                        except PermissionError:
                            pass
                        return found_jpgs

                    all_jpg_files = find_jpg_files(folder_path)

                    for jpg_name, jpg_path in all_jpg_files:
                        jpg_files.append(jpg_name)
                        # 从JPG文件名提取SKC（处理多种格式）
                        jpg_base_name = jpg_name.split('.')[0]

                        # 提取纯SKC：处理 SKC-数字-尺寸 格式
                        if '-' in jpg_base_name:
                            # 格式如：60593744540-1-40x60 -> 60593744540
                            jpg_skc = jpg_base_name.split('-')[0]
                        else:
                            # 格式如：60593744540 -> 60593744540
                            jpg_skc = jpg_base_name

                        logging.info(f"🏷️ [大标移动]   找到JPG: {jpg_name} (原始: {jpg_base_name} -> 提取SKC: {jpg_skc}) 路径: {jpg_path}")
                        logging.info(f"🔥🔥🔥 [调试] SKC比较: 大标PDF的SKC='{skc}' vs JPG的SKC='{jpg_skc}' -> 匹配={jpg_skc == skc}")

                        if jpg_skc == skc:
                            # SKC匹配成功，先添加到匹配列表，稍后根据匹配数量决定是否需要尺寸判断
                            skc_matched_folders.append(folder_path)
                            logging.info(f"🏷️ [大标移动] ✅ SKC匹配成功: {pdf_file} (SKC: {skc}) -> {folder_name} (找到JPG: {jpg_name})")
                            found_match = True
                            break

                    if not jpg_files:
                        logging.info(f"🏷️ [大标移动]   文件夹及子文件夹中没有JPG文件")
                    elif not found_match:
                        logging.info(f"🏷️ [大标移动]   找到 {len(jpg_files)} 个JPG文件，但没有匹配SKC {skc}")
                else:
                    logging.warning(f"🏷️ [大标移动]   文件夹不存在: {folder_path}")

            # 根据SKC匹配结果决定处理方式
            if len(skc_matched_folders) == 0:
                # 没有找到SKC匹配的文件夹，尝试通过PDF内容中的SKU货号查找
                logging.warning(f"🏷️ [大标移动] ❌ 未找到SKC {skc} 对应的JPG文件夹，尝试通过PDF内容查找SKU货号...")

                # 读取PDF内容，提取SKU货号
                pdf_path = os.path.join(big_pdf_folder, pdf_file)
                sku_numbers = extract_sku_from_big_pdf(pdf_path)

                if sku_numbers:
                    logging.info(f"🏷️ [大标移动] 从PDF {pdf_file} 中提取到SKU货号: {sku_numbers}")

                    # 使用SKU货号查找对应的JPG文件
                    sku_matched_folders = []
                    logging.info(f"🏷️ [大标移动] 开始使用SKU货号查找匹配文件夹，共有 {len(target_folders)} 个目标文件夹")
                    for sku_number in sku_numbers:
                        logging.info(f"🏷️ [大标移动] 正在查找SKU货号: {sku_number}")
                        for folder_path in target_folders:
                            folder_name = os.path.basename(folder_path)
                            logging.info(f"🏷️ [大标移动] 检查文件夹: {folder_name} (查找SKU: {sku_number})")

                            # 检查文件夹中是否有对应SKU货号的JPG文件（递归查找子文件夹）
                            if os.path.exists(folder_path):
                                found_match = False
                                jpg_files_in_folder = []
                                for root, dirs, files in os.walk(folder_path):
                                    for file in files:
                                        if file.lower().endswith('.jpg'):
                                            jpg_files_in_folder.append(file)
                                            jpg_path = os.path.join(root, file)
                                            jpg_name = os.path.basename(jpg_path)

                                            # 多种匹配方式
                                            match_found = False

                                            # 1. 直接匹配：JPG文件名以SKU货号开头
                                            if jpg_name.lower().startswith(sku_number.lower()):
                                                match_found = True
                                                logging.info(f"🏷️ [大标移动] ✅ 直接SKU匹配: {jpg_name} 以 {sku_number} 开头")

                                            # 2. 部分匹配：提取SKU货号的主要部分进行匹配
                                            elif sku_number:
                                                # 提取SKU货号的核心部分（去掉尺寸后缀）
                                                sku_core = re.sub(r'-\d+x\d+.*$', '', sku_number)
                                                if len(sku_core) >= 5 and sku_core.lower() in jpg_name.lower():
                                                    match_found = True
                                                    logging.info(f"🏷️ [大标移动] ✅ 部分SKU匹配: {jpg_name} 包含 {sku_core}")

                                            if match_found:
                                                sku_matched_folders.append(folder_path)
                                                logging.info(f"🏷️ [大标移动] ✅ SKU匹配成功: {pdf_file} (SKU: {sku_number}) -> {folder_name} (找到JPG: {jpg_name})")
                                                found_match = True
                                                break
                                    if found_match:
                                        break

                                # 记录文件夹中的JPG文件（用于调试）
                                if jpg_files_in_folder:
                                    logging.info(f"🏷️ [大标移动] 文件夹 {folder_name} 中的JPG文件: {jpg_files_in_folder[:3]}{'...' if len(jpg_files_in_folder) > 3 else ''}")
                                else:
                                    logging.info(f"🏷️ [大标移动] 文件夹 {folder_name} 中没有JPG文件")

                    if sku_matched_folders:
                        # 找到了SKU匹配的文件夹，优先选择完全匹配的文件夹
                        skc_matched_folders = sku_matched_folders
                        logging.info(f"🏷️ [大标移动] 通过SKU货号找到 {len(skc_matched_folders)} 个匹配文件夹")

                        # 对于SKU货号匹配，优先选择完全匹配的文件夹
                        best_match_folders = []
                        for folder_path in sku_matched_folders:
                            folder_name = os.path.basename(folder_path)
                            # 检查是否有完全匹配的SKU货号
                            for sku_number in sku_numbers:
                                if sku_number and len(sku_number) > 5:
                                    # 检查文件夹中是否有完全匹配的JPG文件
                                    for root, dirs, files in os.walk(folder_path):
                                        for file in files:
                                            if file.lower().endswith('.jpg'):
                                                jpg_name = os.path.basename(file)
                                                # 完全匹配：JPG文件名以SKU货号开头
                                                if jpg_name.lower().startswith(sku_number.lower()):
                                                    best_match_folders.append((folder_path, sku_number, jpg_name))
                                                    logging.info(f"🏷️ [大标移动] ✅ 找到完全匹配: {folder_name} 中的 {jpg_name} 匹配 SKU {sku_number}")
                                                    break
                                        if best_match_folders and best_match_folders[-1][0] == folder_path:
                                            break
                                    if best_match_folders and best_match_folders[-1][0] == folder_path:
                                        break

                        if best_match_folders:
                            # 有完全匹配的文件夹，选择第一个
                            best_folder = best_match_folders[0][0]
                            matched_sku = best_match_folders[0][1]
                            matched_jpg = best_match_folders[0][2]
                            logging.info(f"🏷️ [大标移动] ✅ 选择完全匹配文件夹: {os.path.basename(best_folder)} (SKU: {matched_sku}, JPG: {matched_jpg})")
                        else:
                            # 没有完全匹配，使用第一个部分匹配的文件夹
                            best_folder = sku_matched_folders[0]
                            logging.info(f"🏷️ [大标移动] ⚠️ 无完全匹配，使用第一个部分匹配文件夹: {os.path.basename(best_folder)}")
                    else:
                        logging.warning(f"🏷️ [大标移动] ❌ 通过SKU货号也未找到匹配的文件夹，跳过PDF: {pdf_file}")
                        continue
                else:
                    logging.warning(f"🏷️ [大标移动] ❌ 无法从PDF {pdf_file} 中提取SKU货号，跳过")
                    continue
            elif len(skc_matched_folders) == 1:
                # 只有一个SKC匹配的文件夹，直接移动，不需要尺寸判断
                best_folder = skc_matched_folders[0]
                logging.info(f"🏷️ [大标移动] ✅ 唯一SKC匹配，直接移动: {pdf_file} -> {os.path.basename(best_folder)}")
            else:
                # 如果还没有选择best_folder，说明是传统的SKC匹配模式
                if not best_folder:
                    # 有多个SKC匹配的文件夹，使用尺寸判断来选择最佳文件夹
                    logging.info(f"🏷️ [大标移动] 🔍 找到 {len(skc_matched_folders)} 个SKC匹配的文件夹，开始尺寸判断...")

                    if pdf_standardized_size:
                        # 有尺寸信息，进行尺寸匹配
                        for folder_path in skc_matched_folders:
                            folder_name = os.path.basename(folder_path)
                            folder_size = extract_size_from_folder_name(folder_name)

                            if folder_size:
                                folder_standardized_size = parse_and_standardize_size(folder_size)
                                score = calculate_size_similarity(pdf_standardized_size, folder_standardized_size)

                                logging.info(f"🏷️ [大标移动] 尺寸匹配: {pdf_file} ({pdf_standardized_size}) vs {folder_name} ({folder_standardized_size}) = {score:.2f}")

                                if score > best_score:
                                    best_score = score
                                    best_folder = folder_path

                    if not best_folder:
                        # 没有找到尺寸匹配，使用第一个SKC匹配的文件夹
                        best_folder = skc_matched_folders[0]
                        logging.info(f"🏷️ [大标移动] ⚠️ 无尺寸匹配，使用第一个SKC匹配文件夹: {os.path.basename(best_folder)}")
                    else:
                        logging.info(f"🏷️ [大标移动] ✅ 尺寸匹配成功: {pdf_file} -> {os.path.basename(best_folder)} (得分: {best_score:.2f})")
                # 如果已经通过SKU完全匹配选择了best_folder，直接使用

            # 移动PDF文件
            if best_folder:
                success = move_big_pdf_to_folder(big_pdf_folder, pdf_file, best_folder)
                if success:
                    moved_count += 1
                    logging.info(f"🏷️ [大标移动] ✅ 成功移动: {pdf_file} -> {os.path.basename(best_folder)}")
                else:
                    logging.error(f"🏷️ [大标移动] ❌ 移动失败: {pdf_file}")
            else:
                logging.warning(f"🏷️ [大标移动] ⚠️ 没有找到合适的目标文件夹: {pdf_file}")

    logging.info(f"🏷️ [大标移动] 移动完成，共移动了 {moved_count} 个大标PDF文件")

    # 为每个属性集文件夹创建合并的大标PDF
    logging.info(f"🏷️ [大标合并] 开始为各属性集文件夹创建合并的大标PDF...")
    create_merged_big_label_pdfs(target_base_folder)

    # 移动完成后，删除原始大标PDF文件夹
    try:
        if os.path.exists(big_pdf_folder):
            # 检查文件夹是否为空或只包含已处理的文件
            remaining_files = []
            if os.path.isdir(big_pdf_folder):
                for filename in os.listdir(big_pdf_folder):
                    file_path = os.path.join(big_pdf_folder, filename)
                    if os.path.isfile(file_path):
                        remaining_files.append(filename)

            if remaining_files:
                logging.info(f"🏷️ [大标清理] 大标PDF文件夹中还有 {len(remaining_files)} 个文件，不删除文件夹")
                logging.info(f"🏷️ [大标清理] 剩余文件: {remaining_files[:5]}{'...' if len(remaining_files) > 5 else ''}")
            else:
                # 删除空的大标PDF文件夹
                import shutil
                shutil.rmtree(big_pdf_folder)
                logging.info(f"🏷️ [大标清理] ✅ 已自动删除处理完成的大标PDF文件夹: {big_pdf_folder}")
    except Exception as e:
        logging.error(f"🏷️ [大标清理] 删除大标PDF文件夹时出错: {big_pdf_folder}，原因: {e}")

def move_big_pdf_to_folder(source_folder, pdf_filename, target_folder):
    """
    移动单个大标PDF文件到目标文件夹的大标子文件夹

    Args:
        source_folder: 源文件夹路径
        pdf_filename: PDF文件名
        target_folder: 目标属性集文件夹路径

    Returns:
        bool: 移动是否成功
    """
    try:
        # 创建目标文件夹中的大标子文件夹
        big_label_folder = os.path.join(target_folder, "大标")
        os.makedirs(big_label_folder, exist_ok=True)

        # 源文件路径
        source_path = os.path.join(source_folder, pdf_filename)
        # 目标文件路径
        target_path = os.path.join(big_label_folder, pdf_filename)

        if not os.path.exists(source_path):
            logging.error(f"🏷️ [大标移动] 源文件不存在: {source_path}")
            return False

        # 如果目标文件已存在，跳过
        if os.path.exists(target_path):
            logging.info(f"🏷️ [大标移动] 目标文件已存在，跳过: {target_path}")
            return True

        # 移动文件
        shutil.move(source_path, target_path)
        logging.info(f"🏷️ [大标移动] 文件移动成功: {pdf_filename} -> {big_label_folder}")
        return True

    except Exception as e:
        logging.error(f"🏷️ [大标移动] 移动文件失败: {pdf_filename}, 错误: {e}")
        return False

def create_merged_big_label_pdfs(target_base_folder):
    """
    为每个属性集文件夹创建合并的大标PDF文件
    遍历所有属性集文件夹，将其中"大标"子文件夹的所有PDF合并为一个"大标.pdf"

    Args:
        target_base_folder: 目标基础文件夹路径
    """
    logging.info(f"🏷️ [大标合并] 开始扫描属性集文件夹: {target_base_folder}")

    if not os.path.exists(target_base_folder):
        logging.warning(f"🏷️ [大标合并] 目标文件夹不存在: {target_base_folder}")
        return

    merged_count = 0

    # 遍历所有属性集文件夹
    for item in os.listdir(target_base_folder):
        item_path = os.path.join(target_base_folder, item)
        if not os.path.isdir(item_path):
            continue

        # 检查是否有大标子文件夹
        big_label_folder = os.path.join(item_path, "大标")
        if not os.path.exists(big_label_folder):
            logging.info(f"🏷️ [大标合并] 跳过（无大标文件夹）: {item}")
            continue

        # 获取大标文件夹中的所有PDF文件
        pdf_files = []
        for filename in os.listdir(big_label_folder):
            if filename.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(big_label_folder, filename))

        if not pdf_files:
            logging.info(f"🏷️ [大标合并] 跳过（大标文件夹为空）: {item}")
            continue

        # 按文件名排序，确保合并顺序一致
        pdf_files.sort()
        logging.info(f"🏷️ [大标合并] 处理属性集: {item}")
        logging.info(f"🏷️ [大标合并] 找到 {len(pdf_files)} 个PDF文件: {[os.path.basename(f) for f in pdf_files]}")

        # 合并PDF文件
        merged_pdf_path = os.path.join(item_path, "大标.pdf")
        success = merge_pdfs_to_file(pdf_files, merged_pdf_path)

        if success:
            merged_count += 1
            logging.info(f"🏷️ [大标合并] ✅ 成功创建合并PDF: {merged_pdf_path}")
        else:
            logging.error(f"🏷️ [大标合并] ❌ 合并失败: {item}")

    logging.info(f"🏷️ [大标合并] 合并完成，共为 {merged_count} 个属性集创建了合并的大标PDF")

def merge_pdfs_to_file(pdf_files, output_path):
    """
    将多个PDF文件合并为一个PDF文件

    Args:
        pdf_files: PDF文件路径列表
        output_path: 输出文件路径

    Returns:
        bool: 合并是否成功
    """
    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        logging.error("🏷️ [大标合并] PyPDF2库不可用，无法合并PDF")
        return False

    try:
        merger = PyPDF2.PdfMerger()

        for pdf_file in pdf_files:
            if os.path.exists(pdf_file):
                logging.info(f"🏷️ [大标合并] 添加文件: {os.path.basename(pdf_file)}")
                merger.append(pdf_file)
            else:
                logging.warning(f"🏷️ [大标合并] 文件不存在，跳过: {pdf_file}")

        # 写入合并后的PDF
        with open(output_path, 'wb') as output_file:
            merger.write(output_file)

        merger.close()
        logging.info(f"🏷️ [大标合并] PDF合并成功: {output_path}")
        return True

    except Exception as e:
        logging.error(f"🏷️ [大标合并] PDF合并失败: {output_path}, 错误: {e}")
        return False

def process_big_labels_for_folder_optimized(attr_folder_path, big_pdf_folder, all_big_pdf_files, skcs):
    """
    优化版本：为指定的属性集文件夹处理大标PDF文件
    加强文件完整性检查和错误处理
    """
    logging.info(f"处理属性集文件夹: {attr_folder_path} (包含SKC: {skcs})")

    # 查找数量文件夹（如"1套"、"2张"等）
    quantity_folders = []
    try:
        for item in os.listdir(attr_folder_path):
            item_path = os.path.join(attr_folder_path, item)
            if os.path.isdir(item_path) and (item.endswith('套') or item.endswith('张')):
                quantity_folders.append(item_path)
    except Exception as e:
        logging.error(f"读取属性集文件夹失败: {attr_folder_path}，原因: {e}")
        return

    if not quantity_folders:
        logging.warning(f"在 {attr_folder_path} 中未找到数量文件夹")
        return

    # 1. 在属性集文件夹中创建大标文件夹（与数量文件夹并列）
    big_label_folder = os.path.join(attr_folder_path, '大标')
    try:
        os.makedirs(big_label_folder, exist_ok=True)
        logging.info(f"创建大标文件夹: {big_label_folder}")
    except Exception as e:
        logging.error(f"创建大标文件夹失败: {big_label_folder}，原因: {e}")
        return

    # 2. 复制所有对应的大标PDF文件到大标文件夹
    copied_files = []
    failed_copies = []

    for pdf_file in all_big_pdf_files:
        source_path = os.path.join(big_pdf_folder, pdf_file)
        target_path = os.path.join(big_label_folder, pdf_file)

        try:
            if os.path.exists(source_path):
                # 检查源文件大小
                source_size = os.path.getsize(source_path)
                if source_size == 0:
                    logging.warning(f"源大标PDF文件为空: {source_path}")
                    continue

                # 如果目标文件已存在，检查大小是否一致
                if os.path.exists(target_path):
                    target_size = os.path.getsize(target_path)
                    if target_size == source_size:
                        copied_files.append(pdf_file)
                        logging.info(f"大标PDF已存在且大小一致，跳过: {pdf_file}")
                        continue
                    else:
                        logging.warning(f"目标文件大小不一致，重新复制: {pdf_file}")
                        os.remove(target_path)

                # 移动文件
                shutil.move(source_path, target_path)  # 使用move移动文件

                # 验证复制结果
                if os.path.exists(target_path):
                    target_size = os.path.getsize(target_path)
                    if target_size == source_size:
                        copied_files.append(pdf_file)
                        logging.info(f"移动大标PDF成功: {pdf_file} ({source_size} 字节)")
                    else:
                        failed_copies.append(pdf_file)
                        logging.error(f"移动后文件大小不一致: {pdf_file} (源:{source_size}, 目标:{target_size})")
                else:
                    failed_copies.append(pdf_file)
                    logging.error(f"移动后文件不存在: {pdf_file}")
            else:
                failed_copies.append(pdf_file)
                logging.warning(f"源大标PDF不存在: {source_path}")
        except Exception as e:
            failed_copies.append(pdf_file)
            logging.error(f"移动大标PDF失败: {source_path} 到 {target_path}，原因: {e}")

    # 3. 在属性集文件夹中创建合并的大标PDF（与数量文件夹并列）
    if copied_files:
        merged_pdf_path = os.path.join(attr_folder_path, '大标.pdf')

        # 等待一小段时间确保文件系统同步
        import time
        time.sleep(0.1)

        merge_big_label_pdfs_from_folder_optimized(big_label_folder, merged_pdf_path)
        logging.info(f"已为 {attr_folder_path} 创建合并的大标PDF: {merged_pdf_path}")
    else:
        logging.warning(f"没有成功复制任何大标PDF文件到 {attr_folder_path}，跳过合并步骤")

    # 输出处理结果
    logging.info(f"完成处理属性集文件夹: {attr_folder_path}")
    logging.info(f"  成功移动: {len(copied_files)} 个文件")
    if failed_copies:
        logging.warning(f"  移动失败: {len(failed_copies)} 个文件: {failed_copies}")

def find_attribute_folders(target_base_folder, skc_base):
    """
    查找包含指定SKC的属性集文件夹
    """
    attribute_folders = []

    if not os.path.exists(target_base_folder):
        return attribute_folders

    # 遍历目标文件夹，查找包含SKC图片的文件夹
    for folder_name in os.listdir(target_base_folder):
        folder_path = os.path.join(target_base_folder, folder_name)
        if os.path.isdir(folder_path):
            # 检查文件夹内是否有对应SKC的图片或文件
            if folder_contains_skc(folder_path, skc_base):
                attribute_folders.append(folder_path)

    return attribute_folders

def folder_contains_skc(folder_path, skc_base):
    """
    检查文件夹是否包含指定SKC的文件
    """
    try:
        # 递归检查文件夹及其子文件夹
        for _, _, files in os.walk(folder_path):
            for file in files:
                if file.lower().startswith(skc_base.lower()) and (file.lower().endswith('.jpg') or file.lower().endswith('.pdf')):
                    return True
        return False
    except Exception as e:
        logging.error(f"检查文件夹 {folder_path} 时出错: {e}")
        return False

def process_big_labels_for_folder(attr_folder_path, big_pdf_folder, big_pdf_files, skc_base):
    """
    为指定的属性集文件夹处理大标PDF文件
    1. 在属性集文件夹中创建大标文件夹（与数量文件夹并列）
    2. 复制所有对应的大标PDF文件到大标文件夹
    3. 在属性集文件夹中创建合并的大标.pdf文件（与数量文件夹并列）
    """
    logging.info(f"处理属性集文件夹: {attr_folder_path} (SKC: {skc_base})")

    # 查找数量文件夹（如"1套"、"2张"等）
    quantity_folders = []
    for item in os.listdir(attr_folder_path):
        item_path = os.path.join(attr_folder_path, item)
        if os.path.isdir(item_path) and (item.endswith('套') or item.endswith('张')):
            quantity_folders.append(item_path)

    if not quantity_folders:
        logging.warning(f"在 {attr_folder_path} 中未找到数量文件夹")
        return

    # 1. 在属性集文件夹中创建大标文件夹（与数量文件夹并列）
    big_label_folder = os.path.join(attr_folder_path, '大标')
    os.makedirs(big_label_folder, exist_ok=True)
    logging.info(f"创建大标文件夹: {big_label_folder}")

    # 2. 复制所有对应的大标PDF文件到大标文件夹
    copied_files = []
    for pdf_file in big_pdf_files:
        source_path = os.path.join(big_pdf_folder, pdf_file)
        target_path = os.path.join(big_label_folder, pdf_file)

        try:
            if os.path.exists(source_path):
                if not os.path.exists(target_path):
                    shutil.move(source_path, target_path)  # 使用移动操作
                    copied_files.append(pdf_file)
                    logging.info(f"移动大标PDF: {pdf_file} 到 {big_label_folder}")
                else:
                    copied_files.append(pdf_file)
                    logging.info(f"大标PDF已存在，跳过: {target_path}")
            else:
                logging.warning(f"源大标PDF不存在: {source_path}")
        except Exception as e:
            logging.error(f"移动大标PDF失败: {source_path} 到 {target_path}，原因: {e}")

    # 3. 在属性集文件夹中创建合并的大标PDF（与数量文件夹并列）
    # 合并属性集文件夹中大标文件夹里的所有PDF文件
    if copied_files:
        merged_pdf_path = os.path.join(attr_folder_path, '大标.pdf')
        merge_big_label_pdfs_from_folder(big_label_folder, merged_pdf_path)
        logging.info(f"已为 {attr_folder_path} 创建合并的大标PDF: {merged_pdf_path}")
    else:
        logging.warning(f"没有成功移动任何大标PDF文件到 {attr_folder_path}，跳过合并步骤")

    logging.info(f"完成处理属性集文件夹: {attr_folder_path} (移动了 {len(copied_files)} 个文件)")

def merge_big_label_pdfs(big_pdf_folder, big_pdf_files, output_path):
    """
    合并多个大标PDF文件为一个文件
    """
    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        return

    try:
        if not big_pdf_files:
            logging.warning(f"没有大标PDF文件需要合并: {output_path}")
            return

        writer = PyPDF2.PdfWriter()
        merged_count = 0

        # 按文件名排序，确保页面顺序正确
        sorted_files = sorted(big_pdf_files)
        logging.info(f"开始合并 {len(sorted_files)} 个大标PDF文件: {sorted_files}")

        for pdf_file in sorted_files:
            source_path = os.path.join(big_pdf_folder, pdf_file)
            if os.path.exists(source_path):
                try:
                    with open(source_path, 'rb') as file:
                        reader = PyPDF2.PdfReader(file)
                        page_count = len(reader.pages)
                        for page in reader.pages:
                            writer.add_page(page)
                        merged_count += 1
                        logging.info(f"已合并 {pdf_file} ({page_count} 页)")
                except Exception as e:
                    logging.error(f"合并PDF文件 {pdf_file} 时出错: {e}")
            else:
                logging.warning(f"大标PDF文件不存在: {source_path}")

        # 写入合并后的PDF
        if merged_count > 0:
            if not os.path.exists(output_path):
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)
                logging.info(f"合并大标PDF完成: {output_path} (合并了 {merged_count} 个文件)")
            else:
                logging.info(f"合并大标PDF已存在，跳过: {output_path}")
        else:
            logging.error(f"没有成功合并任何PDF文件，无法创建: {output_path}")

    except Exception as e:
        logging.error(f"合并大标PDF失败: {output_path}，原因: {e}")
        import traceback
        logging.error(f"详细错误信息: {traceback.format_exc()}")

def merge_big_label_pdfs_from_folder_optimized(big_label_folder, output_path):
    """
    优化版本：合并指定文件夹中的所有PDF文件为一个文件
    加强错误处理和文件完整性检查
    """
    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        return

    try:
        if not os.path.exists(big_label_folder):
            logging.warning(f"大标文件夹不存在: {big_label_folder}")
            return

        # 获取文件夹中所有PDF文件
        pdf_files = []
        for filename in os.listdir(big_label_folder):
            if filename.lower().endswith('.pdf'):
                file_path = os.path.join(big_label_folder, filename)
                # 检查文件是否可读且不为空
                try:
                    file_size = os.path.getsize(file_path)
                    if file_size > 0:
                        pdf_files.append(filename)
                    else:
                        logging.warning(f"跳过空文件: {filename}")
                except Exception as e:
                    logging.warning(f"无法读取文件信息: {filename}，原因: {e}")

        if not pdf_files:
            logging.warning(f"大标文件夹中没有有效的PDF文件: {big_label_folder}")
            return

        writer = PyPDF2.PdfWriter()
        merged_count = 0
        total_pages = 0

        # 按文件名排序，确保页面顺序正确
        sorted_files = sorted(pdf_files)
        logging.info(f"开始合并大标文件夹中的 {len(sorted_files)} 个PDF文件")

        for pdf_file in sorted_files:
            source_path = os.path.join(big_label_folder, pdf_file)
            try:
                # 多次尝试读取文件，处理文件锁定问题
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        with open(source_path, 'rb') as file:
                            reader = PyPDF2.PdfReader(file)
                            page_count = len(reader.pages)

                            if page_count == 0:
                                logging.warning(f"PDF文件没有页面: {pdf_file}")
                                break

                            for page_num, page in enumerate(reader.pages):
                                try:
                                    writer.add_page(page)
                                    total_pages += 1
                                except Exception as e:
                                    logging.error(f"添加页面失败: {pdf_file} 第{page_num+1}页，原因: {e}")

                            merged_count += 1
                            logging.info(f"已合并 {pdf_file} ({page_count} 页)")
                            break  # 成功读取，跳出重试循环

                    except Exception as e:
                        if attempt < max_retries - 1:
                            logging.warning(f"读取PDF文件失败，重试 {attempt + 1}/{max_retries}: {pdf_file}，原因: {e}")
                            import time
                            time.sleep(0.1)  # 短暂等待后重试
                        else:
                            logging.error(f"多次尝试后仍无法读取PDF文件: {pdf_file}，原因: {e}")

            except Exception as e:
                logging.error(f"处理PDF文件时出错: {pdf_file}，原因: {e}")

        # 写入合并后的PDF
        if merged_count > 0 and total_pages > 0:
            try:
                # 确保输出目录存在
                output_dir = os.path.dirname(output_path)
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir, exist_ok=True)

                # 写入文件
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)

                # 验证输出文件
                if os.path.exists(output_path):
                    output_size = os.path.getsize(output_path)
                    if output_size > 0:
                        logging.info(f"合并大标PDF完成: {output_path}")
                        logging.info(f"  合并了 {merged_count} 个文件，共 {total_pages} 页，大小 {output_size} 字节")
                    else:
                        logging.error(f"合并后的PDF文件为空: {output_path}")
                else:
                    logging.error(f"合并后的PDF文件不存在: {output_path}")

            except Exception as e:
                logging.error(f"写入合并PDF文件失败: {output_path}，原因: {e}")
        else:
            logging.error(f"没有成功合并任何PDF文件，无法创建: {output_path}")
            logging.error(f"  merged_count: {merged_count}, total_pages: {total_pages}")

    except Exception as e:
        logging.error(f"合并大标PDF失败: {output_path}，原因: {e}")
        import traceback
        logging.error(f"详细错误信息: {traceback.format_exc()}")

def merge_big_label_pdfs_from_folder(big_label_folder, output_path):
    """
    合并指定文件夹中的所有PDF文件为一个文件
    """
    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        return

    try:
        if not os.path.exists(big_label_folder):
            logging.warning(f"大标文件夹不存在: {big_label_folder}")
            return

        # 获取文件夹中所有PDF文件
        pdf_files = []
        for filename in os.listdir(big_label_folder):
            if filename.lower().endswith('.pdf'):
                pdf_files.append(filename)

        if not pdf_files:
            logging.warning(f"大标文件夹中没有PDF文件: {big_label_folder}")
            return

        writer = PyPDF2.PdfWriter()
        merged_count = 0

        # 按文件名排序，确保页面顺序正确
        sorted_files = sorted(pdf_files)
        logging.info(f"开始合并大标文件夹中的 {len(sorted_files)} 个PDF文件: {sorted_files}")

        for pdf_file in sorted_files:
            source_path = os.path.join(big_label_folder, pdf_file)
            try:
                with open(source_path, 'rb') as file:
                    reader = PyPDF2.PdfReader(file)
                    page_count = len(reader.pages)
                    for page in reader.pages:
                        writer.add_page(page)
                    merged_count += 1
                    logging.info(f"已合并 {pdf_file} ({page_count} 页)")
            except Exception as e:
                logging.error(f"合并PDF文件 {pdf_file} 时出错: {e}")

        # 写入合并后的PDF
        if merged_count > 0:
            # 强制覆盖已存在的文件，确保内容是最新的
            with open(output_path, 'wb') as output_file:
                writer.write(output_file)
            logging.info(f"合并大标PDF完成: {output_path} (合并了 {merged_count} 个文件)")
        else:
            logging.error(f"没有成功合并任何PDF文件，无法创建: {output_path}")

    except Exception as e:
        logging.error(f"合并大标PDF失败: {output_path}，原因: {e}")
        import traceback
        logging.error(f"详细错误信息: {traceback.format_exc()}")



# 主界面
# 全局变量，用于保存上次选择的路径和UI组件
last_folder_paths = {
    "path_entry": "",
    "pdf_entry": "",
    "source_entry": "",
    "small_entry": ""
}

# 全局UI组件变量
path_entry = None
pdf_entry = None
source_entry = None
small_entry = None

def load_paths():
    """加载路径配置 - 使用统一配置"""
    config = load_unified_config()
    paths = config.get("paths", {})
    main_folder = paths.get("main_folder", "")
    img_folder = paths.get("img_folder", "")

    # 兼容性：如果统一配置中没有，尝试从旧文件读取
    if not main_folder and not img_folder:
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    main_folder = data.get("main_folder", "")
                    img_folder = data.get("img_folder", "")
        except Exception:
            pass

    return main_folder, img_folder

def save_paths(main_folder, img_folder):
    """保存路径配置 - 只使用统一配置"""
    config = load_unified_config()
    config["paths"]["main_folder"] = main_folder
    config["paths"]["img_folder"] = img_folder
    save_unified_config(config)

    # 不再保存到旧文件，只使用统一配置

def get_auto_date_and_time():
    """自动获取当前日期和时间段"""
    now = datetime.datetime.now()

    # 格式化日期为 M-D 格式（如 4-15）
    date_str = f"{now.month}-{now.day}"

    # 判断时间段：13点前为上午，13点后为下午
    time_period = "上午" if now.hour < 13 else "下午"

    return date_str, time_period

def load_folder_config():
    """加载文件夹命名配置 - 使用统一配置"""
    config = load_unified_config()
    folder_config = config.get("folder_config", {})

    # 自动更新日期和时间段
    auto_date, auto_time_period = get_auto_date_and_time()

    result = {
        "date": auto_date,  # 使用自动获取的日期
        "time_period": auto_time_period,  # 使用自动获取的时间段
        "operator": folder_config.get("operator", ""),
        "shop_number": folder_config.get("shop_number", ""),
        "operator_history": folder_config.get("operator_history", []),
        "shop_history": folder_config.get("shop_history", [])
    }

    # 兼容性：如果统一配置中没有，尝试从旧文件读取
    if not result["operator"] and not result["shop_number"]:
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    old_config = data.get("folder_config", {})
                    result["operator"] = old_config.get("operator", "")
                    result["shop_number"] = old_config.get("shop_number", "")
                    result["operator_history"] = old_config.get("operator_history", [])
                    result["shop_history"] = old_config.get("shop_history", [])
        except Exception:
            pass

    return result

def save_folder_config(folder_config):
    """保存文件夹命名配置 - 只使用统一配置"""
    config = load_unified_config()
    config["folder_config"] = folder_config
    save_unified_config(config)

    # 不再保存到旧文件，只使用统一配置

def add_to_history(history_list, new_item, max_items=10):
    """添加项目到历史记录，避免重复并限制数量"""
    if new_item and new_item.strip():
        item = new_item.strip()
        if item in history_list:
            history_list.remove(item)
        history_list.insert(0, item)
        return history_list[:max_items]
    return history_list

def parse_date(date_str):
    """解析日期字符串，返回月和日"""
    try:
        if '-' in date_str:
            parts = date_str.split('-')
            if len(parts) == 2:
                month = int(parts[0])
                day = int(parts[1])
                return month, day
    except:
        pass
    return None, None

def format_date(month, day):
    """格式化日期为字符串"""
    return f"{month}-{day}"

def adjust_date(date_str, days):
    """调整日期，支持跨月"""
    month, day = parse_date(date_str)
    if month is None or day is None:
        return date_str

    # 简单的日期调整逻辑（不考虑闰年等复杂情况）
    days_in_month = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]

    day += days

    while day > days_in_month[month - 1]:
        day -= days_in_month[month - 1]
        month += 1
        if month > 12:
            month = 1

    while day < 1:
        month -= 1
        if month < 1:
            month = 12
        day += days_in_month[month - 1]

    return format_date(month, day)

def select_folder(entry):
    """选择文件夹并更新配置"""
    folder_path = filedialog.askdirectory()
    if folder_path:
        entry.delete(0, tk.END)
        entry.insert(0, folder_path)
        last_folder_paths[entry._name] = folder_path
        # 获取当前的img_folder路径
        current_img_folder = last_folder_paths.get("source_entry", "")
        save_paths(folder_path, current_img_folder)  # 立即保存配置
        logging.info(f"已选择文件夹: {folder_path}")

def select_pdf(entry):
    """选择PDF文件并更新配置"""
    pdf_path = filedialog.askopenfilename(filetypes=[("PDF Files", "*.pdf")])
    if pdf_path:
        entry.delete(0, tk.END)
        entry.insert(0, pdf_path)
        last_folder_paths[entry._name] = pdf_path
        # 同时保存父目录路径
        parent_dir = os.path.dirname(pdf_path)
        if parent_dir:
            last_folder_paths[f"{entry._name}_dir"] = parent_dir
        # 获取当前的main_folder路径
        current_main_folder = last_folder_paths.get("path_entry", "")
        save_paths(current_main_folder, parent_dir)  # 立即保存配置
        logging.info(f"已选择PDF文件: {pdf_path}")

def create_main_window():
    try:
        # 检查激活状态
        if not is_activated():
            prompt_for_activation()
            if not is_activated():  # 再次检查，以防用户关闭激活窗口
                messagebox.showerror("错误", "软件未激活，无法使用")
                return

        global path_entry, pdf_entry, source_entry, small_entry
        root = tk.Tk()
        root.title("文件处理工具")
        root.geometry("670x650")
        root.configure(bg='lightgray')
        large_font = tk.font.Font(size=12, family='Arial', weight='bold')

        # 功能一界面
        tk.Label(root, text="牛马利器", font=large_font, bg='lightgray').grid(row=0, column=0, columnspan=3, pady=10)
        
        # 创建输入框并设置名称
        path_entry = tk.Entry(root, font=large_font, width=50, bg='white')
        path_entry._name = "path_entry"
        
        pdf_entry = tk.Entry(root, font=large_font, width=50, bg='white')
        pdf_entry._name = "pdf_entry"
        
        source_entry = tk.Entry(root, font=large_font, width=50, bg='white')
        source_entry._name = "source_entry"
        
        small_entry = tk.Entry(root, font=large_font, width=50, bg='white')
        small_entry._name = "small_entry"

        # 加载保存的路径
        load_paths()
        
        # 设置输入框的初始值
        path_entry.insert(0, last_folder_paths.get("path_entry", ""))
        pdf_entry.insert(0, last_folder_paths.get("pdf_entry", ""))
        source_entry.insert(0, last_folder_paths.get("source_entry", ""))
        small_entry.insert(0, last_folder_paths.get("small_entry", ""))

        # 布局界面元素
        tk.Label(root, text="文件夹：", font=large_font, bg='lightgray').grid(row=1, column=0, sticky='e', padx=5, pady=5)
        path_entry.grid(row=1, column=1, padx=5, pady=5)
        tk.Button(root, text="选择文件夹", command=lambda: select_folder(path_entry), font=large_font, bg='lightgray').grid(row=1, column=2)
        
        tk.Label(root, text="拣货单：", font=large_font, bg='lightgray').grid(row=2, column=0, sticky='e', padx=5, pady=5)
        pdf_entry.grid(row=2, column=1, padx=5, pady=5)
        tk.Button(root, text="选择文件", command=lambda: select_pdf(pdf_entry), font=large_font, bg='lightgray').grid(row=2, column=2)
        
        tk.Label(root, text="原图：", font=large_font, bg='lightgray').grid(row=3, column=0, sticky='e', padx=5, pady=5)
        source_entry.grid(row=3, column=1, padx=5, pady=5)
        tk.Button(root, text="选择文件夹", command=lambda: select_folder(source_entry), font=large_font, bg='lightgray').grid(row=3, column=2)
        
        tk.Label(root, text="小标：", font=large_font, bg='lightgray').grid(row=4, column=0, sticky='e', padx=5, pady=5)
        small_entry.grid(row=4, column=1, padx=5, pady=5)
        tk.Button(root, text="选择文件", command=lambda: select_pdf(small_entry), font=large_font, bg='lightgray').grid(row=4, column=2)

        # 进度条
        progress_bar = ttk.Progressbar(root, orient='horizontal', length=400, mode='determinate', style='TProgressbar')
        progress_bar.grid(row=5, column=0, columnspan=3, pady=20)
        progress_label = tk.Label(root, text="0.00%", font=large_font, bg='lightgray')
        progress_label.grid(row=6, column=0, columnspan=3, pady=10)
        
        # 功能一按钮
        tk.Button(root, text="起飞", command=lambda: run_script_1(progress_bar, progress_label), font=large_font, width=20, bg='lightgray').grid(row=7, column=0, columnspan=3, pady=20)



        # 设置窗口关闭事件
        def on_closing():
            save_paths(path_entry.get(), source_entry.get())
            root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)
        root.mainloop()
    except Exception as e:
        logging.error(f"创建主窗口时发生错误: {e}")
        messagebox.showerror("错误", f"创建主窗口时发生错误: {e}")

def parse_canvas_size(text):
    """
    解析类似"帆布画-24in/60cmx16in/40cmx3pc"等任意顺序、分隔符、数量的尺寸描述，返回标准尺寸表达式。
    返回值示例：('24x16inch', '60x40cm', 3)
    """
    # 统一所有分隔符为"|"
    text_norm = re.sub(r'[xX×/\\\-]', '|', text)
    # 全局提取所有 inch 和 cm 数值
    inch_vals = re.findall(r'(\d+\.?\d*)[^\d]*?(?:in|inch)', text_norm, re.IGNORECASE)
    cm_vals = re.findall(r'(\d+\.?\d*)[^\d]*?cm', text_norm, re.IGNORECASE)
    logging.info(f"parse_canvas_size inch_vals: {inch_vals}, cm_vals: {cm_vals}")
    # 组装字符串
    if len(inch_vals) >= 2:
        inch_str = f"{inch_vals[0]}x{inch_vals[1]}inch"
    else:
        inch_str = None
    if len(cm_vals) >= 2:
        cm_str = f"{cm_vals[0]}x{cm_vals[1]}cm"
    else:
        cm_str = None
    if not (inch_str and cm_str):
        return None
    # 查找数量（如3pc，允许任意位置）
    count_match = re.search(r'(\d+)\s*pc', text, re.IGNORECASE)
    count = int(count_match.group(1)) if count_match else 1
    return inch_str, cm_str, count

def auto_find_file(folder, keywords):
    """
    在folder下查找文件名包含keywords任一关键字的PDF文件，返回第一个匹配的完整路径。
    """
    for fname in os.listdir(folder):
        if fname.lower().endswith('.pdf') and any(kw.lower() in fname.lower() for kw in keywords):
            return os.path.join(folder, fname)
    return None

# ========== ttkbootstrap 美化UI主界面入口 ==========
# 延迟导入ttkbootstrap - 只在需要时导入
def lazy_import_ttkbootstrap():
    """延迟导入ttkbootstrap"""
    try:
        import ttkbootstrap as tb
        from ttkbootstrap.constants import PRIMARY, SECONDARY, INFO, WARNING, SUCCESS, DANGER
        return tb, PRIMARY, SECONDARY, INFO, WARNING, SUCCESS, DANGER
    except ImportError:
        messagebox.showerror("错误", "缺少ttkbootstrap库，请安装：pip install ttkbootstrap")
        return None, None, None, None, None, None, None

def show_startup_splash():
    """显示启动加载画面"""
    # 创建一个临时的根窗口用于启动画面
    splash_root = tk.Tk()
    splash_root.withdraw()  # 隐藏主窗口

    splash = tk.Toplevel(splash_root)
    splash.title("牛马利器")
    splash.geometry("450x320")
    splash.configure(bg="#eaf4fb")
    splash.resizable(False, False)
    splash.overrideredirect(True)  # 无边框窗口

    # 居中显示
    splash.update_idletasks()
    x = (splash.winfo_screenwidth() // 2) - (450 // 2)
    y = (splash.winfo_screenheight() // 2) - (320 // 2)
    splash.geometry(f"450x320+{x}+{y}")

    # 添加阴影效果的边框
    border_frame = tk.Frame(splash, bg="#d0d0d0", bd=1, relief="solid")
    border_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

    main_frame = tk.Frame(border_frame, bg="#eaf4fb")
    main_frame.pack(fill=tk.BOTH, expand=True, padx=1, pady=1)

    # Logo和标题
    logo_label = tk.Label(main_frame, text="🐳", font=("Segoe UI Emoji", 48), bg="#eaf4fb")
    logo_label.pack(pady=(50, 10))

    title_label = tk.Label(main_frame, text="牛马利器", font=("微软雅黑", 24, "bold"), fg="#1976d2", bg="#eaf4fb")
    title_label.pack(pady=5)

    subtitle_label = tk.Label(main_frame, text="让做单像牛马一样自然", font=("微软雅黑", 12), fg="#6c757d", bg="#eaf4fb")
    subtitle_label.pack(pady=5)

    version_label = tk.Label(main_frame, text="v2.0 ", font=("微软雅黑", 10), fg="#999", bg="#eaf4fb")
    version_label.pack(pady=(5, 20))

    # 状态文本
    status_label = tk.Label(main_frame, text="正在启动...", font=("微软雅黑", 11), fg="#333", bg="#eaf4fb")
    status_label.pack(pady=10)

    # 进度条容器
    progress_frame = tk.Frame(main_frame, bg="#eaf4fb")
    progress_frame.pack(pady=10)

    # 进度条背景
    progress_bg = tk.Canvas(progress_frame, width=350, height=8, bg="#e0e0e0", highlightthickness=0)
    progress_bg.pack()

    # 进度条
    progress_bar = progress_bg.create_rectangle(0, 0, 0, 8, fill="#1976d2", outline="")

    # 百分比文本
    percent_label = tk.Label(main_frame, text="0%", font=("微软雅黑", 10), fg="#666", bg="#eaf4fb")
    percent_label.pack(pady=5)

    splash.update()
    return splash, status_label, progress_bg, progress_bar, percent_label

def update_startup_progress(splash, status_label, progress_bg, progress_bar, percent_label, percent, message):
    """更新启动进度"""
    if splash and splash.winfo_exists():
        try:
            status_label.config(text=message)
            percent_label.config(text=f"{percent}%")

            # 更新进度条
            width = int(350 * percent / 100)
            progress_bg.coords(progress_bar, 0, 0, width, 8)

            # 添加颜色渐变效果
            if percent < 30:
                color = "#ff9800"  # 橙色
            elif percent < 70:
                color = "#2196f3"  # 蓝色
            else:
                color = "#4caf50"  # 绿色

            progress_bg.itemconfig(progress_bar, fill=color)
            splash.update()

        except tk.TclError:
            pass  # 窗口已关闭

def main():
    # 立即显示启动加载画面
    splash, status_label, progress_bg, progress_bar, percent_label = show_startup_splash()

    try:
        # 步骤1: 检查激活状态 (0-20%)
        update_startup_progress(splash, status_label, progress_bg, progress_bar, percent_label, 10, "检查激活状态...")
        if not is_activated():
            # 关闭启动画面再显示激活对话框
            splash_root = splash.master
            splash.destroy()
            splash_root.destroy()

            prompt_for_activation()
            if not is_activated():
                messagebox.showerror("错误", "软件未激活，无法使用")
                return

            # 重新显示启动画面
            splash, status_label, progress_bg, progress_bar, percent_label = show_startup_splash()

        update_startup_progress(splash, status_label, progress_bg, progress_bar, percent_label, 20, "激活验证完成")

        update_startup_progress(splash, status_label, progress_bg, progress_bar, percent_label, 20, "激活验证完成")

        # 步骤2: 导入UI库 (20-50%)
        update_startup_progress(splash, status_label, progress_bg, progress_bar, percent_label, 30, "加载界面组件...")
        result = lazy_import_ttkbootstrap()
        if result[0] is None:
            splash_root = splash.master
            splash.destroy()
            splash_root.destroy()
            return
        tb, PRIMARY, SECONDARY, INFO, WARNING, SUCCESS, DANGER = result

        update_startup_progress(splash, status_label, progress_bg, progress_bar, percent_label, 50, "界面组件加载完成")

        # 步骤3: 加载配置文件 (50-70%)
        update_startup_progress(splash, status_label, progress_bg, progress_bar, percent_label, 60, "加载配置文件...")

        # 步骤4: 后台加载模板映射 (70-85%)
        update_startup_progress(splash, status_label, progress_bg, progress_bar, percent_label, 75, "加载模板映射...")

        # 在后台异步加载模板映射
        def load_mapping_async():
            try:
                load_template_mapping_from_local()
            except Exception as e:
                logging.warning(f"加载模板映射失败: {e}")

        import threading
        mapping_thread = threading.Thread(target=load_mapping_async, daemon=True)
        mapping_thread.start()

        # 等待一小段时间让映射开始加载
        import time
        time.sleep(0.3)

        # 步骤5: 创建主窗口 (85-95%)
        update_startup_progress(splash, status_label, progress_bg, progress_bar, percent_label, 90, "创建主窗口...")

        # 关闭启动画面，然后创建主窗口
        splash_root = splash.master
        splash.destroy()
        splash_root.destroy()

        # 步骤6: 完成启动 (95-100%)
        # 现在创建主应用窗口
        app = tb.Window(themename="flatly")

    except Exception as e:
        # 确保启动画面被正确关闭
        try:
            if splash and splash.winfo_exists():
                splash_root = splash.master
                splash.destroy()
                splash_root.destroy()
        except:
            pass
        logging.error(f"启动过程中出错: {e}")
        messagebox.showerror("启动错误", f"程序启动失败: {e}")
        return
    app.title("牛马利器")
    app.geometry("900x580")
    app.minsize(800, 520)
    app.configure(bg="#eaf4fb")
    app.resizable(True, True)
    # 顶部LOGO和标题
    topbar = tk.Frame(app, bg="#eaf4fb", height=60)
    topbar.pack(side=tk.TOP, fill=tk.X)
    logo = tk.Label(topbar, text="🐳", font=("Segoe UI Emoji", 28), bg="#eaf4fb")
    logo.pack(side=tk.LEFT, padx=(24, 8), pady=8)
    logo.bind("<Button-1>", lambda _: messagebox.showinfo("彩蛋", "哎呦，你干嘛~~"))
    title = tk.Label(topbar, text="牛马利器", font=("微软雅黑", 22, "bold"), fg="#1976d2", bg="#eaf4fb")
    title.pack(side=tk.LEFT, pady=8)
    subtitle = tk.Label(topbar, text="让做单像牛马一样自然", font=("微软雅黑", 12), fg="#6c757d", bg="#eaf4fb")
    subtitle.pack(side=tk.LEFT, padx=(16,0), pady=8)
    # 主体区域
    main_frame = tk.Frame(app, bg="#eaf4fb")
    main_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=16, pady=(0, 16))
    left_frame = tk.Frame(main_frame, width=360, bg="#fff", highlightthickness=0)
    left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 12), pady=0)
    left_frame.pack_propagate(False)
    # 主文件夹输入
    path_frame = tk.Frame(left_frame, bg="#fff")
    path_frame.pack(fill=tk.X, pady=(18, 8), padx=18)
    tk.Label(path_frame, text="主文件", font=("微软雅黑", 11), fg="#1976d2", bg="#fff").pack(side=tk.LEFT)
    path_var = tk.StringVar()
    path_entry = tk.Entry(path_frame, textvariable=path_var, font=("微软雅黑", 11), width=22, relief="flat", highlightthickness=1, highlightbackground="#e3e7ed", bg="#f5f6fa", fg="#222")
    path_entry.pack(side=tk.LEFT, padx=(8, 6))
    # 路径记忆：初始化时自动填充
    main_path, img_path = load_paths()
    path_var.set(main_path)
    # 原图文件夹输入
    img_frame = tk.Frame(left_frame, bg="#fff")
    img_frame.pack(fill=tk.X, pady=(0, 8), padx=18)
    tk.Label(img_frame, text="原图啊", font=("微软雅黑", 11), fg="#1976d2", bg="#fff").pack(side=tk.LEFT)
    img_var = tk.StringVar()
    img_entry = tk.Entry(img_frame, textvariable=img_var, font=("微软雅黑", 11), width=22, relief="flat", highlightthickness=1, highlightbackground="#e3e7ed", bg="#f5f6fa", fg="#222")
    img_entry.pack(side=tk.LEFT, padx=(8, 6))
    img_var.set(img_path)
    def select_folder():
        result = filedialog.askdirectory()
        if result:
            path_var.set(result)
            save_paths(result, img_var.get())
    tb.Button(path_frame, text="选啊", bootstyle=PRIMARY, width=7, command=select_folder).pack(side=tk.LEFT)
    def select_img_folder():
        result = filedialog.askdirectory()
        if result:
            img_var.set(result)
            save_paths(path_var.get(), result)
    tb.Button(img_frame, text="选啊", bootstyle=PRIMARY, width=7, command=select_img_folder).pack(side=tk.LEFT)

    # 加载文件夹命名配置
    folder_config = load_folder_config()

    # 文件夹命名配置输入框
    config_frame = tk.LabelFrame(left_frame, text="文件夹命名配置（可选）", bg="#fff", fg="#1976d2", font=("微软雅黑", 10, "bold"), labelanchor="n")
    config_frame.pack(fill=tk.X, padx=18, pady=(8, 8))

    # 自动获取日期和时间段（不显示在界面上）
    auto_date, auto_time = get_auto_date_and_time()

    # 创建隐藏的变量用于保存自动获取的值
    date_var = tk.StringVar(value=auto_date)
    time_var = tk.StringVar(value=auto_time)

    # 操作员输入（带历史记录）
    operator_frame = tk.Frame(config_frame, bg="#fff")
    operator_frame.pack(fill=tk.X, pady=2, padx=8)
    tk.Label(operator_frame, text="操作员:", font=("微软雅黑", 9), fg="#1976d2", bg="#fff", width=8).pack(side=tk.LEFT)
    operator_var = tk.StringVar(value=folder_config.get("operator", ""))
    operator_history = folder_config.get("operator_history", [])
    operator_combo = ttk.Combobox(operator_frame, textvariable=operator_var, font=("微软雅黑", 9), width=13, values=operator_history)
    operator_combo.pack(side=tk.LEFT, padx=(5, 0))
    tk.Label(operator_frame, text="(如: 蔡徐坤)", font=("微软雅黑", 8), fg="#6c757d", bg="#fff").pack(side=tk.LEFT, padx=(5, 0))

    # 店铺号选择（带历史记录）
    shop_frame = tk.Frame(config_frame, bg="#fff")
    shop_frame.pack(fill=tk.X, pady=2, padx=8)
    tk.Label(shop_frame, text="店铺号:", font=("微软雅黑", 9), fg="#1976d2", bg="#fff", width=8).pack(side=tk.LEFT)
    shop_var = tk.StringVar(value=folder_config.get("shop_number", ""))
    shop_history = folder_config.get("shop_history", [])
    shop_combo = ttk.Combobox(shop_frame, textvariable=shop_var, font=("微软雅黑", 9), width=13, values=shop_history)
    shop_combo.pack(side=tk.LEFT, padx=(5, 0))
    tk.Label(shop_frame, text="(选择或输入店铺号)", font=("微软雅黑", 8), fg="#6c757d", bg="#fff").pack(side=tk.LEFT, padx=(5, 0))

    # 进度条
    step_frame = tk.Frame(left_frame, bg="#fff")
    step_frame.pack(fill=tk.X, pady=(8, 8), padx=18)
    progress_var = tk.DoubleVar(value=0)
    progress_bar = tb.Progressbar(left_frame, variable=progress_var, length=260, bootstyle=INFO, mode="determinate")
    progress_bar.pack(padx=18, pady=(0, 8), fill=tk.X)
    def log_callback(msg):
        log_text.insert(tk.END, msg + "\n")
        log_text.see(tk.END)
    def progress_callback(val):
        progress_var.set(val)
        progress_bar.update()
    # 起飞按钮
    def on_start():
        main_folder = path_var.get().strip()
        img_folder = img_var.get().strip()
        save_paths(main_folder, img_folder)

        # 获取当前配置（自动获取日期时间）
        auto_date, auto_time = get_auto_date_and_time()
        current_operator = operator_var.get().strip()
        current_shop = shop_var.get().strip()

        # 更新历史记录
        updated_operator_history = add_to_history(operator_history.copy(), current_operator)
        updated_shop_history = add_to_history(shop_history.copy(), current_shop)

        # 保存文件夹命名配置
        current_config = {
            "date": auto_date,
            "time_period": auto_time,
            "operator": current_operator,
            "shop_number": current_shop,
            "operator_history": updated_operator_history,
            "shop_history": updated_shop_history
        }
        save_folder_config(current_config)

        # 更新下拉框的选项
        operator_combo['values'] = updated_operator_history
        shop_combo['values'] = updated_shop_history

        if not main_folder or not img_folder:
            messagebox.showerror("错误", "请先选择主文件夹和原图文件夹！")
            return
        log_text.delete(1.0, tk.END)
        log_callback("🐳 开始处理，请稍候...")
        def task():
            picking_pdf = auto_find_file(main_folder, ["拣货单", "picking"])
            small_pdf = auto_find_file(main_folder, ["小标", "small"])
            big_pdf = auto_find_file(main_folder, ["大标", "big"])  # 现在需要大标PDF

            if not picking_pdf or not small_pdf:
                log_callback("未找到拣货单或小标PDF，请检查主文件夹！")
                return

            # 创建小标PDF文件夹并拆分
            small_pdf_folder = os.path.join(main_folder, '小标PDF')
            os.makedirs(small_pdf_folder, exist_ok=True)
            split_pdf(small_pdf, small_pdf_folder, log_callback)

            # 提取数据
            df = extract_pdf_data(picking_pdf)
            if df.empty:
                log_callback("未能从拣货单PDF中提取任何数据，请检查文件。")
                return

            # 保存DataFrame到全局变量，用于更新属性集功能
            global _last_processed_df
            _last_processed_df = df.copy()

            # 新增：收集未映射的属性集
            unmapped_attributes = collect_unmapped_attributes(df)
            if unmapped_attributes:
                log_callback(f"发现 {len(unmapped_attributes)} 个未映射的属性集：")
                for i, attr_info in enumerate(unmapped_attributes[:5], 1):  # 只显示前5个
                    # 显示清理后的属性集（去换行符）
                    log_callback(f"  {i}. '{attr_info['cleaned']}'")
                if len(unmapped_attributes) > 5:
                    log_callback(f"  ... 还有 {len(unmapped_attributes) - 5} 个")
                log_callback("建议：运行完成后可在映射表中添加这些属性集的映射关系")
            else:
                log_callback("所有属性集都已在映射表中")

            # 检查图片和小标PDF缺失文件，获取统计信息
            missing_files, stats = check_missing_files(df, img_folder, small_pdf_folder)

            # 输出检查结果日志
            log_callback("检查结果：")
            log_callback(f"- 匹配到的图片数量：{stats['matched_images_count']}")
            log_callback(f"- 匹配到的小标PDF数量：{stats['matched_small_pdfs_count']}")
            log_callback(f"- 是否有重复图片：{'有' if stats['has_duplicates'] else '无'}")
            if stats['duplicate_skcs']:
                duplicate_skcs_str = ','.join(stats['duplicate_skcs'])
                log_callback(f"- 重复图片SKC编号：{duplicate_skcs_str}")
            else:
                log_callback("- 重复图片SKC编号：无")

            # 执行原来起飞按钮的功能
            if missing_files:
                if show_missing_files_dialog(missing_files):
                    process_files(
                        df=df,
                        target_base_folder=main_folder,
                        source_folder=img_folder,
                        small_pdf_folder=small_pdf_folder,
                        progress_bar=progress_bar,
                        progress_label=None,
                        progress_callback=progress_callback,
                        folder_config=current_config
                    )
                    log_callback("🐳 起飞成功！")

                    # 起飞功能完成后，执行拆大标功能
                    execute_split_big_functionality(main_folder, big_pdf, picking_pdf, df, log_callback)
                else:
                    log_callback("已取消起飞。")
            else:
                process_files(
                    df=df,
                    target_base_folder=main_folder,
                    source_folder=img_folder,
                    small_pdf_folder=small_pdf_folder,
                    progress_bar=progress_bar,
                    progress_label=None,
                    progress_callback=progress_callback,
                    folder_config=current_config
                )
                log_callback("🐳 起飞成功！")

                # 起飞功能完成后，执行拆大标功能
                execute_split_big_functionality(main_folder, big_pdf, picking_pdf, df, log_callback)
        threading.Thread(target=task, daemon=True).start()

    # 拆大标功能的独立函数（从原来的on_split_big提取）
    def execute_split_big_functionality(main_folder, big_pdf, picking_pdf, df, log_callback):
        """执行拆大标功能"""
        if not big_pdf:
            log_callback("未找到大标PDF，跳过拆大标步骤")
            return

        log_callback("🐳 开始拆分大标PDF...")

        output_dir = os.path.join(main_folder, '大标')
        os.makedirs(output_dir, exist_ok=True)

        # 简化版拆分，不输出详细调试信息
        split_pdf_rename(big_pdf, output_dir, None)  # 不传递log_callback，避免详细日志

        # 统计拆分出的大标PDF数量
        big_pdf_count = 0
        if os.path.exists(output_dir):
            for filename in os.listdir(output_dir):
                if filename.lower().endswith('.pdf'):
                    big_pdf_count += 1

        # 简洁的日志输出
        log_callback("大标检查结果：")
        log_callback(f"- 匹配到的大标PDF数量：{big_pdf_count}")

        # 如果有拣货单，自动移动大标到对应文件夹
        if picking_pdf and not df.empty:
            log_callback("🐳 开始移动大标到对应文件夹...")
            move_big_labels_to_folders(df, main_folder, output_dir)
            log_callback("🐳 大标移动完成！")
        else:
            log_callback("跳过大标移动步骤")

        log_callback("🐳 大标拆分完成！")



    # 表格导入按钮
    def on_import_template():
        file_path = filedialog.askopenfilename(
            title="选择属性集映射表格",
            filetypes=[("Excel文件", "*.xlsx"), ("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        if file_path:
            # 默认使用累计模式，不弹窗询问
            clear_existing = False  # 默认累计模式

            # 如果已有映射，在日志中提示使用累计模式
            if _template_mapping:
                log_callback(f"🔄 检测到已有 {len(_template_mapping)} 条映射，使用累计模式导入...")

            loaded_count = load_template_mapping(file_path, clear_existing=clear_existing)
            if loaded_count > 0:
                log_text.delete(1.0, tk.END)
                log_callback(f"🐳 成功累计导入表格模版: {os.path.basename(file_path)}")
                log_callback(f"本次加载 {loaded_count} 条映射，当前共有 {len(_template_mapping)} 条属性集映射")
                # 显示部分映射内容作为预览
                preview_count = min(5, len(_template_mapping))
                log_callback("映射预览:")
                for _, (original, target) in enumerate(list(_template_mapping.items())[:preview_count]):
                    log_callback(f"  {original} -> {target}")
                if len(_template_mapping) > preview_count:
                    log_callback(f"  ... 还有 {len(_template_mapping) - preview_count} 条映射")
            else:
                log_text.delete(1.0, tk.END)
                log_callback("❌ 表格导入失败，请检查文件格式")
                log_callback("要求：A列为拣货单原属性集，B列为目标属性集名称")
                log_callback("提示：程序会自动清理A列中的换行符和格式问题")

    # 映射表编辑相关函数
    def add_new_mapping_dialog(parent_window, refresh_callback):
        """添加新映射的对话框"""
        add_dialog = tk.Toplevel(parent_window)
        add_dialog.title("添加新映射")
        add_dialog.geometry("600x300")
        add_dialog.transient(parent_window)
        add_dialog.grab_set()

        # 主框架
        main_frame = tk.Frame(add_dialog, bg="#fff")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题
        title_label = tk.Label(main_frame, text="➕ 添加新映射", font=("微软雅黑", 14, "bold"), fg="#007bff", bg="#fff")
        title_label.pack(pady=(0, 20))

        # 原属性集输入
        tk.Label(main_frame, text="拣货单属性集:", font=("微软雅黑", 10), bg="#fff").pack(anchor="w", pady=(0, 5))
        original_var = tk.StringVar()
        original_entry = tk.Entry(main_frame, textvariable=original_var, font=("微软雅黑", 10), width=60)
        original_entry.pack(fill=tk.X, pady=(0, 15))

        # 目标属性集输入
        tk.Label(main_frame, text="目标属性集名称:", font=("微软雅黑", 10), bg="#fff").pack(anchor="w", pady=(0, 5))
        target_var = tk.StringVar()
        target_entry = tk.Entry(main_frame, textvariable=target_var, font=("微软雅黑", 10), width=60)
        target_entry.pack(fill=tk.X, pady=(0, 20))

        # 按钮框架
        button_frame = tk.Frame(main_frame, bg="#fff")
        button_frame.pack(fill=tk.X)

        def save_new_mapping():
            original = original_var.get().strip()
            target = target_var.get().strip()

            if not original or not target:
                messagebox.showwarning("警告", "请填写完整的映射信息")
                return

            if original in _template_mapping:
                messagebox.showwarning("警告", "该拣货单属性集已存在映射")
                return

            _template_mapping[original] = target
            save_template_mapping_to_local()
            messagebox.showinfo("成功", "新映射已添加")
            add_dialog.destroy()
            refresh_callback()

        # 保存按钮
        save_btn = tk.Button(button_frame, text="保存", command=save_new_mapping,
                           bg="#28a745", fg="white", font=("微软雅黑", 10), padx=20)
        save_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # 取消按钮
        cancel_btn = tk.Button(button_frame, text="取消", command=add_dialog.destroy,
                             bg="#6c757d", fg="white", font=("微软雅黑", 10), padx=20)
        cancel_btn.pack(side=tk.RIGHT)

        # 焦点设置
        original_entry.focus()

    def save_mapping_edit(original_key, new_original, new_target, refresh_callback):
        """保存映射编辑"""
        new_original = new_original.strip()
        new_target = new_target.strip()

        if not new_original or not new_target:
            messagebox.showwarning("警告", "映射信息不能为空")
            return

        # 如果原始键改变了，需要删除旧的并添加新的
        if original_key != new_original:
            if new_original in _template_mapping:
                messagebox.showwarning("警告", "新的拣货单属性集已存在")
                return
            del _template_mapping[original_key]

        _template_mapping[new_original] = new_target
        save_template_mapping_to_local()
        messagebox.showinfo("成功", "映射已更新")
        refresh_callback()

    def delete_mapping(original_key, refresh_callback):
        """删除映射"""
        if messagebox.askyesno("确认删除", f"确定要删除映射 '{original_key}' 吗？"):
            if original_key in _template_mapping:
                del _template_mapping[original_key]
                save_template_mapping_to_local()
                messagebox.showinfo("成功", "映射已删除")
                refresh_callback()

    # 显示映射表按钮
    def show_mapping_table():
        """显示当前加载的映射表 - 支持编辑、添加、删除"""
        # 确保映射表已加载
        try:
            load_template_mapping_from_local()
            logging.info(f"🔍 [映射表显示] 重新加载映射表，当前有 {len(_template_mapping)} 条映射")
        except Exception as e:
            logging.warning(f"🔍 [映射表显示] 加载映射表失败: {e}")

        dialog = tk.Toplevel(app)
        dialog.title("属性集映射表")
        dialog.geometry("1000x800")
        dialog.transient(app)
        dialog.grab_set()

        # 创建主框架 - 减少内边距
        main_frame = tk.Frame(dialog, bg="#fff")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题和添加按钮框架
        top_frame = tk.Frame(main_frame, bg="#fff")
        top_frame.pack(fill=tk.X, pady=(0, 10))

        # 标题
        title_label = tk.Label(top_frame, text="📋 当前属性集映射表", font=("微软雅黑", 14, "bold"), fg="#1976d2", bg="#fff")
        title_label.pack(side=tk.LEFT)

        # 右上角添加按钮已移除，保留操作面板中的添加按钮

        # 统计信息框架
        stats_frame = tk.Frame(main_frame, bg="#fff")
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        # 统计信息标签（将在refresh函数中更新）
        info_label = tk.Label(stats_frame, text="", font=("微软雅黑", 10), fg="#333", bg="#fff")
        info_label.pack(side=tk.LEFT)

        # 左侧映射表区域框架 - 按红框调整更宽
        left_panel = tk.Frame(main_frame, bg="#fff")
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 滚动框架
        canvas = tk.Canvas(left_panel, bg="#fff")
        scrollbar = tk.Scrollbar(left_panel, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#fff")

        # 绑定鼠标滚轮事件
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        canvas.bind("<MouseWheel>", on_mousewheel)
        dialog.bind("<MouseWheel>", on_mousewheel)

        scrollable_frame.bind(
            "<Configure>",
            lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        # 重要：创建canvas窗口，这是显示内容的关键
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

        # 添加鼠标滚轮支持
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

        def bind_mousewheel(_):
            canvas.bind_all("<MouseWheel>", on_mousewheel)

        def unbind_mousewheel(_):
            canvas.unbind_all("<MouseWheel>")

        canvas.bind('<Enter>', bind_mousewheel)
        canvas.bind('<Leave>', unbind_mousewheel)
        canvas.configure(yscrollcommand=scrollbar.set)

        # 刷新映射显示函数
        def refresh_mapping_display():
            """刷新映射显示"""
            # 清空现有内容
            for widget in scrollable_frame.winfo_children():
                widget.destroy()

            # 更新统计信息
            if _template_mapping:
                info_label.config(text=f"共 {len(_template_mapping)} 条映射规则", fg="#333")
            else:
                info_label.config(text="暂无映射规则，请先导入表格模版", fg="#e74c3c")

            # 表头
            header_frame = tk.Frame(scrollable_frame, bg="#f8f9fa", relief="solid", bd=1)
            header_frame.pack(fill=tk.X, padx=2, pady=(0, 1))

            tk.Label(header_frame, text="序号", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=3).pack(side=tk.LEFT, padx=1, pady=2)
            tk.Label(header_frame, text="拣货单属性集", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=15).pack(side=tk.LEFT, padx=1, pady=2)
            tk.Label(header_frame, text="目标属性集名称", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=60).pack(side=tk.LEFT, padx=1, pady=2)
            tk.Label(header_frame, text="操作", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=6).pack(side=tk.LEFT, padx=2, pady=2)

            # 映射内容
            if _template_mapping:
                for i, (original, target) in enumerate(_template_mapping.items(), 1):
                    # 创建行框架
                    row_frame = tk.Frame(scrollable_frame, bg="#fff" if i % 2 == 0 else "#f8f9fa", relief="solid", bd=1)
                    row_frame.pack(fill=tk.X, padx=2, pady=0)

                    # 序号
                    tk.Label(row_frame, text=str(i), font=("微软雅黑", 8), fg="#333", bg=row_frame["bg"], width=3).pack(side=tk.LEFT, padx=1, pady=1)

                    # 原属性集（可编辑）
                    original_var = tk.StringVar(value=original)
                    original_entry = tk.Entry(row_frame, textvariable=original_var, font=("微软雅黑", 8), width=30)
                    original_entry.pack(side=tk.LEFT, padx=1, pady=1)

                    # 目标属性集（可编辑）- 更宽
                    target_var = tk.StringVar(value=target)
                    target_entry = tk.Entry(row_frame, textvariable=target_var, font=("微软雅黑", 8), width=80)
                    target_entry.pack(side=tk.LEFT, padx=1, pady=1)

                    # 操作按钮框架 - 紧靠
                    action_frame = tk.Frame(row_frame, bg=row_frame["bg"])
                    action_frame.pack(side=tk.LEFT, padx=2, pady=1)

                    # 保存按钮
                    save_btn = tk.Button(action_frame, text="💾", command=lambda orig=original, orig_var=original_var, tgt_var=target_var: save_mapping_edit(orig, orig_var.get(), tgt_var.get(), refresh_mapping_display),
                                       bg="#28a745", fg="white", font=("微软雅黑", 8), width=3)
                    save_btn.pack(side=tk.LEFT, padx=1)

                    # 删除按钮
                    delete_btn = tk.Button(action_frame, text="🗑️", command=lambda orig=original: delete_mapping(orig, refresh_mapping_display),
                                         bg="#dc3545", fg="white", font=("微软雅黑", 8), width=3)
                    delete_btn.pack(side=tk.LEFT, padx=1)
            else:
                # 空状态
                empty_frame = tk.Frame(scrollable_frame, bg="#fff")
                empty_frame.pack(fill=tk.BOTH, expand=True, pady=50)
                tk.Label(empty_frame, text="🔍", font=("Segoe UI Emoji", 48), fg="#bdc3c7", bg="#fff").pack()
                tk.Label(empty_frame, text="暂无映射数据", font=("微软雅黑", 14), fg="#bdc3c7", bg="#fff").pack(pady=(10, 5))
                tk.Label(empty_frame, text="请点击\"导入表格模版\"按钮导入映射表格", font=("微软雅黑", 10), fg="#95a5a6", bg="#fff").pack()

        # 先布局滚动条在最右边
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 然后布局右侧操作面板，紧贴映射表
        right_panel = tk.Frame(main_frame, bg="#fff", width=180)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 0))
        right_panel.pack_propagate(False)

        # 最后布局映射表区域
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 初始显示
        refresh_mapping_display()

        # 按钮标题
        btn_title = tk.Label(right_panel, text="操作面板", font=("微软雅黑", 12, "bold"), fg="#333", bg="#fff")
        btn_title.pack(pady=(0, 10))

        # 移除添加新映射按钮（根据用户要求）

        # 导出按钮
        def export_mapping():
            if not _template_mapping:
                messagebox.showwarning("提示", "暂无映射数据可导出")
                return
            file_path = filedialog.asksaveasfilename(
                title="导出映射表",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
            )
            if file_path:
                try:
                    pd = lazy_import_pandas()
                    if pd:
                        df = pd.DataFrame(list(_template_mapping.items()), columns=["拣货单属性集", "目标属性集名称"])
                        if file_path.endswith('.xlsx'):
                            df.to_excel(file_path, index=False)
                        else:
                            df.to_csv(file_path, index=False, encoding='utf-8-sig')
                        messagebox.showinfo("成功", f"映射表已导出到：\n{file_path}")
                    else:
                        messagebox.showerror("错误", "缺少pandas库，无法导出")
                except Exception as e:
                    messagebox.showerror("错误", f"导出失败：{e}")

        export_btn = tk.Button(right_panel, text="📤 导出映射表", font=("微软雅黑", 10), bg="#28a745", fg="white", relief="flat", command=export_mapping)
        export_btn.pack(fill=tk.X, pady=(0, 10))

        # 清除映射按钮
        def clear_all_mapping():
            if not _template_mapping:
                messagebox.showwarning("提示", "暂无映射数据可清除")
                return
            result = messagebox.askyesno(
                "确认清除",
                f"确定要清除所有 {len(_template_mapping)} 条映射吗？\n\n此操作不可撤销！",
                icon="warning"
            )
            if result:
                count = clear_template_mapping()
                messagebox.showinfo("清除完成", f"已清除 {count} 条映射")
                refresh_mapping_display()
                # 刷新日志显示
                if 'log_callback' in globals():
                    log_callback("🗑️ 已清除所有映射表数据")

        clear_btn = tk.Button(right_panel, text="🗑️ 清除所有映射", font=("微软雅黑", 10), bg="#dc3545", fg="white", relief="flat", command=clear_all_mapping)
        clear_btn.pack(fill=tk.X, pady=(0, 10))

        # 添加新映射按钮 - 移到清除按钮下面
        def add_new_mapping():
            """添加新的映射"""
            add_dialog = tk.Toplevel(dialog)
            add_dialog.title("添加新映射")
            add_dialog.geometry("500x200")
            add_dialog.configure(bg="#fff")
            add_dialog.transient(dialog)
            add_dialog.grab_set()

            # 居中显示
            add_dialog.update_idletasks()
            x = (add_dialog.winfo_screenwidth() // 2) - (add_dialog.winfo_width() // 2)
            y = (add_dialog.winfo_screenheight() // 2) - (add_dialog.winfo_height() // 2)
            add_dialog.geometry(f"+{x}+{y}")

            # 主框架
            main_frame = tk.Frame(add_dialog, bg="#fff")
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)

            # 标题
            title_label = tk.Label(main_frame, text="添加新映射", font=("微软雅黑", 14, "bold"), fg="#333", bg="#fff")
            title_label.pack(pady=(0, 20))

            # 原属性集输入
            original_frame = tk.Frame(main_frame, bg="#fff")
            original_frame.pack(fill=tk.X, pady=(0, 10))
            tk.Label(original_frame, text="拣货单属性集:", font=("微软雅黑", 10), fg="#333", bg="#fff").pack(side=tk.LEFT)
            original_var = tk.StringVar()
            original_entry = tk.Entry(original_frame, textvariable=original_var, font=("微软雅黑", 10), width=40)
            original_entry.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

            # 目标属性集输入
            target_frame = tk.Frame(main_frame, bg="#fff")
            target_frame.pack(fill=tk.X, pady=(0, 20))
            tk.Label(target_frame, text="目标属性集名称:", font=("微软雅黑", 10), fg="#333", bg="#fff").pack(side=tk.LEFT)
            target_var = tk.StringVar()
            target_entry = tk.Entry(target_frame, textvariable=target_var, font=("微软雅黑", 10), width=40)
            target_entry.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

            # 按钮框架
            btn_frame = tk.Frame(main_frame, bg="#fff")
            btn_frame.pack(fill=tk.X)

            def save_new_mapping():
                original = original_var.get().strip()
                target = target_var.get().strip()

                if not original or not target:
                    messagebox.showwarning("输入错误", "请填写完整的映射信息")
                    return

                if original in _template_mapping:
                    result = messagebox.askyesno("映射已存在", f"映射 '{original}' 已存在，是否覆盖？")
                    if not result:
                        return

                _template_mapping[original] = target
                save_template_mapping_to_local()
                messagebox.showinfo("成功", "映射添加成功！")
                add_dialog.destroy()
                refresh_mapping_display()

            # 保存按钮
            save_btn = tk.Button(btn_frame, text="保存", font=("微软雅黑", 10), bg="#007bff", fg="white", relief="flat", command=save_new_mapping)
            save_btn.pack(side=tk.LEFT, padx=(0, 10))

            # 取消按钮
            cancel_btn = tk.Button(btn_frame, text="取消", font=("微软雅黑", 10), bg="#6c757d", fg="white", relief="flat", command=add_dialog.destroy)
            cancel_btn.pack(side=tk.LEFT)

            # 焦点设置
            original_entry.focus_set()

        add_btn = tk.Button(right_panel, text="➕ 添加新映射", font=("微软雅黑", 10), bg="#007bff", fg="white", relief="flat", command=add_new_mapping)
        add_btn.pack(fill=tk.X, pady=(0, 10))

        # 🔍 新增：搜索属性集按钮
        def search_attribute_set():
            """搜索属性集功能"""
            search_dialog = tk.Toplevel(dialog)
            search_dialog.title("搜索属性集")
            search_dialog.geometry("1000x600")
            search_dialog.configure(bg="#fff")
            search_dialog.transient(dialog)
            search_dialog.grab_set()

            # 居中显示
            search_dialog.update_idletasks()
            x = (search_dialog.winfo_screenwidth() // 2) - (search_dialog.winfo_width() // 2)
            y = (search_dialog.winfo_screenheight() // 2) - (search_dialog.winfo_height() // 2)
            search_dialog.geometry(f"+{x}+{y}")

            # 标题
            title_label = tk.Label(search_dialog, text="搜索属性集", font=("微软雅黑", 14, "bold"), fg="#333", bg="#fff")
            title_label.pack(pady=(20, 10))

            # 搜索框
            search_frame = tk.Frame(search_dialog, bg="#fff")
            search_frame.pack(fill=tk.X, padx=20, pady=(0, 10))

            tk.Label(search_frame, text="搜索关键词:", font=("微软雅黑", 10), fg="#333", bg="#fff").pack(side=tk.LEFT)
            search_var = tk.StringVar()
            search_entry = tk.Entry(search_frame, textvariable=search_var, font=("微软雅黑", 10), width=30)
            search_entry.pack(side=tk.LEFT, padx=(10, 0))

            # 结果显示区域
            result_frame = tk.Frame(search_dialog, bg="#fff")
            result_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 10))

            # 创建表头
            header_frame = tk.Frame(result_frame, bg="#f8f9fa", relief="solid", bd=1)
            header_frame.pack(fill=tk.X, padx=2, pady=(0, 1))
            tk.Label(header_frame, text="序号", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=3).pack(side=tk.LEFT, padx=1, pady=2)
            tk.Label(header_frame, text="拣货单属性集", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=15).pack(side=tk.LEFT, padx=1, pady=2)
            tk.Label(header_frame, text="目标属性集名称", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=60).pack(side=tk.LEFT, padx=1, pady=2)
            tk.Label(header_frame, text="操作", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=6).pack(side=tk.LEFT, padx=2, pady=2)

            # 滚动区域
            canvas = tk.Canvas(result_frame, bg="#fff")
            scrollbar = tk.Scrollbar(result_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg="#fff")
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)
            canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            def on_mousewheel(event):
                canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
            canvas.bind_all("<MouseWheel>", on_mousewheel)
            scrollable_frame.bind(
                "<Configure>",
                lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            # 刷新表格内容
            def refresh_table(found_mappings):
                for widget in scrollable_frame.winfo_children():
                    widget.destroy()
                if found_mappings:
                    for i, (original, target) in enumerate(found_mappings, 1):
                        row_frame = tk.Frame(scrollable_frame, bg="#fff" if i % 2 == 0 else "#f8f9fa", relief="solid", bd=1)
                        row_frame.pack(fill=tk.X, padx=2, pady=0)
                        tk.Label(row_frame, text=str(i), font=("微软雅黑", 8), fg="#333", bg=row_frame["bg"], width=3).pack(side=tk.LEFT, padx=1, pady=1)
                        original_var = tk.StringVar(value=original)
                        original_entry = tk.Entry(row_frame, textvariable=original_var, font=("微软雅黑", 8), width=30)
                        original_entry.pack(side=tk.LEFT, padx=1, pady=1)
                        target_var = tk.StringVar(value=target)
                        target_entry = tk.Entry(row_frame, textvariable=target_var, font=("微软雅黑", 8), width=80)
                        target_entry.pack(side=tk.LEFT, padx=1, pady=1)
                        action_frame = tk.Frame(row_frame, bg=row_frame["bg"])
                        action_frame.pack(side=tk.LEFT, padx=2, pady=1)
                        def save_row(orig=original, orig_var=original_var, tgt_var=target_var):
                            new_orig = orig_var.get().strip()
                            new_tgt = tgt_var.get().strip()
                            if not new_orig or not new_tgt:
                                messagebox.showwarning("警告", "映射信息不能为空")
                                return
                            # 如果原始键改变了，需要删除旧的并添加新的
                            if orig != new_orig:
                                if new_orig in _template_mapping:
                                    messagebox.showwarning("警告", "新的拣货单属性集已存在")
                                    return
                                del _template_mapping[orig]
                            _template_mapping[new_orig] = new_tgt
                            save_template_mapping_to_local()
                            messagebox.showinfo("成功", "映射已更新")
                            # 刷新本表格
                            perform_search()
                        save_btn = tk.Button(action_frame, text="💾", command=save_row, bg="#28a745", fg="white", font=("微软雅黑", 8), width=3)
                        save_btn.pack(side=tk.LEFT, padx=1)
                else:
                    empty_frame = tk.Frame(scrollable_frame, bg="#fff")
                    empty_frame.pack(fill=tk.BOTH, expand=True, pady=50)
                    tk.Label(empty_frame, text="🔍", font=("Segoe UI Emoji", 48), fg="#bdc3c7", bg="#fff").pack()
                    tk.Label(empty_frame, text="未找到匹配的属性集", font=("微软雅黑", 14), fg="#bdc3c7", bg="#fff").pack(pady=(10, 5))
                    tk.Label(empty_frame, text="请检查关键词或添加新映射", font=("微软雅黑", 10), fg="#95a5a6", bg="#fff").pack()

            def perform_search():
                """执行搜索"""
                keyword = search_var.get().strip().lower()
                found_mappings = []
                if keyword:
                    for original, target in _template_mapping.items():
                        if keyword in original.lower() or keyword in target.lower():
                            found_mappings.append((original, target))
                refresh_table(found_mappings)

            # 搜索按钮
            search_btn = tk.Button(search_frame, text="🔍 搜索", font=("微软雅黑", 10), bg="#28a745", fg="white", relief="flat", command=perform_search)
            search_btn.pack(side=tk.LEFT, padx=(10, 0))
            search_entry.bind('<Return>', lambda e: perform_search())
            # 初始为空
            refresh_table([])

            # 关闭按钮
            close_search_btn = tk.Button(search_dialog, text="关闭", font=("微软雅黑", 10), bg="#6c757d", fg="white", relief="flat", command=search_dialog.destroy)
            close_search_btn.pack(side=tk.BOTTOM, pady=(0, 20))

            # 焦点设置
            search_entry.focus_set()

        search_btn = tk.Button(right_panel, text="🔍 搜索属性集", font=("微软雅黑", 10), bg="#17a2b8", fg="white", relief="flat", command=search_attribute_set)
        search_btn.pack(fill=tk.X, pady=(0, 10))

        # 在操作面板添加"更新属性集"按钮
        def update_attribute_set():
            """更新属性集功能"""
            update_dialog = tk.Toplevel(dialog)
            update_dialog.title("更新属性集")
            update_dialog.geometry("1000x600")
            update_dialog.configure(bg="#fff")
            update_dialog.transient(dialog)
            update_dialog.grab_set()

            # 居中显示
            update_dialog.update_idletasks()
            x = (update_dialog.winfo_screenwidth() // 2) - (update_dialog.winfo_width() // 2)
            y = (update_dialog.winfo_screenheight() // 2) - (update_dialog.winfo_height() // 2)
            update_dialog.geometry(f"+{x}+{y}")

            # 标题
            title_label = tk.Label(update_dialog, text="更新属性集", font=("微软雅黑", 14, "bold"), fg="#333", bg="#fff")
            title_label.pack(pady=(20, 10))

            # 结果显示区域
            result_frame = tk.Frame(update_dialog, bg="#fff")
            result_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 10))

            # 创建表头
            header_frame = tk.Frame(result_frame, bg="#f8f9fa", relief="solid", bd=1)
            header_frame.pack(fill=tk.X, padx=2, pady=(0, 1))
            tk.Label(header_frame, text="序号", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=3).pack(side=tk.LEFT, padx=1, pady=2)
            tk.Label(header_frame, text="拣货单属性集", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=15).pack(side=tk.LEFT, padx=1, pady=2)
            tk.Label(header_frame, text="目标属性集名称", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=60).pack(side=tk.LEFT, padx=1, pady=2)
            tk.Label(header_frame, text="操作", font=("微软雅黑", 9, "bold"), fg="#333", bg="#f8f9fa", width=6).pack(side=tk.LEFT, padx=2, pady=2)

            # 滚动区域
            canvas = tk.Canvas(result_frame, bg="#fff")
            scrollbar = tk.Scrollbar(result_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg="#fff")
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)
            canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            def on_mousewheel(event):
                canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
            canvas.bind_all("<MouseWheel>", on_mousewheel)
            scrollable_frame.bind(
                "<Configure>",
                lambda _: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            # 刷新表格内容
            def refresh_table(found_mappings):
                for widget in scrollable_frame.winfo_children():
                    widget.destroy()
                if found_mappings:
                    for i, (original, target) in enumerate(found_mappings, 1):
                        row_frame = tk.Frame(scrollable_frame, bg="#fff" if i % 2 == 0 else "#f8f9fa", relief="solid", bd=1)
                        row_frame.pack(fill=tk.X, padx=2, pady=0)
                        tk.Label(row_frame, text=str(i), font=("微软雅黑", 8), fg="#333", bg=row_frame["bg"], width=3).pack(side=tk.LEFT, padx=1, pady=1)
                        original_var = tk.StringVar(value=original)
                        original_entry = tk.Entry(row_frame, textvariable=original_var, font=("微软雅黑", 8), width=30, state="readonly")
                        original_entry.pack(side=tk.LEFT, padx=1, pady=1)
                        target_var = tk.StringVar(value=target)
                        target_entry = tk.Entry(row_frame, textvariable=target_var, font=("微软雅黑", 8), width=80)
                        target_entry.pack(side=tk.LEFT, padx=1, pady=1)
                        action_frame = tk.Frame(row_frame, bg=row_frame["bg"])
                        action_frame.pack(side=tk.LEFT, padx=2, pady=1)
                        def save_row(orig=original, orig_var=original_var, tgt_var=target_var):
                            new_orig = orig_var.get().strip()
                            new_tgt = tgt_var.get().strip()
                            if not new_orig or not new_tgt:
                                messagebox.showwarning("警告", "映射信息不能为空")
                                return
                            # 如果原始键改变了，需要删除旧的并添加新的
                            if orig != new_orig:
                                if new_orig in _template_mapping:
                                    messagebox.showwarning("警告", "新的拣货单属性集已存在")
                                    return
                                del _template_mapping[orig]
                            _template_mapping[new_orig] = new_tgt
                            save_template_mapping_to_local()
                            messagebox.showinfo("成功", "映射已更新")
                            # 刷新本表格
                            perform_search()
                        save_btn = tk.Button(action_frame, text="💾", command=save_row, bg="#28a745", fg="white", font=("微软雅黑", 8), width=3)
                        save_btn.pack(side=tk.LEFT, padx=1)
                else:
                    empty_frame = tk.Frame(scrollable_frame, bg="#fff")
                    empty_frame.pack(fill=tk.BOTH, expand=True, pady=50)
                    tk.Label(empty_frame, text="🔍", font=("Segoe UI Emoji", 48), fg="#bdc3c7", bg="#fff").pack()
                    tk.Label(empty_frame, text="没有未映射的属性集啦！", font=("微软雅黑", 14), fg="#bdc3c7", bg="#fff").pack(pady=(10, 5))
                    tk.Label(empty_frame, text="所有拣货单属性集都已映射完毕", font=("微软雅黑", 10), fg="#95a5a6", bg="#fff").pack()

            def perform_search():
                """执行搜索"""
                found_mappings = []
                if _last_processed_df is not None:
                    unique_attributes = _last_processed_df['属性集'].dropna().unique()
                    mapped_attributes = set(_template_mapping.keys())
                    unmapped_attributes = set(unique_attributes) - mapped_attributes
                    found_mappings = [(attr, "") for attr in unmapped_attributes]
                else:
                    messagebox.showwarning("提示", "请先导入拣货单")
                refresh_table(found_mappings)

            # 初始为空
            refresh_table([])

            # 关闭按钮
            close_update_btn = tk.Button(update_dialog, text="关闭", font=("微软雅黑", 10), bg="#6c757d", fg="white", relief="flat", command=update_dialog.destroy)
            close_update_btn.pack(side=tk.BOTTOM, pady=(0, 20))

            # 执行搜索
            perform_search()

        update_btn = tk.Button(
            right_panel,
            text="📝 更新属性集",
            font=("微软雅黑", 10),
            bg="#ffc107",
            fg="#333",
            relief="flat",
            command=update_attribute_set
        )
        update_btn.pack(fill=tk.X, pady=(0, 10))

        # 关闭按钮
        close_btn = tk.Button(right_panel, text="关闭", font=("微软雅黑", 10), bg="#6c757d", fg="white", relief="flat", command=dialog.destroy)
        close_btn.pack(side=tk.BOTTOM, fill=tk.X, pady=(20, 0))

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

    tb.Button(step_frame, text="导入表格模版", bootstyle=INFO, width=20, command=on_import_template).pack(fill=tk.X, pady=(0, 6))
    tb.Button(step_frame, text="查看映射表", bootstyle=WARNING, width=20, command=show_mapping_table).pack(fill=tk.X, pady=(0, 6))
    tb.Button(step_frame, text="起飞", bootstyle=SECONDARY, width=22, command=on_start).pack(fill=tk.X, pady=(0, 8))
    # 右侧区域 - 日志和说明
    right_frame = tk.Frame(main_frame, bg="#fff", highlightthickness=0)
    right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 0), pady=0)
    right_frame.pack_propagate(False)

    # 日志区域
    log_frame = tk.LabelFrame(right_frame, text="验证/日志", bg="#fff", fg="#1976d2", font=("微软雅黑", 10, "bold"), labelanchor="n")
    log_frame.pack(fill=tk.BOTH, expand=True, padx=18, pady=(18, 8))
    log_text = tk.Text(log_frame, height=12, font=("微软雅黑", 10), bg="#f5f6fa", fg="#222", relief="flat")
    log_text.pack(fill=tk.BOTH, expand=True, padx=4, pady=4)
    log_text.insert(tk.END, "鲸鱼提示：这里会显示处理日志和验证信息~\n")

    # 说明区域
    desc_frame = tk.LabelFrame(right_frame, text="说明", bg="#fff", fg="#1976d2", font=("微软雅黑", 10, "bold"), labelanchor="n")
    desc_frame.pack(fill=tk.X, padx=18, pady=(8, 18))
    desc = (
        '1. 先导入表格模版（A列拣货单属性集，B列目标文件夹名）\n'
        '2. 选择主文件夹和原图文件夹，主文件夹放拣货单大标小标\n'
        '3. 填写命名配置（可选），不填则使用简单命名\n'
        '4. 点击"起飞"按钮，自动完成所有处理（包含拆大标功能）\n'
        '5. 自动查找大标/拣货单/小标，全自动处理\n'
        '6. 根据表格模版自动匹配属性集名称\n'
        '\n鲸鱼祝你爆单！'
    )
    tk.Label(desc_frame, text=desc, font=("微软雅黑", 10), fg="#222", bg="#fff", justify="left", anchor="nw").pack(fill=tk.BOTH, expand=True, padx=8, pady=8)
    # 关闭窗口时自动保存路径和配置
    def on_closing():
        # 保存路径
        save_paths(path_var.get(), img_var.get())

        # 保存操作员和店铺号配置（自动获取日期时间）
        auto_date, auto_time = get_auto_date_and_time()
        current_operator = operator_var.get().strip()
        current_shop = shop_var.get().strip()

        # 更新历史记录
        updated_operator_history = add_to_history(operator_history.copy(), current_operator)
        updated_shop_history = add_to_history(shop_history.copy(), current_shop)

        # 保存文件夹命名配置
        current_config = {
            "date": auto_date,
            "time_period": auto_time,
            "operator": current_operator,
            "shop_number": current_shop,
            "operator_history": updated_operator_history,
            "shop_history": updated_shop_history
        }
        save_folder_config(current_config)

        app.destroy()
    app.protocol("WM_DELETE_WINDOW", on_closing)
    app.mainloop()



def calculate_folder_quantities_from_picking_list(target_base_folder, df, source_folder, newly_created_folders=None):
    """
    简化统计逻辑：
    1. 直接从拣货单统计每个属性集的数量
    2. 检查文件夹是否存在对应的图片
    3. 只统计新创建的文件夹（如果提供了newly_created_folders参数）
    4. 按属性集分组统计总数量
    """
    if newly_created_folders is not None:
        logging.info(f"开始统计新创建的 {len(newly_created_folders)} 个文件夹...")
    else:
        logging.info("开始简化的数量统计逻辑...")

    # 第一步：直接从拣货单统计每个属性集的数量
    attribute_quantities = {}  # 属性集 -> 数量

    logging.info("=== 从拣货单直接统计属性集数量 ===")
    for index, row in df.iterrows():
        product_info = row.get('商品信息')
        attribute_set = row.get('属性集')
        quantity = row.get('数量', 1)  # 获取数量，默认为1

        if not isinstance(product_info, str) or not product_info.strip():
            continue

        # 提取SKC编号
        skc_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if not skc_match:
            continue

        skc_base = skc_match.group(1)

        # 统计属性集数量
        if attribute_set:
            if attribute_set not in attribute_quantities:
                attribute_quantities[attribute_set] = 0

            # 确保数量是数字
            try:
                qty = int(quantity) if quantity else 1
            except (ValueError, TypeError):
                qty = 1

            attribute_quantities[attribute_set] += qty
            logging.info(f"拣货单第{index+2}行: SKC {skc_base}, 属性集: {attribute_set}, 数量: +{qty} = {attribute_quantities[attribute_set]}")

    logging.info("=== 拣货单统计完成 ===")

    # 第二步：检查文件夹是否存在并转换为文件夹统计格式
    folder_stats = {}

    if newly_created_folders is not None:
        logging.info("=== 检查新创建的文件夹 ===")
        folders_to_check = newly_created_folders
    else:
        logging.info("=== 检查已创建的文件夹 ===")
        if not os.path.isdir(target_base_folder):
            logging.error(f"目标文件夹不存在: {target_base_folder}")
            return folder_stats
        folders_to_check = [f for f in os.listdir(target_base_folder) if os.path.isdir(os.path.join(target_base_folder, f))]

    # 扫描要检查的文件夹
    for folder_name in folders_to_check:
        folder_path = os.path.join(target_base_folder, folder_name)
        if os.path.isdir(folder_path):
            # 跳过系统文件夹
            if folder_name.startswith('.') or folder_name.lower() in ['temp', 'tmp', 'cache', '小标pdf', '大标']:
                continue

            # 检查文件夹中的数量子文件夹，收集信息
            has_images = False
            quantity_folders = []  # 存储数量文件夹信息

            try:
                # 先检查主文件夹
                for file_name in os.listdir(folder_path):
                    file_full_path = os.path.join(folder_path, file_name)
                    if os.path.isfile(file_full_path) and file_name.lower().endswith(('.jpg', '.jpeg', '.png')):
                        has_images = True
                    elif os.path.isdir(file_full_path):
                        # 检查子文件夹（如"1套"、"2张"、"11套"等）
                        subfolder_image_count = 0
                        for sub_file in os.listdir(file_full_path):
                            if sub_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                                has_images = True
                                subfolder_image_count += 1

                        # 如果子文件夹有图片，记录信息
                        if subfolder_image_count > 0:
                            quantity_match = re.match(r'(\d+)([张套])', file_name)
                            if quantity_match:
                                folder_quantity = int(quantity_match.group(1))
                                folder_unit = quantity_match.group(2)
                                quantity_folders.append({
                                    'name': file_name,
                                    'quantity': folder_quantity,
                                    'unit': folder_unit,
                                    'jpg_count': subfolder_image_count
                                })
                                logging.info(f"📁 发现数量文件夹: {file_name} -> {folder_quantity}{folder_unit}, {subfolder_image_count}张jpg")
            except Exception as e:
                logging.warning(f"检查文件夹 {folder_path} 时出错: {e}")
                continue

            if has_images:
                # 尝试从文件夹名匹配属性集
                matched_attr_set = None
                for attr_set in attribute_quantities.keys():
                    # 获取目标属性集映射
                    target_attribute = get_template_mapping(attr_set)
                    if target_attribute and target_attribute.lower() in folder_name.lower():
                        matched_attr_set = attr_set
                        break

                # 计算总数量
                total_calculated_quantity = 0
                detected_unit = "张"  # 默认单位

                if quantity_folders:
                    # 从文件夹名称提取pc数
                    pc_count = 1  # 默认1pc
                    pc_match = re.search(r'(\d+)pc', folder_name, re.IGNORECASE)
                    if pc_match:
                        pc_count = int(pc_match.group(1))
                        logging.info(f"🔍 从文件夹名称提取pc数: {folder_name} -> {pc_count}pc")

                    # 计算每个数量文件夹的贡献并累加
                    for qf in quantity_folders:
                        detected_unit = qf['unit']  # 使用检测到的单位

                        if pc_count > 1:
                            # 多pc：(jpg数量 ÷ pc数) × 文件夹数量
                            base_sets = qf['jpg_count'] // pc_count
                            contribution = base_sets * qf['quantity']
                            logging.info(f"🔢 计算: {qf['name']} -> ({qf['jpg_count']}张jpg ÷ {pc_count}pc) × {qf['quantity']} = {base_sets} × {qf['quantity']} = {contribution}{qf['unit']}")
                        else:
                            # 单pc：jpg数量 × 文件夹数量
                            contribution = qf['jpg_count'] * qf['quantity']
                            logging.info(f"🔢 计算: {qf['name']} -> {qf['jpg_count']}张jpg × {qf['quantity']} = {contribution}{qf['unit']}")

                        total_calculated_quantity += contribution

                if matched_attr_set:
                    actual_quantity = max(1, total_calculated_quantity)
                    actual_unit = detected_unit

                    folder_stats[folder_name] = {
                        'total_quantity': actual_quantity,
                        'default_unit': actual_unit,
                        'attribute_set': matched_attr_set
                    }
                    logging.info(f"✅ 文件夹匹配: {folder_name} -> 属性集: {matched_attr_set}, 总数量: {actual_quantity}{actual_unit}")
                else:
                    actual_quantity = max(1, total_calculated_quantity)
                    actual_unit = detected_unit

                    folder_stats[folder_name] = {
                        'total_quantity': actual_quantity,
                        'default_unit': actual_unit,
                        'attribute_set': folder_name
                    }
                    logging.info(f"⚠️ 文件夹未匹配: {folder_name} -> 总数量: {actual_quantity}{actual_unit}")

    logging.info(f"=== 文件夹检查完成，共找到 {len(folder_stats)} 个有效文件夹 ===")
    return folder_stats



def calculate_final_stats_from_folders_and_pdfs(target_base_folder, df, source_folder, small_pdf_folder):
    """
    检查刚创建的文件夹对应的SKC，去匹配小标PDF数量
    相同SKC叠加：如果SKC匹配到两个小标就是1+1=2
    """
    logging.info("开始统计刚创建文件夹的SKC对应小标PDF数量...")

    # 第一步：统计小标PDF中各SKC的数量（相同SKC叠加）
    skc_pdf_counts = {}
    if small_pdf_folder and os.path.isdir(small_pdf_folder):
        logging.info("扫描小标PDF文件夹...")
        for filename in os.listdir(small_pdf_folder):
            if filename.lower().endswith('.pdf'):
                # 提取SKC编号
                skc_match = re.match(r'(\d{9,11})', filename)
                if skc_match:
                    skc = skc_match.group(1)
                    if skc not in skc_pdf_counts:
                        skc_pdf_counts[skc] = 0
                    skc_pdf_counts[skc] += 1  # 相同SKC叠加
                    logging.info(f"小标PDF: {filename} -> SKC {skc} (累计: {skc_pdf_counts[skc]})")

        logging.info(f"小标PDF统计完成，共找到 {len(skc_pdf_counts)} 个不同的SKC")
        for skc, count in skc_pdf_counts.items():
            logging.info(f"  SKC {skc}: {count} 个小标PDF")
    else:
        logging.warning("没有找到小标PDF文件夹")

    # 第二步：检查刚创建的文件夹，提取对应的SKC
    folder_stats = {}

    if not os.path.isdir(target_base_folder):
        logging.error(f"目标文件夹不存在: {target_base_folder}")
        return folder_stats

    logging.info(f"扫描已创建的文件夹: {target_base_folder}")
    for folder_name in os.listdir(target_base_folder):
        folder_path = os.path.join(target_base_folder, folder_name)
        if os.path.isdir(folder_path):
            logging.info(f"分析文件夹: {folder_name}")

            # 从文件夹内的图片文件名提取SKC（最准确的方法）
            found_skc = None
            for file_name in os.listdir(folder_path):
                if file_name.lower().endswith(('.jpg', '.jpeg', '.png')):
                    skc_match = re.search(r'(\d{9,11})', file_name)
                    if skc_match:
                        found_skc = skc_match.group(1)
                        logging.info(f"  从图片文件 '{file_name}' 提取SKC: {found_skc}")
                        break

            # 如果没找到，尝试从文件夹名直接提取SKC
            if not found_skc:
                skc_match = re.search(r'(\d{9,11})', folder_name)
                if skc_match:
                    found_skc = skc_match.group(1)
                    logging.info(f"  从文件夹名提取SKC: {found_skc}")

            if found_skc:
                # 第三步：用SKC匹配小标PDF数量
                pdf_quantity = skc_pdf_counts.get(found_skc, 0)

                if pdf_quantity > 0:
                    logging.info(f"  SKC {found_skc} 匹配到 {pdf_quantity} 个小标PDF")
                else:
                    logging.warning(f"  SKC {found_skc} 没有匹配到小标PDF，设置默认数量为1")
                    pdf_quantity = 1  # 没有小标PDF时默认为1

                # 检测pc数判断单位
                try:
                    jpg_count, pc_count = count_images_and_pc(source_folder, found_skc)
                    is_multi_pc = pc_count > 1
                    logging.info(f"  SKC {found_skc} 检测到 {pc_count}pc，{'多pc' if is_multi_pc else '单pc'}")
                except Exception as e:
                    logging.warning(f"  检测pc数失败: {e}，默认为单pc")
                    pc_count = 1
                    is_multi_pc = False

                folder_stats[folder_name] = {
                    'total_quantity': pdf_quantity,
                    'is_multi_pc': is_multi_pc,
                    'skc_count': 1,
                    'skc_base': found_skc
                }
                logging.info(f"  文件夹统计完成: {folder_name} -> SKC {found_skc}, 数量: {pdf_quantity}, 单位: {'套' if is_multi_pc else '张'}")
            else:
                logging.warning(f"  无法为文件夹 '{folder_name}' 找到对应的SKC")

    logging.info(f"文件夹统计完成，共统计 {len(folder_stats)} 个文件夹")
    return folder_stats

def show_quantity_confirmation_dialog(folder_stats):
    """显示数量确认对话框，基于拣货单数量自动填入"""
    dialog = tk.Toplevel()
    dialog.title("数量统计确认")
    dialog.geometry("900x600")
    dialog.transient()
    dialog.grab_set()

    # 创建主框架
    main_frame = tk.Frame(dialog, bg="#fff")
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

    # 标题
    title_label = tk.Label(main_frame, text="📊 数量统计确认", font=("微软雅黑", 14, "bold"), fg="#1976d2", bg="#fff")
    title_label.pack(pady=(0, 10))

    # 说明文字
    desc_label = tk.Label(main_frame, text="程序已根据拣货单数量统计各文件夹，请确认或修改总X张/套：", font=("微软雅黑", 10), fg="#333", bg="#fff")
    desc_label.pack(pady=(0, 10))

    # 滚动框架
    canvas = tk.Canvas(main_frame, bg="#fff")
    scrollbar = tk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg="#fff")

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    # 存储输入框的字典
    quantity_vars = {}
    unit_vars = {}

    # 为每个文件夹创建输入行
    for i, (folder_name, stats) in enumerate(folder_stats.items()):
        # 创建行框架
        row_frame = tk.Frame(scrollable_frame, bg="#f8f9fa", relief="solid", bd=1)
        row_frame.pack(fill=tk.X, padx=5, pady=2)

        # 文件夹名称（截断显示）
        folder_display = folder_name[:60] + "..." if len(folder_name) > 60 else folder_name
        folder_label = tk.Label(row_frame, text=f"{i+1}. {folder_display}", font=("微软雅黑", 9), fg="#333", bg="#f8f9fa", anchor="w")
        folder_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10, pady=5)

        # 数量和单位输入框
        input_frame = tk.Frame(row_frame, bg="#f8f9fa")
        input_frame.pack(side=tk.RIGHT, padx=10, pady=5)

        tk.Label(input_frame, text="总", font=("微软雅黑", 9), fg="#333", bg="#f8f9fa").pack(side=tk.LEFT)

        quantity_var = tk.StringVar(value=str(stats['total_quantity']))
        quantity_entry = tk.Entry(input_frame, textvariable=quantity_var, font=("微软雅黑", 9), width=6, justify="center")
        quantity_entry.pack(side=tk.LEFT, padx=(2, 2))

        # 单位选择
        unit_var = tk.StringVar(value=stats['default_unit'])
        unit_combo = ttk.Combobox(input_frame, textvariable=unit_var, values=["张", "套"], width=3, state="readonly")
        unit_combo.pack(side=tk.LEFT, padx=(2, 0))

        quantity_vars[folder_name] = quantity_var
        unit_vars[folder_name] = unit_var

    # 布局滚动区域
    canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # 按钮框架
    button_frame = tk.Frame(main_frame, bg="#fff")
    button_frame.pack(fill=tk.X, pady=(20, 0))

    result = [None]  # 存储结果

    def on_confirm():
        # 收集修改后的数量和单位
        updated_stats = {}
        for folder_name in folder_stats.keys():
            try:
                new_quantity = int(quantity_vars[folder_name].get())
                new_unit = unit_vars[folder_name].get()
                updated_stats[folder_name] = {
                    'total_quantity': new_quantity,
                    'unit': new_unit,
                    'is_multi_pc': new_unit == "套"
                }
            except ValueError:
                messagebox.showerror("错误", f"文件夹 '{folder_name[:30]}...' 的数量必须是整数！")
                return

        result[0] = updated_stats
        dialog.destroy()

    def on_cancel():
        result[0] = None
        dialog.destroy()

    # 按钮
    cancel_btn = tk.Button(button_frame, text="取消", font=("微软雅黑", 10), bg="#6c757d", fg="white", relief="flat", padx=20, command=on_cancel)
    cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))

    confirm_btn = tk.Button(button_frame, text="确认", font=("微软雅黑", 10), bg="#28a745", fg="white", relief="flat", padx=20, command=on_confirm)
    confirm_btn.pack(side=tk.RIGHT)

    # 居中显示
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
    y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
    dialog.geometry(f"+{x}+{y}")

    dialog.wait_window()
    return result[0]

def update_folder_names_with_confirmed_quantities(target_base_folder, confirmed_stats):
    """根据确认的数量更新文件夹名称"""
    for attr_set, stats in confirmed_stats.items():
        # 查找包含该属性集的文件夹
        for folder_name in os.listdir(target_base_folder):
            folder_path = os.path.join(target_base_folder, folder_name)
            if os.path.isdir(folder_path) and attr_set in folder_name:
                # 更新文件夹名称中的总数部分
                new_quantity = stats['total_quantity']
                unit = stats['unit']

                if "总X" in folder_name:
                    # 替换 "总X" 为具体数量
                    new_folder_name = folder_name.replace("总X", f"总{new_quantity}{unit}")
                elif "总" in folder_name and ("张" in folder_name or "套" in folder_name):
                    # 更新现有的总数
                    new_folder_name = re.sub(r'总\d+[张套]', f'总{new_quantity}{unit}', folder_name)
                else:
                    # 添加总数信息
                    new_folder_name = f"{folder_name}(总{new_quantity}{unit})"

                if new_folder_name != folder_name:
                    new_folder_path = os.path.join(target_base_folder, new_folder_name)
                    try:
                        os.rename(folder_path, new_folder_path)
                        logging.info(f"更新文件夹名称: {folder_name} -> {new_folder_name}")
                    except Exception as e:
                        logging.error(f"重命名文件夹失败: {folder_name} -> {new_folder_name}，原因: {e}")

if __name__ == '__main__':
    main()

