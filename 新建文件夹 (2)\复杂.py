# 基础导入 - 快速启动
import re
import json
import os
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import logging
import shutil
import uuid
import hashlib
import datetime
import threading

# 延迟导入的重型库 - 只在需要时导入
# import pandas as pd  # 延迟导入
# import pdfplumber  # 延迟导入
# import PyPDF2  # 延迟导入
# import pyperclip  # 延迟导入
# from io import StringIO  # 延迟导入
# from pdfminer.* # 延迟导入
# import requests  # 延迟导入
# from collections import Counter  # 延迟导入

def lazy_import_pandas():
    """延迟导入pandas"""
    global pd
    try:
        import pandas as pd
        return pd
    except ImportError:
        messagebox.showerror("错误", "缺少pandas库，请安装：pip install pandas")
        return None

def lazy_import_pdfplumber():
    """延迟导入pdfplumber"""
    try:
        import pdfplumber
        return pdfplumber
    except ImportError:
        messagebox.showerror("错误", "缺少pdfplumber库，请安装：pip install pdfplumber")
        return None

def lazy_import_pypdf2():
    """延迟导入PyPDF2"""
    try:
        import PyPDF2
        return PyPDF2
    except ImportError:
        messagebox.showerror("错误", "缺少PyPDF2库，请安装：pip install PyPDF2")
        return None

def lazy_import_pyperclip():
    """延迟导入pyperclip"""
    try:
        import pyperclip
        return pyperclip
    except ImportError:
        messagebox.showerror("错误", "缺少pyperclip库，请安装：pip install pyperclip")
        return None

def lazy_import_requests():
    """延迟导入requests"""
    try:
        import requests
        return requests
    except ImportError:
        messagebox.showerror("错误", "缺少requests库，请安装：pip install requests")
        return None

def lazy_import_pdfminer():
    """延迟导入pdfminer"""
    try:
        from io import StringIO
        from pdfminer.converter import TextConverter
        from pdfminer.layout import LAParams
        from pdfminer.pdfdocument import PDFDocument
        from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
        from pdfminer.pdfpage import PDFPage
        from pdfminer.pdfparser import PDFParser
        return {
            'StringIO': StringIO,
            'TextConverter': TextConverter,
            'LAParams': LAParams,
            'PDFDocument': PDFDocument,
            'PDFResourceManager': PDFResourceManager,
            'PDFPageInterpreter': PDFPageInterpreter,
            'PDFPage': PDFPage,
            'PDFParser': PDFParser
        }
    except ImportError:
        messagebox.showerror("错误", "缺少pdfminer库，请安装：pip install pdfminer.six")
        return None

def lazy_import_collections():
    """延迟导入collections"""
    try:
        from collections import Counter
        return Counter
    except ImportError:
        return None

# 设置常量
LICENSE_FILE = "activationg.license"
CONFIG_FILE = "confige.json"

# 新增：文件夹命名配置
FOLDER_NAME_CONFIG = {
    "date": "4-15",
    "time_period": "上午",  # 上午/下午
    "operator": "吴进宇",
    "shop_number": "店铺号"
}

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')

# 随机生成SALT值
def generate_salt():
    return os.urandom(16).hex()  # 生成一个16字节的随机字符串

def get_machine_code():
    mac = uuid.getnode()
    return hashlib.md5(str(mac).encode()).hexdigest()

def generate_activation_code(machine_code):
    expiry_date = "99991231"
    salt = generate_salt()  # 随机生成SALT值
    activation_code = hashlib.sha256((machine_code + expiry_date + salt).encode()).hexdigest().upper()
    return f"{activation_code}:{expiry_date}:{salt}"  # 将SALT值也存储在激活码中

def verify_activation_code(code):
    try:
        code_parts = code.split(":")
        if len(code_parts) != 3:
            return False

        activation_code, expiry_date, salt = code_parts

        current_date = datetime.datetime.now().strftime("%Y%m%d")
        if current_date > expiry_date:
            return False

        machine_code = get_machine_code()
        expected_code = hashlib.sha256((machine_code + expiry_date + salt).encode()).hexdigest().upper()
        return activation_code == expected_code
    except:
        return False

def is_activated():
    if os.path.exists(LICENSE_FILE):
        with open(LICENSE_FILE, 'r') as f:
            saved = f.read().strip()
        return verify_activation_code(saved)
    return False

def save_activation_code(code):
    with open(LICENSE_FILE, 'w') as f:
        f.write(code)

def prompt_for_activation():
    root = tk.Tk()
    root.title("软件激活")
    root.geometry("400x300")

    machine_code = get_machine_code()

    ttk.Label(root, text="请输入激活码:").pack(pady=10)
    code_entry = ttk.Entry(root, width=50)
    code_entry.pack(pady=5)

    ttk.Label(root, text="机器码（发送给作者）:").pack(pady=5)
    machine_code_label = ttk.Label(root, text=machine_code)
    machine_code_label.pack(pady=5)

    def copy_machine_code():
        pyperclip = lazy_import_pyperclip()
        if pyperclip:
            pyperclip.copy(machine_code)
            messagebox.showinfo("提示", "机器码已复制到剪贴板")
        else:
            messagebox.showerror("错误", "无法复制到剪贴板")

    ttk.Button(root, text="复制机器码", command=copy_machine_code).pack(pady=5)

    def activate():
        activation_code = code_entry.get().strip()
        if verify_activation_code(activation_code):
            save_activation_code(activation_code)
            messagebox.showinfo("成功", "激活成功！")
            root.destroy()
        else:
            messagebox.showerror("错误", "激活码无效或已过期")

    ttk.Button(root, text="激活", command=activate).pack(pady=10)
    root.mainloop()

# 文件选择函数
def select_folder(entry):
    folder_path = filedialog.askdirectory()
    if folder_path:  # 确保用户选择了文件夹
        entry.delete(0, tk.END)  # 清空当前输入
        entry.insert(0, folder_path)  # 插入选择的文件夹路径

def select_pdf(entry):
    pdf_path = filedialog.askopenfilename(filetypes=[("PDF Files", "*.pdf")])
    if pdf_path:  # 确保用户选择了文件
        entry.delete(0, tk.END)  # 清空当前输入
        entry.insert(0, pdf_path)  # 插入选择的 PDF 文件路径

# 功能一：处理文件和PDF数据
def check_missing_files(df, source_folder, small_pdf_folder):
    """检查缺失的文件并返回缺失信息和统计数据"""
    missing_files = []
    matched_images = []
    matched_small_pdfs = []
    duplicate_skcs = []

    # 统计所有SKC的图片数量和具体文件名
    skc_image_count = {}
    skc_image_files = {}  # 记录每个SKC对应的具体文件名

    for index, row in df.iterrows():
        product_info = row['商品信息']

        # 检查 product_info 是否为非空字符串
        if not isinstance(product_info, str) or not product_info.strip():
            logging.warning(f"第{index}行：'商品信息' 无效或为空，跳过此行SKC检查。")
            continue # 如果商品信息无效或为空，跳过此行

        # 修改正则表达式，使其能匹配 SKC 后面的 9 到 11 位数字
        skc_number_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if not skc_number_match:
              continue

        skc_base = skc_number_match.group(1)
        logging.info(f"第{index}行：从商品信息 '{product_info}' 中提取到 SKC: {skc_base} (位数: {len(skc_base)})") # 添加日志

        # 检查图片文件是否存在，使用 move_files_with_suffix 查找
        image_paths = move_files_with_suffix(source_folder, skc_base)
        if image_paths:
            matched_images.extend(image_paths)
            # 统计每个SKC的图片数量
            if skc_base in skc_image_count:
                skc_image_count[skc_base] += len(image_paths)
            else:
                skc_image_count[skc_base] = len(image_paths)

            # 记录每个SKC对应的具体文件名
            if skc_base not in skc_image_files:
                skc_image_files[skc_base] = []
            for image_path in image_paths:
                filename = os.path.basename(image_path)
                skc_image_files[skc_base].append(filename)
        else:
            missing_files.append(f"SKC{skc_base} 的图片文件")

        # 检查拆分后的小标PDF，查找纯数字文件名 (9-11位)
        found_small_pdf = False
        if os.path.isdir(small_pdf_folder):
             for filename in os.listdir(small_pdf_folder):
                 # 检查文件名是否是纯数字 (9-11位) 加上 .pdf
                 match = re.fullmatch(r'\d{9,11}\.pdf', filename.lower())
                 if match:
                      # 检查提取的SKC编号是否与文件名的数字部分匹配
                      file_skc = filename.split('.')[0] # 获取文件名的数字部分
                      # 如果提取的SKC与文件名完全匹配，或者提取的SKC是文件名的9位前缀
                      if skc_base == file_skc or (len(skc_base) == 9 and file_skc.startswith(skc_base) and len(file_skc) == 11):
                           small_pdf_path = os.path.join(small_pdf_folder, filename)
                           matched_small_pdfs.append(small_pdf_path)
                           found_small_pdf = True
                           logging.info(f"第{index}行：找到匹配的小标PDF文件: {filename} (匹配SKC: {skc_base})")
                           break # 找到一个匹配的就停止查找

        if not found_small_pdf:
            missing_files.append(f"{skc_base} 的拆分后小标PDF文件")

    # 检查重复的SKC（考虑文件名序号，如-1、-2、-3等）
    for skc, files in skc_image_files.items():
        if len(files) > 1:
            # 提取文件名中的序号
            file_suffixes = set()
            for filename in files:
                # 提取SKC后面的部分作为后缀（包括-1、-2、-3等序号）
                if filename.lower().startswith(skc.lower()):
                    suffix = filename[len(skc):].lower()  # 获取SKC后面的部分
                    file_suffixes.add(suffix)

            # 如果所有文件的后缀都不同，则不算重复
            if len(file_suffixes) == len(files):
                logging.info(f"SKC {skc} 有 {len(files)} 个文件，但序号不重复: {list(file_suffixes)}")
            else:
                # 如果有相同的后缀，才算重复
                duplicate_skcs.append(skc)
                logging.warning(f"SKC {skc} 有重复文件: {files}")

    # 返回统计信息
    stats = {
        'matched_images_count': len(matched_images),
        'matched_small_pdfs_count': len(matched_small_pdfs),
        'has_duplicates': len(duplicate_skcs) > 0,
        'duplicate_skcs': duplicate_skcs
    }

    return missing_files, stats

def show_missing_files_dialog(missing_files):
    """显示可复制的缺失文件对话框"""
    dialog = tk.Toplevel()
    dialog.title("缺失文件列表")
    dialog.geometry("600x400")

    # 创建文本框和滚动条
    frame = ttk.Frame(dialog)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    scrollbar = ttk.Scrollbar(frame)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    text = tk.Text(frame, yscrollcommand=scrollbar.set, wrap=tk.WORD)
    text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.config(command=text.yview)

    # 插入缺失文件列表
    text.insert(tk.END, "以下文件缺失：\n\n")
    for file in missing_files:
        text.insert(tk.END, file + "\n")

    # 设置文本框只读
    text.config(state=tk.DISABLED)

    # 添加按钮框架
    button_frame = ttk.Frame(dialog)
    button_frame.pack(fill=tk.X, padx=10, pady=5)

    def copy_skc_numbers():
        """只复制SKC数字到剪贴板"""
        skc_numbers = []
        for file in missing_files:
            # 使用正则表达式提取SKC数字 (匹配 9 到 11 位纯数字)
            match = re.search(r'(\d{9,11})', file)
            if match:
                skc_numbers.append(match.group(1))

        if skc_numbers:
            dialog.clipboard_clear()
            dialog.clipboard_append('\n'.join(skc_numbers))
            messagebox.showinfo("提示", "已复制SKC数字到剪贴板")
        else:
            messagebox.showinfo("提示", "没有找到SKC数字")

    def continue_processing():
        """继续处理"""
        dialog.destroy()
        return True

    def cancel_processing():
        """取消处理"""
        dialog.destroy()
        return False

    # 添加按钮
    ttk.Button(button_frame, text="复制SKC数字", command=copy_skc_numbers).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="继续处理", command=continue_processing).pack(side=tk.RIGHT, padx=5)
    ttk.Button(button_frame, text="取消", command=cancel_processing).pack(side=tk.RIGHT, padx=5)

    # 设置模态对话框
    dialog.transient(dialog.master)
    dialog.grab_set()
    dialog.wait_window()

    return dialog.result if hasattr(dialog, 'result') else False

def copy_duplicate_skcs():
    """复制重复SKC编号到剪贴板的独立功能"""
    # 延迟导入pyperclip
    pyperclip = lazy_import_pyperclip()
    if not pyperclip:
        return

    pdf_file_path = pdf_entry.get() # 拣货单PDF
    source_folder = source_entry.get()

    if not (os.path.exists(pdf_file_path) and pdf_file_path.lower().endswith('.pdf')):
        messagebox.showerror("错误", "请先选择有效的拣货单PDF文件。")
        return
    if not (os.path.exists(source_folder) and os.path.isdir(source_folder)):
        messagebox.showerror("错误", "请先选择有效的源文件夹。")
        return

    # 提取数据
    df = extract_pdf_data(pdf_file_path)
    if df is None or df.empty:
        messagebox.showerror("错误", "未能从拣货单PDF中提取任何数据，请检查文件。")
        return

    # 创建临时小标PDF文件夹用于检查
    path_prefix = path_entry.get()
    if not path_prefix:
        messagebox.showerror("错误", "请先选择目标文件夹。")
        return

    small_pdf_folder = os.path.join(path_prefix, '小标PDF')

    # 检查重复SKC
    _, stats = check_missing_files(df, source_folder, small_pdf_folder)

    if stats['duplicate_skcs']:
        duplicate_skcs_str = ','.join(stats['duplicate_skcs'])
        pyperclip.copy(duplicate_skcs_str)
        messagebox.showinfo("提示", f"已复制重复SKC编号到剪贴板：\n{duplicate_skcs_str}")
    else:
        messagebox.showinfo("提示", "未发现重复的SKC编号。")

def run_script_1(progress_bar, progress_label):
    path_prefix = path_entry.get()
    pdf_file_path = pdf_entry.get() # 拣货单PDF
    source_folder = source_entry.get()
    small_pdf_path = small_entry.get() # 小标源PDF

    # 验证路径
    if not (os.path.exists(path_prefix) and os.path.isdir(path_prefix)):
        messagebox.showerror("错误", "目标文件夹路径无效。")
        return
    if not (os.path.exists(pdf_file_path) and pdf_file_path.lower().endswith('.pdf')):
        messagebox.showerror("错误", "拣货单PDF文件路径无效。")
        return
    if not (os.path.exists(source_folder) and os.path.isdir(source_folder)):
        messagebox.showerror("错误", "源文件夹路径无效。")
        return
    if not (os.path.exists(small_pdf_path) and small_pdf_path.lower().endswith('.pdf')):
         messagebox.showerror("错误", "小标源PDF文件路径无效。")
         return

    # 创建小标PDF文件夹
    small_pdf_folder = os.path.join(path_prefix, '小标PDF')
    os.makedirs(small_pdf_folder, exist_ok=True)

    # 拆分小标PDF
    split_pdf(small_pdf_path, small_pdf_folder)

    # 提取数据
    df = extract_pdf_data(pdf_file_path)
    if df.empty:
        messagebox.showerror("错误", "未能从拣货单PDF中提取任何数据，请检查文件。")
        return

    # 检查图片和小标PDF缺失文件，获取统计信息
    missing_files, stats = check_missing_files(df, source_folder, small_pdf_folder)

    # 输出检查结果日志
    logging.info("检查结果：")
    logging.info(f"- 匹配到的图片数量：{stats['matched_images_count']}")
    logging.info(f"- 匹配到的小标PDF数量：{stats['matched_small_pdfs_count']}")
    logging.info(f"- 是否有重复图片：{'有' if stats['has_duplicates'] else '无'}")
    if stats['duplicate_skcs']:
        duplicate_skcs_str = ','.join(stats['duplicate_skcs'])
        logging.info(f"- 重复图片SKC编号：{duplicate_skcs_str}（可复制粘贴）")
    else:
        logging.info("- 重复图片SKC编号：无")

    if missing_files:
        if show_missing_files_dialog(missing_files):
            process_files(df, path_prefix, source_folder, small_pdf_folder, progress_bar, progress_label)
            messagebox.showinfo("完成", "功能一处理完成。")
        else:
            messagebox.showinfo("提示", "已取消功能一处理。")
    else:
        process_files(df, path_prefix, source_folder, small_pdf_folder, progress_bar, progress_label)
        messagebox.showinfo("完成", "功能一处理完成。")

# 功能二：拆分PDF并重命名
def run_script_2():
    path_prefix = path_entry.get()
    big_pdf_file_path = pdf_entry_2.get() # 大标源PDF

    output_dir = os.path.join(path_prefix, '大标')

    # 验证路径
    if not os.path.exists(path_prefix) or not os.path.isdir(path_prefix):
        messagebox.showerror("错误", "目标文件夹路径无效。")
        return
    if not os.path.exists(big_pdf_file_path) or not big_pdf_file_path.lower().endswith('.pdf'):
        messagebox.showerror("错误", "大标PDF文件路径无效。")
        return

    # 创建大标输出文件夹
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 拆分大标PDF
    split_pdf_rename(big_pdf_file_path, output_dir)

    # 统计拆分出的大标PDF数量
    big_pdf_count = 0
    if os.path.exists(output_dir):
        for filename in os.listdir(output_dir):
            if filename.lower().endswith('.pdf'):
                big_pdf_count += 1

    # 输出检查结果日志
    logging.info("检查结果：")
    logging.info(f"- 匹配到的大标PDF数量：{big_pdf_count}")

    # 提取拣货单SKC列表
    pdf_file_path_func1 = pdf_entry.get() # 拣货单PDF路径
    if not (os.path.exists(pdf_file_path_func1) and pdf_file_path_func1.lower().endswith('.pdf')):
        messagebox.showinfo("完成", f"大标文件拆分完成，共生成 {big_pdf_count} 个PDF文件")
        return

    df_func1 = extract_pdf_data(pdf_file_path_func1)
    if df_func1.empty:
         messagebox.showinfo("完成", f"大标文件拆分完成，共生成 {big_pdf_count} 个PDF文件")
         return

    skc_list_raw = df_func1['SKU货号'].dropna().unique().tolist()

    # 严格过滤SKC列表，只保留能转换为整数的项
    skc_list = []
    for item in skc_list_raw:
        try:
            # 尝试将项转换为整数
            skc_int = int(float(str(item).strip()))
            # 如果成功，将其作为字符串添加到列表中
            skc_list.append(str(skc_int))
        except (ValueError, TypeError) as e:
            # 如果转换失败，说明不是有效的数字，过滤掉
            logging.warning(f"过滤掉无效SKU货号 '{item}' (无法转换为整数): {e}")
            continue

    # 检查拆分后的大标PDF文件
    missing_big_pdfs = []
    matched_big_pdfs = []
    for skc_str in skc_list:
        # 确保skc_str非空且为数字
        if not isinstance(skc_str, str) or not skc_str.isdigit() or not skc_str:
             logging.warning(f"检查大标PDF时跳过无效或空SKC货号: '{skc_str}'")
             continue # 跳过无效或空的SKC

        # 拆分后的大标文件名格式是 SKC_PC_Count_PageNum.pdf
        # 我们只需要检查对应SKC的文件是否存在即可
        found = False
        # 使用更精确的匹配，确保文件名以SKC数字开头并紧跟下划线
        for filename in os.listdir(output_dir):
            # 确保 filename 是字符串类型再进行操作
            if isinstance(filename, str) and filename.startswith(f"{skc_str}_") and filename.lower().endswith('.pdf'):
                matched_big_pdfs.append(filename)
                found = True
                break
        if not found:
             # 在添加到缺失列表前再次确认skc_str有效
             if skc_str and skc_str.isdigit():
                 missing_big_pdfs.append(f"SKC{skc_str} 的拆分后大标PDF文件")
             else:
                 # 这应该不会发生，但作为最后的保障
                 logging.error(f"尝试添加无效SKC到缺失列表: '{skc_str}'")

    if missing_big_pdfs:
        show_missing_files_dialog(missing_big_pdfs)
    else:
        # 拆分完成后，自动移动大标到对应文件夹
        logging.info("开始自动移动大标到对应文件夹...")
        move_big_labels_to_folders(df_func1, path_prefix, output_dir)
        messagebox.showinfo("完成", f"大标文件拆分及移动完成，共生成 {big_pdf_count} 个PDF文件")

# 提取PDF中的表格数据
def extract_pdf_data(pdf_file_path):
    # 延迟导入
    pdfplumber = lazy_import_pdfplumber()
    pd = lazy_import_pandas()

    if not pdfplumber or not pd:
        return None

    data = []
    try:
        with pdfplumber.open(pdf_file_path) as pdf:
            for page in pdf.pages:
                table = page.extract_table()
                if table:
                    for row in table[1:]:  # 跳过表头
                        data.append(row)
        if not data:
            logging.error("未从PDF中提取到任何数据。")
            return pd.DataFrame()
        else:
            logging.info(f"成功提取到{len(data)}行数据。")
            return pd.DataFrame(data, columns=["序号", "商品信息", "属性集", "SKU ID", "SKU货号", "数量", "拣货数"])
    except Exception as e:
        logging.error(f"读取PDF文件时出错: {e}")
        messagebox.showerror("错误", f"读取PDF文件时出错: {e}")
        return pd.DataFrame()

# 拆分PDF文件
def split_pdf(input_pdf_path, output_folder, log_callback=None):
    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        return

    try:
        if not os.path.exists(input_pdf_path):
            if log_callback:
                log_callback(f"输入文件不存在: {input_pdf_path}")
            else:
                logging.error(f"输入文件不存在: {input_pdf_path}")
            return
        numbers = []
        with open(input_pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for i, page in enumerate(reader.pages):
                text = page.extract_text()
                number = extract_number_from_text(text)
                if number:
                    numbers.append((i, number))
        with open(input_pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for i, page in enumerate(reader.pages):
                writer = PyPDF2.PdfWriter()
                writer.add_page(page)
                for index, number in numbers:
                    if index == i:
                        output_pdf_path = os.path.join(output_folder, f"{number}.pdf")
                        with open(output_pdf_path, 'wb') as output_file:
                            writer.write(output_file)
    except Exception as e:
        if log_callback:
            log_callback(f"发生错误: {e}")
        else:
            logging.error(f"发生错误: {e}")

# 提取数字
def extract_number_from_text(text):
    # 修改正则表达式以匹配 9 到 11 位的数字序列
    match = re.search(r'\b\d{9,11}\b', text)
    if match:
        return match.group(0)
    else:
        logging.error("未能从文本中提取数字。")
        return None


def move_files_with_suffix(source_folder, skc_base):
    """
    查找与SKC相关的图片文件，支持多种后缀格式，返回所有找到的路径列表
    """
    found_files = []

    # 直接遍历文件夹中的所有文件，查找以SKC开头的jpg文件
    if os.path.isdir(source_folder):
        for filename in os.listdir(source_folder):
            # 检查文件名是否以 skc_base 开头，且以 .jpg 结尾 (忽略大小写)
            if filename.lower().startswith(skc_base.lower()) and filename.lower().endswith('.jpg'):
                file_path = os.path.join(source_folder, filename)
                if os.path.isfile(file_path): # 确保是文件而不是文件夹
                     found_files.append(file_path)

    return found_files

def copy_file(source_path, target_folder, file_extension=None):
    """
    复制单个文件，如果存在的话
    file_extension参数保留用于兼容性，但当前未使用
    """
    file_name = os.path.basename(source_path)
    target_path = os.path.join(target_folder, file_name)
    if os.path.exists(source_path):
        os.makedirs(target_folder, exist_ok=True)
        if not os.path.exists(target_path):
            try:
                shutil.copy(source_path, target_path)
                logging.info(f"成功复制文件: {source_path} 到 {target_path}")
            except Exception as e:
                logging.error(f"复制文件失败：{source_path} 到 {target_path}，原因：{e}")
        else:
            logging.warning(f"目标文件已存在，跳过：{target_path}")
    else:
        logging.warning(f"文件未找到：{source_path}")

    # 避免未使用参数警告
    _ = file_extension

def round_size(value):
    """
    四舍五入尺寸，返回字符串格式
    """
    return str(round(float(value), 1))

# API调用缓存
_api_cache = {}

def call_glm_api(attribute_set):
    # 延迟导入requests
    requests = lazy_import_requests()
    if not requests:
        return None

    # 检查缓存
    if attribute_set in _api_cache:
        logging.info(f"使用缓存的API结果: {attribute_set}")
        return _api_cache[attribute_set]

    # 简单过滤：如果属性集太短或明显无效，不调用API
    if len(attribute_set.strip()) < 5 or attribute_set.strip().lower() in ['unknown', 'nan', 'null', '']:
        logging.info(f"跳过无效属性集的API调用: {attribute_set}")
        return None

    url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
    headers = {
        "Authorization": "Bearer a45ebb96984f4479b067bc2d2e3f32a1.NZqmt7J0wX4uaGDe",
        "Content-Type": "application/json"
    }
    prompt = (
        f"请帮我从以下描述中智能提取标准画布尺寸和数量，返回格式如'24x16inch, 60x40cm, 3pc'，"
        f"如果有多个尺寸请全部列出：{attribute_set}"
    )
    data = {
        "model": "glm-4-flash-250414",
        "messages": [{"role": "user", "content": prompt}]
    }
    try:
        resp = requests.post(url, headers=headers, json=data, timeout=10)
        if resp.status_code == 200:
            content = resp.json()['choices'][0]['message']['content']
            logging.info(f"GLM-4-Flash-250414原始返回: {content}")
            # 缓存结果
            _api_cache[attribute_set] = content
            return content
        else:
            logging.error(f"GLM API调用失败: {resp.status_code}, {resp.text}")
    except Exception as e:
        logging.error(f"GLM API调用异常: {e}")
    return None

def extract_size_from_ai(ai_text):
    # 修复：支持小数点和正确的格式匹配
    # 格式1: 29.97cm x 39.88cm (每个数字后都有cm)
    cm_pattern1 = r'(\d+\.?\d*)\s*cm\s*[x×*]\s*(\d+\.?\d*)\s*cm'
    cm_match1 = re.search(cm_pattern1, ai_text, re.IGNORECASE)
    if cm_match1:
        cm_str = f"{cm_match1.group(1)}cm x {cm_match1.group(2)}cm"
        cm_matches = [cm_str]
    else:
        # 格式2: 29.97 x 39.88 cm (最后才有cm)
        cm_pattern2 = r'(\d+\.?\d*)\s*[x×*]\s*(\d+\.?\d*)\s*cm'
        cm_matches = re.findall(cm_pattern2, ai_text, re.IGNORECASE)
        if cm_matches:
            # 重新格式化为标准格式
            cm_matches = [f"{match[0]}cm x {match[1]}cm" for match in cm_matches]
        else:
            cm_matches = []

    # inch格式处理
    inch_pattern1 = r'(\d+\.?\d*)\s*inch\s*[x×*]\s*(\d+\.?\d*)\s*inch'
    inch_match1 = re.search(inch_pattern1, ai_text, re.IGNORECASE)
    if inch_match1:
        inch_str = f"{inch_match1.group(1)}inch x {inch_match1.group(2)}inch"
        inch_matches = [inch_str]
    else:
        inch_pattern2 = r'(\d+\.?\d*)\s*[x×*]\s*(\d+\.?\d*)\s*(?:in|inch)'
        inch_matches = re.findall(inch_pattern2, ai_text, re.IGNORECASE)
        if inch_matches:
            inch_matches = [f"{match[0]}inch x {match[1]}inch" for match in inch_matches]
        else:
            inch_matches = []

    pc_match = re.search(r'(\d+)\s*pc', ai_text, re.IGNORECASE)
    logging.info(f"AI二次提取: cm={cm_matches}, inch={inch_matches}, pc={pc_match.group(1) if pc_match else None}")
    return cm_matches, inch_matches, pc_match.group(1) if pc_match else None

def get_size_folder(attribute_set):
    logging.info(f"--- 处理属性集: {attribute_set} ---")

    # 先清理换行符和多余空格，然后检查复杂前缀
    cleaned_text = re.sub(r'[\n\r]+', ' ', attribute_set).strip()
    logging.info(f"清理换行符后的属性集: {cleaned_text}")

    # 最优先：检查特殊的Two-sided双面抱枕（在复杂前缀检查之前）
    two_sided_18_patterns = [
        r'two[-\s]*sided?.*?18\s*[x×*X]\s*18\s*inch',  # 支持two-side和two-sided
        r'two[-\s]*sided?.*?18\s*inch\s*[x×*X]\s*18\s*inch',
        r'two[-\s]*sided?.*?18\s*[x×*X]\s*18\s*i',  # 支持inch被截断为i
        r'two[-\s]*sided?.*?18[x×*X]18\s*in',  # 修复：无空格的18X18in格式
        r'two[-\s]*sided?.*?18[x×*X]18\s*i',   # 修复：无空格的18X18i格式
        r'two[-\s]*side.*?18\s*[x×*X]\s*18',  # 更宽松的匹配
        r'two[-\s]*side.*?18[x×*X]18',  # 修复：无空格的18X18格式
        r'双面.*?18\s*[x×*X]\s*18\s*inch',
        r'双面.*?18\s*inch\s*[x×*X]\s*18\s*inch',
        r'双面.*?18\s*[x×*X]\s*18\s*i',  # 支持inch被截断为i
        r'双面.*?18\s*[x×*X]\s*18',  # 更宽松的匹配
        r'双面.*?18[x×*X]18'  # 修复：无空格的18X18格式
    ]

    for i, pattern in enumerate(two_sided_18_patterns):
        if re.search(pattern, cleaned_text, re.IGNORECASE):
            logging.info(f"✅ 最优先识别：Two-sided双面抱枕 (模式{i+1}: {pattern})")
            return "two_sided_18x18inch"

    # 其次：如果包含其他复杂前缀（排除two-side双面抱枕），直接返回unknown，保持原始属性集
    if re.search(r'\b(three|four|five|six|seven|eight|nine|ten|双|三|四|五|六|七|八|九|十)-', cleaned_text, re.IGNORECASE):
        logging.info(f"检测到复杂前缀，直接返回unknown保持原始属性集: {cleaned_text}")
        return "unknown"

    # 增强的预处理：保留原始文本用于特殊匹配，创建清理版本用于通用匹配
    original_lower = cleaned_text.lower()
    cleaned_attribute_set = cleaned_text.replace(' ', '').strip().lower()
    logging.info(f"移除空格后的属性集: {cleaned_attribute_set}")

    # ====== 黑框/金框特殊命名逻辑 ======
    # 黑框：基本格式 11.8x15.7inch-H、11.8x15.7inch--H 和 11.7x15.8inch-H
    black_patterns = [
        r'11\.8\s*[x×*]\s*15\.7\s*inch\s*-+\s*h',  # 支持一个或多个连字符
        r'11\.7\s*[x×*]\s*15\.8\s*inch\s*-+\s*h'   # 支持一个或多个连字符
    ]
    for pattern in black_patterns:
        if re.search(pattern, original_lower):
            logging.info(f"匹配到黑框模式: {pattern} (原文: {attribute_set})")
            return "black_30x40"

    # 金框：基本格式 G-11.8x15.7inch 和 G-11.7x15.8inch（但排除Canvas Painting）
    if not re.search(r'canvas\s*paintin?\s*g?', original_lower):
        gold_patterns = [
            r'g\s*-\s*11\.8\s*[x×*]\s*15\.7\s*inch',
            r'g\s*-\s*11\.7\s*[x×*]\s*15\.8\s*inch'
        ]
        for pattern in gold_patterns:
            if re.search(pattern, original_lower):
                logging.info(f"匹配到金框模式: {pattern}")
                return "gold_30x40"

    # ====== 增强的尺寸识别逻辑 ======
    # 1. 先尝试识别特定的尺寸模式（如Canvas Painting-11.8inch/30cm*15.7inch/40cm*3pcs）
    enhanced_result = enhanced_size_recognition(attribute_set)
    if enhanced_result != "unknown":
        return enhanced_result

    # 2. 原有的正则表达式匹配（增强版）
    # 支持更多分隔符和格式
    size_patterns = [
        r'(\d+\.?\d*)\s*[x×*]\s*(\d+\.?\d*)\s*(cm|inch|in|英寸)',  # 标准格式
        r'(\d+\.?\d*)\s*(cm|inch|in)\s*[x×*/]\s*(\d+\.?\d*)\s*(cm|inch|in)',  # 带单位的格式
        r'(\d+\.?\d*)\s*[x×*]\s*(\d+\.?\d*)',  # 无单位格式
        r'(\d+\.?\d*)\s*(inch|in)\s*[x×*/]\s*(\d+\.?\d*)\s*(inch|in)',  # inch格式
    ]

    matches = []
    for pattern in size_patterns:
        pattern_matches = re.findall(pattern, cleaned_attribute_set)
        if pattern_matches:
            matches.extend(pattern_matches)
            break

    logging.info(f"增强正则表达式找到的匹配项: {matches}")

    # 判断单位
    unit = determine_unit(attribute_set)
    logging.info(f"判断单位为: {unit}")

    for match in matches:
        try:
            # 处理不同的匹配格式
            if len(match) == 3:  # (数字, 数字, 单位)
                width_str, height_str, match_unit = match[0], match[1], match[2]
                if match_unit.lower() in ['inch', 'in', '英寸']:
                    unit = 'inch'
                elif match_unit.lower() == 'cm':
                    unit = 'cm'
            elif len(match) == 4:  # (数字, 单位, 数字, 单位)
                width_str, unit1, height_str, unit2 = match
                # 如果两个单位都是inch或都是cm，使用该单位
                if unit1.lower() in ['inch', 'in'] and unit2.lower() in ['inch', 'in']:
                    unit = 'inch'
                elif unit1.lower() == 'cm' and unit2.lower() == 'cm':
                    unit = 'cm'
            else:  # len(match) == 2，无单位格式
                width_str, height_str = match[0], match[1]

            logging.info(f"处理数字对: {width_str} x {height_str} ({unit})")

            width = float(width_str)
            height = float(height_str)

            # 转换为厘米
            width_cm, height_cm = convert_to_cm(width, height, unit)
            logging.info(f"转换为厘米尺寸: {width_cm:.2f} x {height_cm:.2f}")

            # 匹配标准尺寸
            standard_size = match_standard_size(width_cm, height_cm)
            if standard_size != "unknown":
                return standard_size

        except (ValueError, IndexError) as e:
            logging.warning(f"无法解析尺寸值从匹配项 '{match}': {e}")
            continue

    # ========== GLM-4-Flash-250414大模型API兜底 ==========
    logging.info("本地规则全部失败，调用GLM-4-Flash-250414大模型API兜底...")
    glm_result = call_glm_api(attribute_set)
    if glm_result:
        cm_matches, inch_matches, _ = extract_size_from_ai(glm_result)
        # 优先用cm尺寸 - 先检查预定义格式，再进行数值匹配
        if cm_matches:
            cm_str = cm_matches[0].replace(' ', '').replace('*', 'x').replace('×', 'x').lower()

            # 先检查预定义格式
            if cm_str in ["60x40cm", "40x60cm"]:
                return "40x60cm"
            elif cm_str == "40x40cm":
                return "40x40cm"
            elif cm_str in ["30x40cm", "40x30cm"]:
                return "30x40cm"

            # 如果不匹配预定义格式，尝试数值解析和标准化匹配
            cm_pattern = r'(\d+\.?\d*)cm\s*[x×*]\s*(\d+\.?\d*)cm'
            cm_match = re.search(cm_pattern, cm_str)
            if cm_match:
                try:
                    width_cm = float(cm_match.group(1))
                    height_cm = float(cm_match.group(2))
                    logging.info(f"AI返回的cm尺寸数值: {width_cm}x{height_cm}cm")

                    # 使用标准尺寸匹配函数
                    standard_size = match_standard_size(width_cm, height_cm)
                    if standard_size != "unknown":
                        logging.info(f"AI返回的尺寸匹配到标准尺寸: {standard_size}")
                        return standard_size
                except ValueError:
                    pass

            # 兜底返回原始字符串
            return cm_str
        # 其次用inch尺寸
        if inch_matches:
            inch_str = inch_matches[0].replace(' ', '').replace('*', 'x').replace('×', 'x').lower()
            return inch_str
        # 兜底返回原始内容
        return glm_result
    else:
        logging.warning(f"GLM-4-Flash-250414 API也未能识别: {attribute_set}")
        return "unknown"

def enhanced_size_recognition(attribute_set):
    """
    增强的尺寸识别函数，处理复杂的尺寸格式和英文干扰
    """
    original_text = attribute_set.lower()
    logging.info(f"增强尺寸识别处理: {attribute_set}")

    # 特殊模式：Canvas Painting-11.8inch/30cm*15.7inch/40cm*3pcs 和 11.7x15.8inch 变体
    # 增强版：支持空格分隔的复杂格式
    canvas_patterns = [
        # 11.8x15.7 Canvas Painting 模式 - 增强版，支持空格
        r'canvas\s*paintin?\s*g?\s*-?\s*11\.8\s*inch\s*/?\s*30\s*c?\s*m?\s*[x×*]\s*15\.7\s*inch\s*/?\s*40\s*c?\s*m?\s*[x×*]?\s*(\d+)\s*pcs?',
        # 11.7x15.8 Canvas Painting 模式 - 增强版，支持空格
        r'canvas\s*paintin?\s*g?\s*-?\s*11\.7\s*inch\s*/?\s*30\s*c?\s*m?\s*[x×*]\s*15\.8\s*inch\s*/?\s*40\s*c?\s*m?\s*[x×*]?\s*(\d+)\s*pcs?',
        # 通用 Canvas Painting 模式 - 增强版，支持空格分隔
        r'canvas\s*paintin?\s*g?\s*-?\s*(\d+\.?\d*)\s*inch\s*/?\s*(\d+\.?\d*)\s*c?\s*m?\s*[x×*]\s*(\d+\.?\d*)\s*inch\s*/?\s*(\d+\.?\d*)\s*c?\s*m?\s*[x×*]?\s*(\d+)\s*pcs?',
        # 更宽松的Canvas Painting模式，处理各种空格和分隔符
        r'canvas.*?(\d+\.?\d*)\s*inch.*?(\d+\.?\d*)\s*cm.*?(\d+\.?\d*)\s*inch.*?(\d+\.?\d*)\s*cm.*?(\d+)\s*pc'
    ]

    for pattern in canvas_patterns:
        canvas_match = re.search(pattern, original_text)
        if canvas_match:
            groups = canvas_match.groups()
            if len(groups) == 1:  # 特定尺寸模式，只有pc数量
                logging.info(f"匹配到特定Canvas Painting模式: {pattern}")
                return "30x40cm"  # 11.8x15.7 和 11.7x15.8 都对应30x40cm
            elif len(groups) == 5:  # 通用模式
                inch_w, cm_w, inch_h, cm_h, pc_count = groups
                logging.info(f"匹配到通用Canvas Painting模式: {inch_w}inch/{cm_w}cm x {inch_h}inch/{cm_h}cm x {pc_count}pcs")
                # 使用厘米尺寸进行匹配
                try:
                    width_cm = float(cm_w)
                    height_cm = float(cm_h)
                    standard_size = match_standard_size(width_cm, height_cm)
                    if standard_size != "unknown":
                        return standard_size
                except ValueError:
                    pass
            break

    # 11.7x15.8inch 和 11.8x15.7inch 的各种变体识别（排除金框黑框）
    if not re.search(r'(g\s*-|gold|-\s*h|black)', original_text):
        inch_11_patterns = [
            # 11.7x15.8inch 变体
            r'\b11\.7\s*[x×*]\s*15\.8\s*inch\b',
            r'\b11\.7\s*inch\s*[x×*]\s*15\.8\s*inch\b',
            r'\b11\.7\s*in\s*[x×*]\s*15\.8\s*in\b',
            r'\b15\.8\s*[x×*]\s*11\.7\s*inch\b',  # 宽高互换
            # 11.8x15.7inch 变体
            r'\b11\.8\s*[x×*]\s*15\.7\s*inch\b',
            r'\b11\.8\s*inch\s*[x×*]\s*15\.7\s*inch\b',
            r'\b11\.8\s*in\s*[x×*]\s*15\.7\s*in\b',
            r'\b15\.7\s*[x×*]\s*11\.8\s*inch\b',  # 宽高互换
        ]

        for pattern in inch_11_patterns:
            if re.search(pattern, original_text):
                logging.info(f"匹配到11.7x15.8inch或11.8x15.7inch模式: {pattern}")
                # 这些尺寸都对应30x40cm
                return "30x40cm"

    # 完全禁用18x18inch的自动识别，避免影响复杂属性集名称
    # 让所有包含18x18inch的属性集都保持原样
    # if re.match(r'^18\s*[x×*]\s*18\s*inch?$', original_text.strip(), re.IGNORECASE):
    #     logging.info(f"匹配到纯18x18inch格式: {original_text}")
    #     return "18x18inch"

    # 特殊识别：12inch木板钟
    if re.search(r'12\s*inch', original_text):
        logging.info(f"检测到12inch木板钟: {original_text}")
        return "12inch"

    # 特殊识别：21.65x26.77inch围裙
    apron_patterns = [
        r'21\.65\s*[x×*]\s*26\.77\s*inch',
        r'21\.65\s*inch\s*[x×*]\s*26\.77\s*inch',
        r'26\.77\s*[x×*]\s*21\.65\s*inch',  # 宽高互换
        r'26\.77\s*inch\s*[x×*]\s*21\.65\s*inch',
        # 新增：支持更多格式
        r'21\.65\s*[x×*]\s*26\.77',  # 不带inch后缀
        r'26\.77\s*[x×*]\s*21\.65',  # 宽高互换，不带inch后缀
        r'1pc\s*21\.65\s*[x×*]\s*26\.77',  # 带1pc前缀
        r'1pc\s*26\.77\s*[x×*]\s*21\.65',  # 带1pc前缀，宽高互换
        # 支持小数点后可能有更多位数
        r'21\.65\d*\s*[x×*]\s*26\.77\d*',
        r'26\.77\d*\s*[x×*]\s*21\.65\d*'
    ]

    for pattern in apron_patterns:
        if re.search(pattern, original_text):
            logging.info(f"检测到21.65x26.77inch围裙: {original_text}")
            return "apron_55x68cm"



    # 18x48inch 的各种变体
    inch_18_48_patterns = [
        r'18\s*[x×*]\s*48\s*inch',
        r'18\s*inch\s*[x×*]\s*48\s*inch',
        r'18\s*in\s*[x×*]\s*48\s*in',
        r'48\s*[x×*]\s*18\s*inch',
        r'48\s*inch\s*[x×*]\s*18\s*inch'
    ]

    for pattern in inch_18_48_patterns:
        if re.search(pattern, original_text):
            logging.info(f"匹配到18x48inch模式: {pattern}")
            # 18x48inch = 45.72x121.92cm，接近40x120cm（超大尺寸）
            return "40x120cm"

    # 特殊处理：12inch木板钟（12inch = 30.48cm，应该识别为18x18inch）
    if re.search(r'\b12\s*inch\b', original_text):
        logging.info(f"检测到12inch木板钟，识别为18x18inch: {original_text}")
        return "18x18inch"

    # 通用的inch尺寸识别（支持各种分隔符）
    # 但排除包含复杂前缀的情况（排除two-side双面抱枕，因为已经在前面处理了）
    # 并且排除18x18inch的识别
    if not re.search(r'\b(three|four|five|six|seven|eight|nine|ten|双|三|四|五|六|七|八|九|十)-', original_text, re.IGNORECASE):
        inch_patterns = [
            r'(\d+\.?\d*)\s*[x×*]\s*(\d+\.?\d*)\s*inch',
            r'(\d+\.?\d*)\s*inch\s*[x×*]\s*(\d+\.?\d*)\s*inch',
            r'(\d+\.?\d*)\s*in\s*[x×*]\s*(\d+\.?\d*)\s*in',
            r'(\d+\.?\d*)\s*[x×*]\s*(\d+\.?\d*)\s*in',
            r'(\d+\.?\d*)\s*inx\s*(\d+\.?\d*)\s*in'
        ]

        for pattern in inch_patterns:
            match = re.search(pattern, original_text)
            if match:
                try:
                    width_inch = float(match.group(1))
                    height_inch = float(match.group(2))

                    # 注释掉18x18inch的排除逻辑，让它正常识别
                    # if width_inch == 18 and height_inch == 18:
                    #     logging.info(f"检测到18x18inch，跳过识别，保持原始属性集: {original_text}")
                    #     continue

                    width_cm, height_cm = convert_to_cm(width_inch, height_inch, 'inch')
                    logging.info(f"匹配到inch尺寸: {width_inch}x{height_inch}inch = {width_cm:.2f}x{height_cm:.2f}cm")

                    standard_size = match_standard_size(width_cm, height_cm)
                    if standard_size != "unknown":
                        return standard_size
                except ValueError:
                    continue

    # 通用的cm尺寸识别
    cm_patterns = [
        r'(\d+\.?\d*)\s*[x×*]\s*(\d+\.?\d*)\s*cm',
        r'(\d+\.?\d*)\s*cm\s*[x×*]\s*(\d+\.?\d*)\s*cm'
    ]

    for pattern in cm_patterns:
        match = re.search(pattern, original_text)
        if match:
            try:
                width_cm = float(match.group(1))
                height_cm = float(match.group(2))
                logging.info(f"匹配到cm尺寸: {width_cm}x{height_cm}cm")

                standard_size = match_standard_size(width_cm, height_cm)
                if standard_size != "unknown":
                    return standard_size
            except ValueError:
                continue

    return "unknown"

def determine_unit(attribute_set):
    """
    智能判断尺寸单位
    """
    text = attribute_set.lower()

    # 优先级：明确的单位标识
    if 'inch' in text or '英寸' in text:
        return 'inch'
    elif 'cm' in text and 'inch' not in text:
        return 'cm'
    elif re.search(r'\d+\s*in[^c]', text):  # 匹配 "18in" 但不匹配 "inch"
        return 'inch'
    elif re.search(r'\d+\s*inx', text):  # 匹配 "18inx18in"
        return 'inch'

    # 默认根据数值大小判断
    numbers = re.findall(r'(\d+\.?\d*)', text)
    if numbers:
        avg_size = sum(float(n) for n in numbers) / len(numbers)
        if avg_size > 50:  # 大于50的数字更可能是厘米
            return 'cm'
        elif avg_size < 25:  # 小于25的数字更可能是英寸
            return 'inch'

    return 'cm'  # 默认厘米

def convert_to_cm(width, height, unit):
    """
    将尺寸转换为厘米
    """
    if unit == 'inch':
        return width * 2.54, height * 2.54
    else:
        return width, height

def match_standard_size(width_cm, height_cm):
    """
    匹配标准尺寸，支持宽高互换，严格控制匹配范围
    """
    # 确保较小的值在前面（标准化）
    w, h = min(width_cm, height_cm), max(width_cm, height_cm)

    # 30x40cm 范围 (优先匹配，允许±4cm误差)
    if (26 <= w <= 34) and (36 <= h <= 44):
        logging.info(f"匹配到 30x40cm 范围: {w:.2f}x{h:.2f}cm")
        return "30x40cm"

    # 40x40cm 范围 (严格控制，允许±4cm误差)
    if (36 <= w <= 44) and (36 <= h <= 44):
        logging.info(f"匹配到 40x40cm 范围: {w:.2f}x{h:.2f}cm")
        return "40x40cm"

    # 40x60cm 范围 (允许±4cm误差)
    if (36 <= w <= 44) and (56 <= h <= 64):
        logging.info(f"匹配到 40x60cm 范围: {w:.2f}x{h:.2f}cm")
        return "40x60cm"

    # 特殊尺寸：40x120cm (18x48inch)
    if (36 <= w <= 44) and (116 <= h <= 124):
        logging.info(f"匹配到 40x120cm 范围: {w:.2f}x{h:.2f}cm")
        return "40x120cm"

    # 对于18x18inch (45.72x45.72cm)，正确匹配为18x18inch
    if (44 <= w <= 47) and (44 <= h <= 47):
        logging.info(f"匹配到18x18inch尺寸: {w:.2f}x{h:.2f}cm")
        return "18x18inch"

    # 边界情况：只有在误差很小时才匹配
    distances = {
        "30x40cm": abs(w - 30) + abs(h - 40),
        "40x40cm": abs(w - 40) + abs(h - 40),
        "40x60cm": abs(w - 40) + abs(h - 60)
    }

    min_distance = min(distances.values())
    if min_distance <= 8:  # 减小误差容忍度到8cm
        best_match = min(distances, key=distances.get)
        logging.info(f"边界匹配到 {best_match}: {w:.2f}x{h:.2f}cm (误差: {min_distance:.2f}cm)")
        return best_match

    logging.info(f"未匹配到标准尺寸: {w:.2f}x{h:.2f}cm")
    return "unknown"

# 新增：根据"合计"精准检测多规格行
def is_multi_spec_by_total(row):
    """
    检查该行所有字段是否包含"合计"二字，兼容Series和ndarray
    """
    try:
        iterable = row.values if hasattr(row, 'values') else row
        for v in iterable:
            if isinstance(v, str) and '合计' in v:
                return True
    except Exception:
        # 兜底：直接遍历 row
        for v in row:
            if isinstance(v, str) and '合计' in v:
                return True
    return False

# 新增：多规格拆分函数
def split_multi_specs(attribute_set):
    """
    拆分一行中的多规格描述，返回每个规格的字符串列表。
    支持常见分隔符：换行、分号、逗号、A-等。
    """
    if not isinstance(attribute_set, str):
        return []
    # 先用换行、分号、逗号拆分
    specs = re.split(r'[\n;,，；]+', attribute_set)
    # 再进一步用A-分割（但保留A-）
    result = []
    for spec in specs:
        parts = re.split(r'(?=A-)', spec)
        for part in parts:
            clean = part.strip()
            if clean:
                result.append(clean)
    return [s for s in result if s]

class EnhancedProgressTracker:
    """增强的进度跟踪器"""

    def __init__(self, progress_bar, progress_label, total_items):
        self.progress_bar = progress_bar
        self.progress_label = progress_label
        self.total_items = total_items
        self.current_item = 0
        self.stats = {
            'success': 0,
            'failed': 0,
            'skipped': 0,
            'duplicates': 0
        }
        self.current_phase = "准备中"
        self.start_time = None

    def start_processing(self):
        """开始处理"""
        import time
        self.start_time = time.time()
        self.update_display("开始处理", "正在初始化...")

    def update_progress(self, current_item, phase, detail="", status=None):
        """更新进度"""
        self.current_item = current_item
        self.current_phase = phase

        if status and status in self.stats:
            self.stats[status] += 1

        # 计算进度百分比
        if self.total_items > 0:
            progress_percent = (current_item / self.total_items) * 100
            self.progress_bar['value'] = progress_percent

        # 更新显示文本
        self.update_display(phase, detail)

    def update_display(self, phase, detail):
        """更新显示文本"""
        import time

        # 计算耗时
        elapsed_time = ""
        if self.start_time:
            elapsed = time.time() - self.start_time
            elapsed_time = f" | 耗时: {elapsed:.1f}s"

        # 构建状态文本
        status_text = (
            f"📊 进度: {self.current_item}/{self.total_items} "
            f"| 🔄 {phase}"
            f"{elapsed_time}\n"
            f"✅ 成功: {self.stats['success']} "
            f"❌ 失败: {self.stats['failed']} "
            f"⏭️ 跳过: {self.stats['skipped']} "
            f"🔄 重复: {self.stats['duplicates']}\n"
            f"📝 {detail}"
        )

        # 检查progress_label是否存在且不为None
        if self.progress_label is not None:
            try:
                self.progress_label.config(text=status_text)
                # 强制更新界面
                if hasattr(self.progress_label, 'update'):
                    self.progress_label.update()
            except Exception as e:
                logging.warning(f"更新进度标签失败: {e}")
        else:
            # 如果没有进度标签，输出到日志
            logging.info(f"进度更新: {phase} - {detail}")

    def finish_processing(self):
        """完成处理"""
        import time

        total_time = ""
        if self.start_time:
            total_elapsed = time.time() - self.start_time
            total_time = f"总耗时: {total_elapsed:.1f}s"

        final_text = (
            f"🎉 处理完成！\n"
            f"✅ 成功: {self.stats['success']} "
            f"❌ 失败: {self.stats['failed']} "
            f"⏭️ 跳过: {self.stats['skipped']} "
            f"🔄 重复: {self.stats['duplicates']}\n"
            f"⏱️ {total_time}"
        )

        # 检查progress_label是否存在且不为None
        if self.progress_label is not None:
            try:
                self.progress_label.config(text=final_text)
            except Exception as e:
                logging.warning(f"更新完成状态失败: {e}")
        else:
            logging.info(f"处理完成: {final_text}")

        # 检查progress_bar是否存在且不为None
        if self.progress_bar is not None:
            try:
                self.progress_bar['value'] = 100
            except Exception as e:
                logging.warning(f"更新进度条失败: {e}")

def process_files(df, target_base_folder, source_folder, small_pdf_folder, progress_bar, progress_label, progress_callback=None, folder_config=None):
    os.makedirs(target_base_folder, exist_ok=True)
    total_rows = len(df)

    # 加载文件夹命名配置
    if folder_config is None:
        folder_config = load_folder_config()

    # 预先统计总体信息用于命名（基于小标PDF数量）
    total_stats = calculate_total_stats(df, source_folder, small_pdf_folder)

    # 创建增强的进度跟踪器
    progress_tracker = EnhancedProgressTracker(progress_bar, progress_label, total_rows)
    progress_tracker.start_processing()

    i = 0
    processed_count = 0

    while i < total_rows:
        row = df.iloc[i]
        product_info = row.get('商品信息')
        attribute_set = row.get('属性集')

        # 跳过无效行
        if (not isinstance(product_info, str) or not product_info.strip()) and (not isinstance(attribute_set, str) or not attribute_set.strip()):
            progress_tracker.update_progress(
                processed_count,
                "跳过无效行",
                f"第{i+1}行数据无效，跳过处理",
                "skipped"
            )
            i += 1
            continue

        # 检查是否为多规格组起点
        if isinstance(product_info, str) and product_info.strip():
            # 提取SKC用于显示
            skc_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
            skc_display = skc_match.group(1) if skc_match else "未知SKC"

            progress_tracker.update_progress(
                processed_count,
                "分析商品组",
                f"正在分析SKC{skc_display}的规格组..."
            )

            # 新组起点
            group = [row]
            j = i + 1
            while j < total_rows:
                next_row = df.iloc[j]
                next_attr = next_row.get('属性集')
                next_prod = next_row.get('商品信息')
                # 合计行
                if any(isinstance(v, str) and '合计' in v for v in next_row):
                    # group为多规格组，合计行不用管
                    progress_tracker.update_progress(
                        processed_count,
                        "处理多规格组",
                        f"SKC{skc_display}包含{len(group)}个规格，开始逐个处理..."
                    )

                    for spec_idx, spec_row in enumerate(group, 1):
                        processed_count += 1
                        progress_tracker.update_progress(
                            processed_count,
                            f"处理规格{spec_idx}/{len(group)}",
                            f"SKC{skc_display}-规格{spec_idx}: {spec_row.get('属性集', '未知属性')[:30]}..."
                        )

                        # 继承首行的商品信息、SKC等，属性集用spec_row自己的
                        try:
                            process_single_spec(
                                spec_row,
                                product_info,  # 继承首行
                                target_base_folder,
                                source_folder,
                                small_pdf_folder,
                                progress_bar,
                                progress_label,
                                index=i+spec_idx-1,
                                spec_idx=spec_idx,
                                total_rows=total_rows,
                                progress_callback=progress_callback,
                                progress_tracker=progress_tracker,
                                folder_config=folder_config,
                                total_stats=total_stats
                            )
                            progress_tracker.stats['success'] += 1
                        except Exception as e:
                            logging.error(f"处理规格{spec_idx}失败: {e}")
                            progress_tracker.stats['failed'] += 1
                            progress_tracker.update_progress(
                                processed_count,
                                f"规格{spec_idx}处理失败",
                                f"错误: {str(e)[:50]}..."
                            )

                    i = j + 1
                    break
                # 组内行（商品信息为空但属性集有内容）
                elif (not isinstance(next_prod, str) or not next_prod.strip()) and isinstance(next_attr, str) and next_attr.strip():
                    group.append(next_row)
                    j += 1
                else:
                    # 不是本组，单规格处理
                    processed_count += 1
                    progress_tracker.update_progress(
                        processed_count,
                        "处理单规格",
                        f"SKC{skc_display}: {attribute_set[:30] if attribute_set else '未知属性'}..."
                    )

                    try:
                        process_single_spec(
                            row,
                            product_info,
                            target_base_folder,
                            source_folder,
                            small_pdf_folder,
                            progress_bar,
                            progress_label,
                            index=i,
                            spec_idx=1,
                            total_rows=total_rows,
                            progress_callback=progress_callback,
                            progress_tracker=progress_tracker,
                            folder_config=folder_config,
                            total_stats=total_stats
                        )
                        progress_tracker.stats['success'] += 1
                    except Exception as e:
                        logging.error(f"处理单规格失败: {e}")
                        progress_tracker.stats['failed'] += 1
                        progress_tracker.update_progress(
                            processed_count,
                            "单规格处理失败",
                            f"错误: {str(e)[:50]}..."
                        )

                    i += 1
                    break
            else:
                # 到结尾也没遇到合计，单规格处理
                processed_count += 1
                progress_tracker.update_progress(
                    processed_count,
                    "处理单规格(末尾)",
                    f"SKC{skc_display}: {attribute_set[:30] if attribute_set else '未知属性'}..."
                )

                try:
                    process_single_spec(
                        row,
                        product_info,
                        target_base_folder,
                        source_folder,
                        small_pdf_folder,
                        progress_bar,
                        progress_label,
                        index=i,
                        spec_idx=1,
                        total_rows=total_rows,
                        progress_callback=progress_callback,
                        progress_tracker=progress_tracker,
                        folder_config=folder_config,
                        total_stats=total_stats
                    )
                    progress_tracker.stats['success'] += 1
                except Exception as e:
                    logging.error(f"处理单规格失败: {e}")
                    progress_tracker.stats['failed'] += 1
                    progress_tracker.update_progress(
                        processed_count,
                        "单规格处理失败",
                        f"错误: {str(e)[:50]}..."
                    )

                i += 1
        else:
            # 不是新组起点，跳过
            progress_tracker.update_progress(
                processed_count,
                "跳过空行",
                f"第{i+1}行：非新组起点，跳过",
                "skipped"
            )
            i += 1

    # 处理完成
    progress_tracker.finish_processing()

    # 检查是否有配置输入，只有填了配置才需要修改总数
    date = folder_config.get("date", "").strip()
    time_period = folder_config.get("time_period", "").strip()
    operator = folder_config.get("operator", "").strip()
    shop_number = folder_config.get("shop_number", "").strip()

    has_config = any([date, time_period, operator, shop_number])

    if has_config:
        logging.info("检测到配置输入，开始数量统计...")

        # 统计各文件夹对应的拣货单数量
        folder_quantity_stats = calculate_folder_quantities_from_picking_list(target_base_folder, df, source_folder)

        if folder_quantity_stats:
            logging.info(f"✅ 找到 {len(folder_quantity_stats)} 个文件夹的统计数据，显示数量统计弹窗...")
            for folder_name, stats in folder_quantity_stats.items():
                logging.info(f"  📁 {folder_name}: {stats}")

            confirmed_stats = show_quantity_confirmation_dialog(folder_quantity_stats)

            if confirmed_stats is not None:
                # 用户确认了数量，更新文件夹名称中的"总X"部分
                logging.info("用户确认数量，开始更新文件夹名称...")
                update_folder_names_with_confirmed_quantities(target_base_folder, confirmed_stats)
                logging.info("数量统计和文件夹更新完成！")
            else:
                logging.info("用户取消了数量确认，保持原有文件夹名称")
        else:
            logging.warning("❌ 没有找到统计数据，跳过数量确认")
            logging.info("调试信息：检查目标文件夹内容...")
            if os.path.exists(target_base_folder):
                folder_contents = os.listdir(target_base_folder)
                logging.info(f"目标文件夹内容: {folder_contents}")
            else:
                logging.error(f"目标文件夹不存在: {target_base_folder}")
    else:
        logging.info("没有配置输入，跳过数量统计和弹窗")

def process_single_spec(row, product_info, target_base_folder, source_folder, small_pdf_folder, progress_bar, progress_label, index, spec_idx, total_rows, progress_callback=None, progress_tracker=None, folder_config=None, total_stats=None):
    # 这里就是原process_files主循环内的单规格处理逻辑
    # 用row['属性集']，用product_info（继承首行）
    # 文件夹名建议加_spec{spec_idx}避免重名
    # 其余逻辑同原来
    attribute_set = row.get('属性集')
    quantity = row.get('数量')

    # 加载文件夹命名配置
    if folder_config is None:
        folder_config = load_folder_config()

    # ========== 以下为原有单规格逻辑，attribute_set用row自己的，product_info用传入的 ===========
    # 提取 SKC 编号
    if progress_tracker:
        progress_tracker.update_progress(
            progress_tracker.current_item,
            "提取SKC编号",
            f"正在从商品信息中提取SKC编号..."
        )

    skc_number_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
    if not skc_number_match:
        logging.warning(f"第{index+2}行：未找到SKC编号，跳过")
        if progress_tracker:
            progress_tracker.update_progress(
                progress_tracker.current_item,
                "SKC提取失败",
                f"第{index+2}行：未找到有效的SKC编号",
                "failed"
            )
        return
    skc_base = skc_number_match.group(1)
    # 检查数据是否为空（使用更通用的方法）
    def is_empty_value(value):
        if value is None:
            return True
        if isinstance(value, str) and not value.strip():
            return True
        try:
            # 延迟导入pandas来检查NaN
            pd = lazy_import_pandas()
            if pd and pd.isna(value):
                return True
        except:
            pass
        return False

    if is_empty_value(quantity) or is_empty_value(attribute_set):
        logging.warning(f"第{index+2}行：缺少数量或属性集，跳过")
        if progress_tracker:
            progress_tracker.update_progress(
                progress_tracker.current_item,
                "数据缺失",
                f"第{index+2}行：缺少数量或属性集信息",
                "failed"
            )
        return

    if progress_tracker:
        progress_tracker.update_progress(
            progress_tracker.current_item,
            "识别尺寸",
            f"正在识别属性集: {attribute_set[:30]}..."
        )

    intended_size = get_size_folder(attribute_set)
    logging.info(f"第{index+2}行-规格{spec_idx}：属性集 '{attribute_set}' 识别为预期尺寸 '{intended_size}'")

    # 特殊调试：如果包含21.65和26.77，输出详细信息
    if attribute_set and ("21.65" in str(attribute_set) and "26.77" in str(attribute_set)):
        logging.info(f"🔍 围裙调试 - 原始属性集: '{attribute_set}'")
        logging.info(f"🔍 围裙调试 - 识别结果: '{intended_size}'")
        logging.info(f"🔍 围裙调试 - 期望结果: 'apron_55x68cm'")
        if intended_size != "apron_55x68cm":
            logging.warning(f"⚠️ 围裙识别失败！应该识别为 apron_55x68cm 但识别为 {intended_size}")

    # 特殊调试：如果包含two-side和18，输出详细信息
    if attribute_set and ("two-side" in str(attribute_set).lower() and "18" in str(attribute_set)):
        logging.info(f"🔍 双面抱枕调试 - 原始属性集: '{attribute_set}'")
        logging.info(f"🔍 双面抱枕调试 - 识别结果: '{intended_size}'")
        logging.info(f"🔍 双面抱枕调试 - 期望结果: 'two_sided_18x18inch'")
        if intended_size != "two_sided_18x18inch":
            logging.warning(f"⚠️ 双面抱枕识别失败！应该识别为 two_sided_18x18inch 但识别为 {intended_size}")
    # 在源文件夹中查找与SKC相关的图片文件，并根据文件名后缀分组
    if progress_tracker:
        progress_tracker.update_progress(
            progress_tracker.current_item,
            "查找图片文件",
            f"正在查找SKC{skc_base}的图片文件..."
        )

    image_groups = {
        "30x40cm": [], # 无特定后缀的默认为 30x40cm
        "40x40cm": [], # 文件名包含 -40x40.jpg
        "40x60cm": []  # 文件名包含 -40x60.jpg
    }
    all_related_images = []

    if os.path.isdir(source_folder):
        for filename in os.listdir(source_folder):
            file_path = os.path.join(source_folder, filename)
            # 检查文件名是否以 skc_base 开头，且以 .jpg 结尾 (忽略大小写)
            if os.path.isfile(file_path) and filename.lower().startswith(skc_base.lower()) and filename.lower().endswith('.jpg'):
                 all_related_images.append(file_path)
                 # 根据文件名后缀分组图片
                 if "-40x60.jpg" in filename.lower():
                      image_groups["40x60cm"].append(file_path)
                 elif "-40x40.jpg" in filename.lower():
                      image_groups["40x40cm"].append(file_path)
                 else:
                      # 没有特定尺寸后缀的图片
                      image_groups["30x40cm"].append(file_path)

    total_image_count = len(all_related_images)
    logging.info(f"第{index+2}行：找到 {total_image_count} 张与SKC '{skc_base}' 相关的图片")
    for size, paths in image_groups.items():
         if paths:
             logging.info(f"  - {size} 组找到 {len(paths)} 张图片")

    # 获取数量部分的字符串 (X套 或 X张) - 恢复原来的逻辑
    try:
        quantity_num = int(float(quantity))
        # 恢复原来的命名逻辑：基于图片数量判断
        quantity_subfolder_name = f"{quantity_num}套" if total_image_count > 1 else f"{quantity_num}张"
    except (ValueError, TypeError):
        logging.warning(f"第{index+2}行：数量 '{quantity}' 无效，跳过")
        return

    # 根据预期的尺寸和对应的图片数量构建主文件夹名称
    images_to_copy = [] # 需要复制的图片列表
    count_for_intended_size = 0

    # ====== 黑框/金框特殊命名逻辑 ======
    if intended_size == "black_30x40":
        size_for_image_match = "30x40cm"
        logging.info(f"第{index+2}行：属性集匹配 '11.8x15.7inch-H'，识别为黑框")
        if size_for_image_match in image_groups:
            images_to_copy = image_groups[size_for_image_match]
            count_for_intended_size = len(images_to_copy)
    elif intended_size == "gold_30x40":
        size_for_image_match = "30x40cm"
        logging.info(f"第{index+2}行：属性集匹配 'G-11.8x15.7inch'，识别为金框")
        if size_for_image_match in image_groups:
            images_to_copy = image_groups[size_for_image_match]
            count_for_intended_size = len(images_to_copy)
    # ====== 18x18inch 特殊命名逻辑 ======
    elif intended_size == "18x18inch":
        # 统一命名为正方形抱枕格式
        images_to_copy = all_related_images  # 复制所有相关图片
        count_for_intended_size = total_image_count
        logging.info(f"第{index+2}行：属性集匹配 '18x18inch'，识别为正方形抱枕")
    # ====== 新增特殊尺寸逻辑 ======
    elif intended_size == "12inch":
        # 12inch木板钟
        images_to_copy = all_related_images  # 复制所有相关图片
        count_for_intended_size = total_image_count
        logging.info(f"第{index+2}行：属性集匹配 '12inch'，识别为木板钟")
    elif intended_size == "apron_55x68cm":
        # 围裙
        images_to_copy = all_related_images  # 复制所有相关图片
        count_for_intended_size = total_image_count
        logging.info(f"第{index+2}行：属性集匹配 '21.65x26.77inch'，识别为围裙55x68cm")
    elif intended_size == "two_sided_18x18inch":
        # 双面抱枕
        images_to_copy = all_related_images  # 复制所有相关图片
        count_for_intended_size = total_image_count
        logging.info(f"第{index+2}行：属性集匹配 'Two-sided 18x18inch'，识别为双面抱枕45x45cm")
    # ====== 原有逻辑 ======
    elif intended_size != "unknown" and intended_size in image_groups:
        # 如果识别到标准尺寸 - 保持6.13的核心逻辑
        count_for_intended_size = len(image_groups[intended_size])
        if count_for_intended_size > 0:
             # 如果该尺寸分组有图片，则确定要复制的图片
             images_to_copy = image_groups[intended_size] # 复制该尺寸分组的图片
             logging.info(f"第{index+2}行：根据预期尺寸 '{intended_size}' 和图片数量 {count_for_intended_size} 构建文件夹名")
        else:
             # 如果该尺寸分组没有图片，记录警告并使用所有图片
             logging.warning(f"第{index+2}行：预期尺寸 '{intended_size}' 分组没有找到图片。")
             if all_related_images:
                  images_to_copy = all_related_images
                  count_for_intended_size = total_image_count
                  logging.warning(f"第{index+2}行：使用总图片数量 {total_image_count}，复制所有相关图片。")

    elif all_related_images: # 如果没有识别到标准尺寸，但有相关图片
         # 使用总图片数量
         images_to_copy = all_related_images # 复制所有相关图片
         count_for_intended_size = total_image_count
         logging.warning(f"第{index+2}行：未识别到标准尺寸，使用总图片数量 {total_image_count}，复制所有相关图片。")
    else:
         # 没有找到任何相关图片
         logging.warning(f"第{index+2}行：未找到与SKC '{skc_base}' 相关的任何图片，跳过文件复制和文件夹创建")
         return # 跳过此行的后续处理

    # 生成文件夹名称 - 支持简单命名和复杂命名
    main_folder_name = generate_folder_name(intended_size, count_for_intended_size, folder_config, total_stats, False, attribute_set)
    logging.info(f"第{index+2}行：生成文件夹名称: {main_folder_name}")

    # 构建完整的目标路径（确保路径格式正确）
    main_folder_path = os.path.normpath(os.path.join(target_base_folder, main_folder_name))
    quantity_folder_path = os.path.normpath(os.path.join(main_folder_path, quantity_subfolder_name))
    images_target_folder = quantity_folder_path # 图片直接放到数量文件夹下
    small_label_target_folder = os.path.normpath(os.path.join(quantity_folder_path, '小标')) # 小标PDF放到数量文件夹下的小标子文件夹

    # 记录路径信息用于调试
    logging.info(f"第{index+2}行：路径构建完成")
    logging.info(f"  主文件夹路径: {main_folder_path}")
    logging.info(f"  数量文件夹路径: {quantity_folder_path}")
    logging.info(f"  小标文件夹路径: {small_label_target_folder}")

    # 创建必要的文件夹 (确保按层级创建)
    if progress_tracker:
        progress_tracker.update_progress(
            progress_tracker.current_item,
            "创建文件夹",
            f"正在创建文件夹: {main_folder_name}"
        )

    os.makedirs(main_folder_path, exist_ok=True)
    os.makedirs(quantity_folder_path, exist_ok=True)
    os.makedirs(small_label_target_folder, exist_ok=True)

    # 复制筛选后的图片到目标文件夹
    if not images_to_copy:
         logging.warning(f"第{index+2}行：没有图片需要复制到 '{images_target_folder}'")
         if progress_tracker:
             progress_tracker.update_progress(
                 progress_tracker.current_item,
                 "无图片复制",
                 f"SKC{skc_base}没有找到需要复制的图片"
             )
         # 即使没有图片，也要复制小标PDF
    else:
        if progress_tracker:
            progress_tracker.update_progress(
                progress_tracker.current_item,
                "复制图片",
                f"正在复制{len(images_to_copy)}张图片..."
            )

        for i, img_path in enumerate(images_to_copy, 1):
            if progress_tracker:
                progress_tracker.update_progress(
                    progress_tracker.current_item,
                    f"复制图片 {i}/{len(images_to_copy)}",
                    f"正在复制: {os.path.basename(img_path)}"
                )

            target_img_filename = os.path.basename(img_path)
            target_img_path = os.path.join(images_target_folder, target_img_filename)
            if os.path.exists(img_path):
                 if not os.path.exists(target_img_path):
                     try:
                         shutil.copy(img_path, target_img_path)
                         logging.info(f"成功复制图片: {img_path} 到 {target_img_path}")
                     except Exception as e:
                         logging.error(f"复制图片失败：{img_path} 到 {target_img_path}，原因：{e}")
                 else:
                     logging.warning(f"目标图片已存在，跳过：{target_img_path}")
            else:
                 logging.warning(f"图片未找到：{img_path}")

    # 查找并复制对应的小标PDF文件 (匹配 9-11 位纯数字文件名)
    if progress_tracker:
        progress_tracker.update_progress(
            progress_tracker.current_item,
            "查找小标PDF",
            f"正在查找SKC{skc_base}的小标PDF文件..."
        )

    found_and_copied_small_pdf = False
    if os.path.isdir(small_pdf_folder):
         for filename in os.listdir(small_pdf_folder):
             # 检查文件名是否是纯数字 (9-11位) 加上 .pdf
             match = re.fullmatch(r'\d{9,11}\.pdf', filename.lower())
             if match:
                  file_skc = filename.split('.')[0] # 获取文件名的数字部分
                  # 如果提取的SKC与文件名完全匹配，或者提取的SKC是文件名的9位前缀
                  if skc_base == file_skc or (len(skc_base) == 9 and file_skc.startswith(skc_base) and len(file_skc) == 11):
                       if progress_tracker:
                           progress_tracker.update_progress(
                               progress_tracker.current_item,
                               "复制小标PDF",
                               f"正在复制小标PDF: {filename}"
                           )

                       source_small_pdf_path = os.path.join(small_pdf_folder, filename)
                       copy_file(source_small_pdf_path, small_label_target_folder, 'pdf')
                       found_and_copied_small_pdf = True
                       logging.info(f"第{index+2}行：成功复制小标PDF文件: {filename} (匹配SKC: {skc_base})")
                       break # 找到一个匹配的就停止查找

    if not found_and_copied_small_pdf:
         logging.warning(f"第{index+2}行：未找到与SKC '{skc_base}' 匹配的小标PDF文件进行复制")
         if progress_tracker:
             progress_tracker.update_progress(
                 progress_tracker.current_item,
                 "小标PDF缺失",
                 f"SKC{skc_base}未找到对应的小标PDF文件"
             )

    # 更新进度 - 这部分放在外层循环，更新总进度
    progress_value = (index + 1) / total_rows * 100

    # 优先使用progress_callback（现代化UI）
    if progress_callback:
        try:
            progress_callback(progress_value)
            logging.debug(f"通过callback更新进度: {progress_value:.2f}%")
        except Exception as e:
            logging.warning(f"通过callback更新进度失败: {e}")
    else:
        # 兼容不同类型的进度条（传统UI）
        try:
            # 尝试现代化UI的方式（ttkbootstrap）
            if hasattr(progress_bar, 'variable') and progress_bar.variable:
                progress_bar.variable.set(progress_value)
                logging.debug(f"通过variable更新进度: {progress_value:.2f}%")
            # 尝试字典式访问（传统UI）
            elif hasattr(progress_bar, '__setitem__'):
                progress_bar['value'] = progress_value
                logging.debug(f"通过字典更新进度: {progress_value:.2f}%")
            # 尝试直接设置value属性
            elif hasattr(progress_bar, 'value'):
                progress_bar.value = progress_value
                logging.debug(f"通过属性更新进度: {progress_value:.2f}%")

            # 更新进度条显示
            if hasattr(progress_bar, 'update'):
                progress_bar.update()

        except Exception as e:
            logging.warning(f"更新进度条失败: {e}")

    # 更新进度标签
    if progress_label:
        try:
            progress_label.config(text=f"{progress_value:.2f}%")
        except Exception as e:
            logging.warning(f"更新进度标签失败: {e}")

def clean_folder_name(name):
    """
    清理文件夹名字中的非法字符
    """
    return re.sub(r'[<>:"/\\|?*\n\r]', '', name).strip()

def calculate_individual_stats_from_folders(target_base_folder):
    """
    从已创建的文件夹中统计各属性集的独立信息

    新的统计逻辑：
    - 检查各个属性集下面的数量文件夹
    - 2张文件夹里1个jpg = 2张，3套文件夹里1个jpg = 3套
    - 各属性集独立统计，不全局汇总
    - 单pc显示张，多pc显示套

    Args:
        target_base_folder: 目标基础文件夹路径

    Returns:
        dict: 各属性集的统计信息
    """
    attribute_stats = {}

    if not os.path.exists(target_base_folder):
        return {}

    # 遍历所有主文件夹（属性集文件夹）
    for main_folder_name in os.listdir(target_base_folder):
        main_folder_path = os.path.join(target_base_folder, main_folder_name)

        if not os.path.isdir(main_folder_path):
            continue

        # 跳过特殊文件夹
        if main_folder_name in ['小标PDF', '大标']:
            continue

        attribute_total = 0
        is_multi_pc = False  # 是否是多pc属性集

        logging.info(f"检查属性集文件夹: {main_folder_name}")

        # 遍历该属性集下的数量文件夹
        for quantity_folder_name in os.listdir(main_folder_path):
            quantity_folder_path = os.path.join(main_folder_path, quantity_folder_name)

            if not os.path.isdir(quantity_folder_path):
                continue

            # 解析数量文件夹名称（如"2张"、"3套"）
            quantity_match = re.match(r'(\d+)[张套]', quantity_folder_name)
            if not quantity_match:
                continue

            quantity_value = int(quantity_match.group(1))

            # 检查是否是"套"文件夹，如果是则标记为多pc
            if quantity_folder_name.endswith('套'):
                is_multi_pc = True

            # 统计该数量文件夹中的jpg文件数量（只统计直接在数量文件夹下的jpg）
            jpg_count = 0
            for item in os.listdir(quantity_folder_path):
                item_path = os.path.join(quantity_folder_path, item)
                if os.path.isfile(item_path) and item.lower().endswith('.jpg'):
                    jpg_count += 1

            # 计算该数量文件夹的总数量
            folder_total = jpg_count * quantity_value
            attribute_total += folder_total

            logging.info(f"  - {quantity_folder_name}: {jpg_count}个jpg × {quantity_value} = {folder_total}")

        # 确定该属性集的显示单位
        unit = "套" if is_multi_pc else "张"

        attribute_stats[main_folder_name] = {
            "total": attribute_total,
            "unit": unit,
            "is_multi_pc": is_multi_pc
        }

        logging.info(f"  属性集 '{main_folder_name}' 总计: {attribute_total}{unit}")

    return attribute_stats

def calculate_total_stats(df, source_folder, small_pdf_folder=None):
    """
    新的统计逻辑：
    - SKC匹配小标拣货单数量
    - 相同SKC累加（如果有两个一样的SKC说明是2）
    - 按属性集分组统计
    """
    # 按属性集分组统计
    attribute_stats = {}

    # 统计拣货单中各SKC的数量（相同SKC累加）
    skc_counts_in_df = {}
    for _, row in df.iterrows():
        product_info = row.get('商品信息')
        attribute_set = row.get('属性集')

        # 跳过无效行
        if (not isinstance(product_info, str) or not product_info.strip()) and (not isinstance(attribute_set, str) or not attribute_set.strip()):
            continue

        # 提取SKC编号
        skc_number_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if not skc_number_match:
            continue

        skc_base = skc_number_match.group(1)

        # 统计拣货单中SKC出现次数
        if skc_base not in skc_counts_in_df:
            skc_counts_in_df[skc_base] = 0
        skc_counts_in_df[skc_base] += 1

        logging.info(f"拣货单中SKC {skc_base} 累计出现: {skc_counts_in_df[skc_base]} 次")

    # 按属性集分组，统计各属性集下面的SKC数量
    for _, row in df.iterrows():
        product_info = row.get('商品信息')
        attribute_set = row.get('属性集')

        # 跳过无效行
        if (not isinstance(product_info, str) or not product_info.strip()) and (not isinstance(attribute_set, str) or not attribute_set.strip()):
            continue

        # 提取SKC编号
        skc_number_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if not skc_number_match:
            continue

        skc_base = skc_number_match.group(1)

        # 检查该SKC是否有对应的图片文件
        has_images = False
        pc_count = 1  # 默认单pc
        if os.path.isdir(source_folder):
            jpg_count, pc_count = count_images_and_pc(source_folder, skc_base)
            has_images = jpg_count > 0

        if has_images:
            # 按属性集分组
            if attribute_set not in attribute_stats:
                attribute_stats[attribute_set] = {
                    'total_quantity': 0,
                    'is_multi_pc': False,
                    'skc_count': 0,
                    'processed_skcs': set()  # 记录已处理的SKC，避免重复统计
                }

            # 避免同一个SKC在同一个属性集中重复统计
            if skc_base not in attribute_stats[attribute_set]['processed_skcs']:
                # 使用拣货单中该SKC的累计数量
                skc_quantity = skc_counts_in_df.get(skc_base, 0)

                if skc_quantity > 0:
                    # 累加该属性集的数量（基于拣货单中SKC数量）
                    attribute_stats[attribute_set]['total_quantity'] += skc_quantity
                    attribute_stats[attribute_set]['skc_count'] += 1
                    attribute_stats[attribute_set]['processed_skcs'].add(skc_base)

                    # 单pc/多pc判断：基于pc数判断
                    if pc_count > 1:
                        attribute_stats[attribute_set]['is_multi_pc'] = True
                        logging.info(f"属性集 '{attribute_set}' 标记为多pc (SKC {skc_base}: {pc_count}pc)")

                    logging.info(f"属性集 '{attribute_set}': SKC {skc_base}({pc_count}pc) 拣货单数量+{skc_quantity}")

    # 输出各属性集的统计结果
    total_quantity = 0
    total_specs = len(attribute_stats)

    for attr_set, stats in attribute_stats.items():
        quantity = stats['total_quantity']
        unit = "套" if stats['is_multi_pc'] else "张"
        total_quantity += quantity
        logging.info(f"属性集 '{attr_set}': 总{quantity}{unit} ({stats['skc_count']}个SKC) - 基于拣货单SKC数量统计")

    logging.info(f"全局统计: {total_quantity}，{total_specs}个属性集")

    return {
        "total_images": total_quantity,
        "total_specs": total_specs,
        "attribute_stats": attribute_stats  # 返回各属性集的详细统计
    }

def count_images_and_pc(source_folder, skc_base):
    """
    统计SKC的图片数量和pc数

    Args:
        source_folder: 源文件夹路径
        skc_base: SKC编号

    Returns:
        tuple: (图片数量, pc数)
    """
    if not os.path.isdir(source_folder):
        return 0, 1

    # 收集所有相关图片
    related_images = []
    pc_suffixes = set()

    for filename in os.listdir(source_folder):
        file_path = os.path.join(source_folder, filename)
        if (os.path.isfile(file_path) and
            filename.lower().startswith(skc_base.lower()) and
            filename.lower().endswith('.jpg')):

            related_images.append(filename)

            # 检查是否有pc后缀（如-1, -2, -3）
            pc_match = re.search(r'-(\d+)\.jpg$', filename.lower())
            if pc_match:
                pc_suffixes.add(int(pc_match.group(1)))

    jpg_count = len(related_images)

    # 确定pc数：如果有pc后缀，pc数=最大后缀数；否则为1pc
    if pc_suffixes:
        pc_count = max(pc_suffixes)
    else:
        pc_count = 1

    logging.info(f"SKC {skc_base}: 找到{jpg_count}张图片，pc后缀: {sorted(pc_suffixes) if pc_suffixes else '无'}，判定为{pc_count}pc")

    return jpg_count, pc_count

def update_folder_names_with_individual_stats(target_base_folder, individual_stats, folder_config):
    """
    使用各属性集独立统计的数据更新文件夹名称

    Args:
        target_base_folder: 目标基础文件夹路径
        individual_stats: 各属性集的独立统计数据
        folder_config: 文件夹配置
    """
    if not os.path.exists(target_base_folder):
        return

    # 检查是否需要使用复杂命名
    date = folder_config.get("date", "").strip()
    time_period = folder_config.get("time_period", "").strip()
    operator = folder_config.get("operator", "").strip()
    shop_number = folder_config.get("shop_number", "").strip()

    # 如果配置为空，不需要更新文件夹名称
    if not any([date, time_period, operator, shop_number]):
        logging.info("配置为空，不更新文件夹名称")
        return

    # 遍历所有主文件夹，更新名称
    for main_folder_name in os.listdir(target_base_folder):
        main_folder_path = os.path.join(target_base_folder, main_folder_name)

        if not os.path.isdir(main_folder_path):
            continue

        # 跳过特殊文件夹
        if main_folder_name in ['小标PDF', '大标']:
            continue

        # 检查是否是需要更新的文件夹格式
        if "特级JIT" in main_folder_name and main_folder_name in individual_stats:
            # 已经是新格式，更新总数部分
            stats = individual_stats[main_folder_name]
            new_folder_name = update_individual_total_in_folder_name(main_folder_name, stats)
            if new_folder_name != main_folder_name:
                new_folder_path = os.path.join(target_base_folder, new_folder_name)
                try:
                    os.rename(main_folder_path, new_folder_path)
                    logging.info(f"更新文件夹名称: {main_folder_name} -> {new_folder_name}")
                except Exception as e:
                    logging.error(f"重命名文件夹失败: {e}")

def update_individual_total_in_folder_name(folder_name, stats):
    """
    更新文件夹名称中的总数部分（基于该属性集的独立统计）

    Args:
        folder_name: 原文件夹名称
        stats: 该属性集的统计数据 {"total": 数量, "unit": "张"/"套", "is_multi_pc": bool}

    Returns:
        str: 更新后的文件夹名称
    """
    total_count = stats.get("total", 0)
    unit = stats.get("unit", "张")

    # 新的总数显示
    new_total_display = f"总{total_count}{unit}"

    # 使用正则表达式替换总数部分
    pattern = r'\(总\d+[张套]\)'
    new_folder_name = re.sub(pattern, f'({new_total_display})', folder_name)

    return new_folder_name

def generate_folder_name(intended_size, image_count, config, total_stats=None, use_simple_naming=False, attribute_set=None):
    """
    生成文件夹命名格式，支持简单命名和复杂命名

    Args:
        intended_size: 识别的尺寸 (如 "30x40cm", "18x18inch", "black_30x40", "gold_30x40")
        image_count: 当前规格的图片数量
        config: 配置信息 (日期、时间段、操作员、店铺号)
        total_stats: 总统计信息 {"total_images": 总图片数, "total_specs": 总规格数, "attribute_stats": 各属性集统计}
        use_simple_naming: 是否使用简单命名（原来的命名方式）
        attribute_set: 当前属性集名称

    Returns:
        str: 格式化的文件夹名称
    """
    # 检查是否所有配置项都为空或默认值，如果是则使用简单命名
    date = config.get("date", "").strip()
    time_period = config.get("time_period", "").strip()
    operator = config.get("operator", "").strip()
    shop_number = config.get("shop_number", "").strip()

    # 定义需要使用新命名格式的规格
    special_naming_specs = {
        "18x18inch": True,
        "black_30x40": True,
        "gold_30x40": True,
        "30x40cm": True,
        "40x60cm": True,
        "12inch": True,  # 新增：12inch木板钟
        "apron_55x68cm": True,  # 新增：围裙
        "two_sided_18x18inch": True  # 新增：双面抱枕
    }

    # 检查当前规格是否需要使用新命名格式
    needs_special_naming = False
    if intended_size in special_naming_specs:
        # 对于特定规格，检查数量是否匹配需要新命名的情况
        if intended_size == "18x18inch" and image_count == 1:
            needs_special_naming = True
        elif intended_size == "black_30x40" and image_count == 1:
            needs_special_naming = True
        elif intended_size == "gold_30x40" and image_count == 1:
            needs_special_naming = True
        elif intended_size == "30x40cm" and image_count in [1, 2, 3, 4]:  # 添加4pc支持
            needs_special_naming = True
        elif intended_size == "40x60cm" and image_count in [1, 2, 3, 4]:  # 添加4pc支持
            needs_special_naming = True
        elif intended_size == "12inch":  # 12inch木板钟
            needs_special_naming = True
        elif intended_size == "apron_55x68cm":  # 围裙
            needs_special_naming = True
        elif intended_size == "two_sided_18x18inch":  # 双面抱枕
            needs_special_naming = True

    # 特殊处理：强制4pc30x40cm和4pc40x60cm使用复杂命名
    if attribute_set and isinstance(attribute_set, str):
        if (attribute_set.strip() == "4pc30x40cm" and intended_size == "30x40cm" and image_count == 4) or \
           (attribute_set.strip() == "4pc40x60cm" and intended_size == "40x60cm" and image_count == 4):
            needs_special_naming = True
            logging.info(f"强制使用复杂命名: {attribute_set} -> {intended_size} {image_count}pc")

    # 如果不需要特殊命名，或者配置为空，或者明确要求使用简单命名，则使用6.13的简单命名方式
    if use_simple_naming or not needs_special_naming or not any([date, time_period, operator, shop_number]):
        # 6.13的简单命名方式
        if intended_size == "18x18inch":
            return f"{image_count}pc18x18inch"
        elif intended_size == "black_30x40":
            return "黑框1pc30x40cm"
        elif intended_size == "gold_30x40":
            return "金框1pc30x40"
        elif intended_size == "12inch":
            return f"{image_count}pc12inch"
        elif intended_size == "apron_55x68cm":
            return f"{image_count}pc围裙55x68cm"
        elif intended_size == "two_sided_18x18inch":
            return f"{image_count}pc双面抱枕45x45cm"
        elif intended_size in ["30x40cm", "40x60cm", "40x40cm"]:
            return f"{image_count}pc{intended_size}"
        else:
            # 如果intended_size是unknown，使用清理后的属性集名称
            if intended_size == "unknown" and attribute_set:
                cleaned_attribute_set = clean_folder_name(attribute_set)
                return f"{image_count}pc{cleaned_attribute_set}"
            else:
                size_display = intended_size if intended_size != "unknown" else "未知尺寸"
                return f"{image_count}pc{size_display}"

    # 使用默认值填充空的配置项
    if not date:
        date = "4-15"
    if not time_period:
        time_period = "上午"
    if not operator:
        operator = "彭于晏"
    if not shop_number:
        shop_number = "店铺号"

    # 总数显示部分先留空，等弹窗确认后再填入
    total_display = "总X"
    logging.info(f"文件夹创建时总数部分留空: {total_display}")

    # 根据不同的尺寸类型生成复杂命名（只对特定规格）
    if intended_size == "18x18inch" and image_count == 1:
        # 正方形抱枕
        folder_name = f"特级JIT-{date}-{time_period}自送-{image_count}pc正方形抱枕45x45cm-单面-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "black_30x40" and image_count == 1:
        # 黑框帆布画
        folder_name = f"特级JIT-{date}-{time_period}自送-{image_count}pc黑框帆布画30x40cm-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "gold_30x40" and image_count == 1:
        # 金框帆布画
        folder_name = f"特级JIT-{date}-{time_period}自送-{image_count}pc金框帆布画30x40cm-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "12inch":
        # 12inch木板钟
        folder_name = f"特级JIT-{date}-{time_period}自送-木板钟12inch-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "apron_55x68cm":
        # 围裙 - 固定使用1pc
        folder_name = f"特级JIT-{date}-{time_period}自送-1pc围裙55x68cm-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "two_sided_18x18inch":
        # 双面抱枕
        folder_name = f"特级JIT-{date}-{time_period}自送-1pc正方形抱枕45x45cm-双面-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "30x40cm" and image_count in [1, 2, 3, 4]:
        # 木框帆布画30x40cm
        folder_name = f"特级JIT-{date}-{time_period}自送-{image_count}pc木框帆布画30x40cm-{operator}-俞志敏组-({total_display})-{shop_number}"
    elif intended_size == "40x60cm" and image_count in [1, 2, 3, 4]:
        # 木框帆布画40x60cm
        folder_name = f"特级JIT-{date}-{time_period}自送-{image_count}pc木框帆布画40x60cm-{operator}-俞志敏组-({total_display})-{shop_number}"
    else:
        # 其他情况，使用原来的简单命名
        if intended_size == "18x18inch":
            return f"{image_count}pc18x18inch"
        elif intended_size == "black_30x40":
            return "黑框1pc30x40cm"
        elif intended_size == "gold_30x40":
            return "金框1pc30x40"
        elif intended_size in ["30x40cm", "40x60cm", "40x40cm"]:
            return f"{image_count}pc{intended_size}"
        else:
            # 如果intended_size是unknown，使用原始属性集名称
            if intended_size == "unknown" and attribute_set:
                # 清理换行符和多余空格
                cleaned_attribute_set = re.sub(r'[\n\r]+', ' ', attribute_set).strip()
                return cleaned_attribute_set  # 使用清理后的原始属性集名称
            else:
                size_display = intended_size if intended_size != "unknown" else "未知尺寸"
                return f"{image_count}pc{size_display}"

    return clean_folder_name(folder_name)

# 拆分PDF文件并根据内容重命名
def split_pdf_rename(pdf_path, output_dir, log_callback=None):
    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        return

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    try:
        reader = PyPDF2.PdfReader(pdf_path)
        num_pages = len(reader.pages)
        for page_num in range(num_pages):
            try:
                # 尝试两种方法提取文本
                # 方法1：使用PyPDF2直接提取
                page = reader.pages[page_num]
                text1 = page.extract_text()

                # 方法2：使用pdfminer提取
                text2 = extract_text_from_page(pdf_path, page_num)

                # 合并两种方法的文本
                combined_text = text1 + "\n" + text2

                # 添加调试信息
                logging.info(f"PDF第{page_num + 1}页提取的文本内容: {combined_text[:200]}...")  # 只显示前200个字符

                # 更宽松的正则表达式匹配
                skc_patterns = [
                    r"SKC[:：]?\s*(\d{9,11})",  # SKC: 或 SKC：后跟9-11位数字
                    r"SKC\s*(\d{9,11})",       # SKC 后跟数字
                    r"(\d{9,11})",             # 直接匹配9-11位数字
                ]

                pc_patterns = [
                    r"PC[:：]?\s*(\d{10,15})",  # PC: 或 PC：后跟10-15位数字（完整PC码）
                    r"PC\s*(\d{10,15})",        # PC 后跟10-15位数字
                    r"pc[:：]?\s*(\d{10,15})",  # 小写pc后跟10-15位数字
                    r"PC[:：]?\s*(\d+)",        # 兜底：PC: 或 PC：后跟任意数字
                    r"PC\s*(\d+)",              # 兜底：PC 后跟任意数字
                    r"pc[:：]?\s*(\d+)",        # 兜底：小写pc后跟任意数字
                ]

                count_patterns = [
                    r"(\d+)\s*件",             # 数字+件
                    r"(\d+)\s*个",             # 数字+个
                    r"数量[:：]?\s*(\d+)",      # 数量:数字
                    r"qty[:：]?\s*(\d+)",      # qty:数字（英文）
                ]

                # 尝试匹配SKC
                skc = "UnknownSKC"
                for pattern in skc_patterns:
                    match = re.search(pattern, combined_text, re.IGNORECASE)
                    if match:
                        skc = match.group(1)
                        break

                # 尝试匹配PC
                pc = "UnknownPC"
                for i, pattern in enumerate(pc_patterns):
                    match = re.search(pattern, combined_text, re.IGNORECASE)
                    if match:
                        pc = match.group(1)  # 只要数字，不要PC前缀
                        logging.info(f"PC匹配成功: 模式{i+1} '{pattern}' 提取到 '{pc}'")
                        break
                else:
                    logging.warning(f"PC匹配失败，文本内容: {combined_text}")
                    # 尝试查找所有可能的数字序列
                    all_numbers = re.findall(r'\d{6,}', combined_text)
                    if all_numbers:
                        logging.info(f"文本中找到的长数字序列: {all_numbers}")
                        # 选择最长的数字序列作为PC码
                        pc = max(all_numbers, key=len)
                        logging.info(f"选择最长数字序列作为PC码: {pc}")

                # 尝试匹配件数
                count = "1"
                for pattern in count_patterns:
                    match = re.search(pattern, combined_text, re.IGNORECASE)
                    if match:
                        count = match.group(1)
                        break

                writer = PyPDF2.PdfWriter()
                writer.add_page(page)
                # 保持原来的命名格式：SKC编号_PC编号_件数_页码.pdf
                output_filename = f"{skc}_{pc}_{count}_{page_num + 1}.pdf"
                output_path = os.path.join(output_dir, output_filename)
                with open(output_path, "wb") as outfile:
                    writer.write(outfile)

            except Exception as e:
                if log_callback:
                    log_callback(f"处理第{page_num + 1}页时发生错误: {e}")
                else:
                    print(f"处理第{page_num + 1}页时发生错误: {e}")
    except Exception as e:
        if log_callback:
            log_callback(f"读取PDF文件时发生错误: {e}")
        else:
            print(f"读取PDF文件时发生错误: {e}")

def extract_text_from_page(pdf_path, page_num):
    # 延迟导入pdfminer
    pdfminer_modules = lazy_import_pdfminer()
    if not pdfminer_modules:
        return ""

    StringIO = pdfminer_modules['StringIO']
    PDFParser = pdfminer_modules['PDFParser']
    PDFDocument = pdfminer_modules['PDFDocument']
    PDFResourceManager = pdfminer_modules['PDFResourceManager']
    TextConverter = pdfminer_modules['TextConverter']
    LAParams = pdfminer_modules['LAParams']
    PDFPageInterpreter = pdfminer_modules['PDFPageInterpreter']
    PDFPage = pdfminer_modules['PDFPage']

    output_string = StringIO()
    with open(pdf_path, 'rb') as in_file:
        parser = PDFParser(in_file)
        doc = PDFDocument(parser)
        rsrcmgr = PDFResourceManager()
        device = TextConverter(rsrcmgr, output_string, laparams=LAParams())
        interpreter = PDFPageInterpreter(rsrcmgr, device)
        for i, page in enumerate(PDFPage.create_pages(doc)):
            if i == page_num:
                interpreter.process_page(page)
                break
    return output_string.getvalue()

def move_big_labels_to_folders(df, target_base_folder, big_pdf_folder):
    """
    根据SKC匹配大标PDF文件，并移动到对应的属性集文件夹中
    优化版本：加强错误处理和文件完整性检查
    """
    logging.info("开始处理大标文件移动...")

    # 获取所有大标PDF文件
    big_pdf_files = {}
    if os.path.exists(big_pdf_folder):
        for filename in os.listdir(big_pdf_folder):
            if filename.lower().endswith('.pdf'):
                # 从文件名中提取SKC (格式: SKC_PC_Count_PageNum.pdf)
                parts = filename.split('_')
                if len(parts) >= 1:
                    skc = parts[0]
                    if skc.isdigit():
                        if skc not in big_pdf_files:
                            big_pdf_files[skc] = []
                        big_pdf_files[skc].append(filename)

    logging.info(f"找到大标PDF文件，涉及SKC: {list(big_pdf_files.keys())}")

    # 按属性集分组处理，避免重复处理
    attribute_set_skcs = {}

    for _, row in df.iterrows():
        product_info = row.get('商品信息')
        attribute_set = row.get('属性集', '未知属性集')

        # 跳过无效行
        if not isinstance(product_info, str) or not product_info.strip():
            continue

        # 提取SKC编号
        skc_number_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if not skc_number_match:
            continue

        skc_base = skc_number_match.group(1)

        # 检查是否有对应的大标PDF文件
        if skc_base not in big_pdf_files:
            logging.warning(f"SKC {skc_base} 没有找到对应的大标PDF文件")
            continue

        # 按属性集分组
        if attribute_set not in attribute_set_skcs:
            attribute_set_skcs[attribute_set] = set()
        attribute_set_skcs[attribute_set].add(skc_base)

    logging.info(f"按属性集分组完成，共 {len(attribute_set_skcs)} 个属性集")

    # 为每个属性集处理大标PDF
    for attribute_set, skcs in attribute_set_skcs.items():
        logging.info(f"处理属性集: {attribute_set}，包含 {len(skcs)} 个SKC")

        # 查找属性集对应的文件夹
        attribute_folders = []
        for skc in skcs:
            folders = find_attribute_folders(target_base_folder, skc)
            attribute_folders.extend(folders)

        # 去重
        attribute_folders = list(set(attribute_folders))

        if not attribute_folders:
            logging.warning(f"属性集 {attribute_set} 没有找到对应的文件夹")
            continue

        # 收集该属性集的所有大标PDF文件
        all_big_pdf_files = []
        for skc in skcs:
            if skc in big_pdf_files:
                all_big_pdf_files.extend(big_pdf_files[skc])

        # 为每个属性集文件夹处理大标PDF
        for attr_folder_path in attribute_folders:
            process_big_labels_for_folder_optimized(attr_folder_path, big_pdf_folder, all_big_pdf_files, list(skcs))

    logging.info("大标文件移动处理完成")

def process_big_labels_for_folder_optimized(attr_folder_path, big_pdf_folder, all_big_pdf_files, skcs):
    """
    优化版本：为指定的属性集文件夹处理大标PDF文件
    加强文件完整性检查和错误处理
    """
    logging.info(f"处理属性集文件夹: {attr_folder_path} (包含SKC: {skcs})")

    # 查找数量文件夹（如"1套"、"2张"等）
    quantity_folders = []
    try:
        for item in os.listdir(attr_folder_path):
            item_path = os.path.join(attr_folder_path, item)
            if os.path.isdir(item_path) and (item.endswith('套') or item.endswith('张')):
                quantity_folders.append(item_path)
    except Exception as e:
        logging.error(f"读取属性集文件夹失败: {attr_folder_path}，原因: {e}")
        return

    if not quantity_folders:
        logging.warning(f"在 {attr_folder_path} 中未找到数量文件夹")
        return

    # 1. 在属性集文件夹中创建大标文件夹（与数量文件夹并列）
    big_label_folder = os.path.join(attr_folder_path, '大标')
    try:
        os.makedirs(big_label_folder, exist_ok=True)
        logging.info(f"创建大标文件夹: {big_label_folder}")
    except Exception as e:
        logging.error(f"创建大标文件夹失败: {big_label_folder}，原因: {e}")
        return

    # 2. 复制所有对应的大标PDF文件到大标文件夹
    copied_files = []
    failed_copies = []

    for pdf_file in all_big_pdf_files:
        source_path = os.path.join(big_pdf_folder, pdf_file)
        target_path = os.path.join(big_label_folder, pdf_file)

        try:
            if os.path.exists(source_path):
                # 检查源文件大小
                source_size = os.path.getsize(source_path)
                if source_size == 0:
                    logging.warning(f"源大标PDF文件为空: {source_path}")
                    continue

                # 如果目标文件已存在，检查大小是否一致
                if os.path.exists(target_path):
                    target_size = os.path.getsize(target_path)
                    if target_size == source_size:
                        copied_files.append(pdf_file)
                        logging.info(f"大标PDF已存在且大小一致，跳过: {pdf_file}")
                        continue
                    else:
                        logging.warning(f"目标文件大小不一致，重新复制: {pdf_file}")
                        os.remove(target_path)

                # 复制文件
                shutil.copy2(source_path, target_path)  # 使用copy2保留元数据

                # 验证复制结果
                if os.path.exists(target_path):
                    target_size = os.path.getsize(target_path)
                    if target_size == source_size:
                        copied_files.append(pdf_file)
                        logging.info(f"复制大标PDF成功: {pdf_file} ({source_size} 字节)")
                    else:
                        failed_copies.append(pdf_file)
                        logging.error(f"复制后文件大小不一致: {pdf_file} (源:{source_size}, 目标:{target_size})")
                else:
                    failed_copies.append(pdf_file)
                    logging.error(f"复制后文件不存在: {pdf_file}")
            else:
                failed_copies.append(pdf_file)
                logging.warning(f"源大标PDF不存在: {source_path}")
        except Exception as e:
            failed_copies.append(pdf_file)
            logging.error(f"复制大标PDF失败: {source_path} 到 {target_path}，原因: {e}")

    # 3. 在属性集文件夹中创建合并的大标PDF（与数量文件夹并列）
    if copied_files:
        merged_pdf_path = os.path.join(attr_folder_path, '大标.pdf')

        # 等待一小段时间确保文件系统同步
        import time
        time.sleep(0.1)

        merge_big_label_pdfs_from_folder_optimized(big_label_folder, merged_pdf_path)
        logging.info(f"已为 {attr_folder_path} 创建合并的大标PDF: {merged_pdf_path}")
    else:
        logging.warning(f"没有成功复制任何大标PDF文件到 {attr_folder_path}，跳过合并步骤")

    # 输出处理结果
    logging.info(f"完成处理属性集文件夹: {attr_folder_path}")
    logging.info(f"  成功复制: {len(copied_files)} 个文件")
    if failed_copies:
        logging.warning(f"  复制失败: {len(failed_copies)} 个文件: {failed_copies}")

def find_attribute_folders(target_base_folder, skc_base):
    """
    查找包含指定SKC的属性集文件夹
    """
    attribute_folders = []

    if not os.path.exists(target_base_folder):
        return attribute_folders

    # 遍历目标文件夹，查找包含SKC图片的文件夹
    for folder_name in os.listdir(target_base_folder):
        folder_path = os.path.join(target_base_folder, folder_name)
        if os.path.isdir(folder_path):
            # 检查文件夹内是否有对应SKC的图片或文件
            if folder_contains_skc(folder_path, skc_base):
                attribute_folders.append(folder_path)

    return attribute_folders

def folder_contains_skc(folder_path, skc_base):
    """
    检查文件夹是否包含指定SKC的文件
    """
    try:
        # 递归检查文件夹及其子文件夹
        for _, _, files in os.walk(folder_path):
            for file in files:
                if file.lower().startswith(skc_base.lower()) and (file.lower().endswith('.jpg') or file.lower().endswith('.pdf')):
                    return True
        return False
    except Exception as e:
        logging.error(f"检查文件夹 {folder_path} 时出错: {e}")
        return False

def process_big_labels_for_folder(attr_folder_path, big_pdf_folder, big_pdf_files, skc_base):
    """
    为指定的属性集文件夹处理大标PDF文件
    1. 在属性集文件夹中创建大标文件夹（与数量文件夹并列）
    2. 复制所有对应的大标PDF文件到大标文件夹
    3. 在属性集文件夹中创建合并的大标.pdf文件（与数量文件夹并列）
    """
    logging.info(f"处理属性集文件夹: {attr_folder_path} (SKC: {skc_base})")

    # 查找数量文件夹（如"1套"、"2张"等）
    quantity_folders = []
    for item in os.listdir(attr_folder_path):
        item_path = os.path.join(attr_folder_path, item)
        if os.path.isdir(item_path) and (item.endswith('套') or item.endswith('张')):
            quantity_folders.append(item_path)

    if not quantity_folders:
        logging.warning(f"在 {attr_folder_path} 中未找到数量文件夹")
        return

    # 1. 在属性集文件夹中创建大标文件夹（与数量文件夹并列）
    big_label_folder = os.path.join(attr_folder_path, '大标')
    os.makedirs(big_label_folder, exist_ok=True)
    logging.info(f"创建大标文件夹: {big_label_folder}")

    # 2. 复制所有对应的大标PDF文件到大标文件夹
    copied_files = []
    for pdf_file in big_pdf_files:
        source_path = os.path.join(big_pdf_folder, pdf_file)
        target_path = os.path.join(big_label_folder, pdf_file)

        try:
            if os.path.exists(source_path):
                if not os.path.exists(target_path):
                    shutil.copy(source_path, target_path)
                    copied_files.append(pdf_file)
                    logging.info(f"复制大标PDF: {pdf_file} 到 {big_label_folder}")
                else:
                    copied_files.append(pdf_file)
                    logging.info(f"大标PDF已存在，跳过: {target_path}")
            else:
                logging.warning(f"源大标PDF不存在: {source_path}")
        except Exception as e:
            logging.error(f"复制大标PDF失败: {source_path} 到 {target_path}，原因: {e}")

    # 3. 在属性集文件夹中创建合并的大标PDF（与数量文件夹并列）
    # 合并属性集文件夹中大标文件夹里的所有PDF文件
    if copied_files:
        merged_pdf_path = os.path.join(attr_folder_path, '大标.pdf')
        merge_big_label_pdfs_from_folder(big_label_folder, merged_pdf_path)
        logging.info(f"已为 {attr_folder_path} 创建合并的大标PDF: {merged_pdf_path}")
    else:
        logging.warning(f"没有成功复制任何大标PDF文件到 {attr_folder_path}，跳过合并步骤")

    logging.info(f"完成处理属性集文件夹: {attr_folder_path} (复制了 {len(copied_files)} 个文件)")

def merge_big_label_pdfs(big_pdf_folder, big_pdf_files, output_path):
    """
    合并多个大标PDF文件为一个文件
    """
    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        return

    try:
        if not big_pdf_files:
            logging.warning(f"没有大标PDF文件需要合并: {output_path}")
            return

        writer = PyPDF2.PdfWriter()
        merged_count = 0

        # 按文件名排序，确保页面顺序正确
        sorted_files = sorted(big_pdf_files)
        logging.info(f"开始合并 {len(sorted_files)} 个大标PDF文件: {sorted_files}")

        for pdf_file in sorted_files:
            source_path = os.path.join(big_pdf_folder, pdf_file)
            if os.path.exists(source_path):
                try:
                    with open(source_path, 'rb') as file:
                        reader = PyPDF2.PdfReader(file)
                        page_count = len(reader.pages)
                        for page in reader.pages:
                            writer.add_page(page)
                        merged_count += 1
                        logging.info(f"已合并 {pdf_file} ({page_count} 页)")
                except Exception as e:
                    logging.error(f"合并PDF文件 {pdf_file} 时出错: {e}")
            else:
                logging.warning(f"大标PDF文件不存在: {source_path}")

        # 写入合并后的PDF
        if merged_count > 0:
            if not os.path.exists(output_path):
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)
                logging.info(f"合并大标PDF完成: {output_path} (合并了 {merged_count} 个文件)")
            else:
                logging.info(f"合并大标PDF已存在，跳过: {output_path}")
        else:
            logging.error(f"没有成功合并任何PDF文件，无法创建: {output_path}")

    except Exception as e:
        logging.error(f"合并大标PDF失败: {output_path}，原因: {e}")
        import traceback
        logging.error(f"详细错误信息: {traceback.format_exc()}")

def merge_big_label_pdfs_from_folder_optimized(big_label_folder, output_path):
    """
    优化版本：合并指定文件夹中的所有PDF文件为一个文件
    加强错误处理和文件完整性检查
    """
    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        return

    try:
        if not os.path.exists(big_label_folder):
            logging.warning(f"大标文件夹不存在: {big_label_folder}")
            return

        # 获取文件夹中所有PDF文件
        pdf_files = []
        for filename in os.listdir(big_label_folder):
            if filename.lower().endswith('.pdf'):
                file_path = os.path.join(big_label_folder, filename)
                # 检查文件是否可读且不为空
                try:
                    file_size = os.path.getsize(file_path)
                    if file_size > 0:
                        pdf_files.append(filename)
                    else:
                        logging.warning(f"跳过空文件: {filename}")
                except Exception as e:
                    logging.warning(f"无法读取文件信息: {filename}，原因: {e}")

        if not pdf_files:
            logging.warning(f"大标文件夹中没有有效的PDF文件: {big_label_folder}")
            return

        writer = PyPDF2.PdfWriter()
        merged_count = 0
        total_pages = 0

        # 按文件名排序，确保页面顺序正确
        sorted_files = sorted(pdf_files)
        logging.info(f"开始合并大标文件夹中的 {len(sorted_files)} 个PDF文件")

        for pdf_file in sorted_files:
            source_path = os.path.join(big_label_folder, pdf_file)
            try:
                # 多次尝试读取文件，处理文件锁定问题
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        with open(source_path, 'rb') as file:
                            reader = PyPDF2.PdfReader(file)
                            page_count = len(reader.pages)

                            if page_count == 0:
                                logging.warning(f"PDF文件没有页面: {pdf_file}")
                                break

                            for page_num, page in enumerate(reader.pages):
                                try:
                                    writer.add_page(page)
                                    total_pages += 1
                                except Exception as e:
                                    logging.error(f"添加页面失败: {pdf_file} 第{page_num+1}页，原因: {e}")

                            merged_count += 1
                            logging.info(f"已合并 {pdf_file} ({page_count} 页)")
                            break  # 成功读取，跳出重试循环

                    except Exception as e:
                        if attempt < max_retries - 1:
                            logging.warning(f"读取PDF文件失败，重试 {attempt + 1}/{max_retries}: {pdf_file}，原因: {e}")
                            import time
                            time.sleep(0.1)  # 短暂等待后重试
                        else:
                            logging.error(f"多次尝试后仍无法读取PDF文件: {pdf_file}，原因: {e}")

            except Exception as e:
                logging.error(f"处理PDF文件时出错: {pdf_file}，原因: {e}")

        # 写入合并后的PDF
        if merged_count > 0 and total_pages > 0:
            try:
                # 确保输出目录存在
                output_dir = os.path.dirname(output_path)
                if output_dir and not os.path.exists(output_dir):
                    os.makedirs(output_dir, exist_ok=True)

                # 写入文件
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)

                # 验证输出文件
                if os.path.exists(output_path):
                    output_size = os.path.getsize(output_path)
                    if output_size > 0:
                        logging.info(f"合并大标PDF完成: {output_path}")
                        logging.info(f"  合并了 {merged_count} 个文件，共 {total_pages} 页，大小 {output_size} 字节")
                    else:
                        logging.error(f"合并后的PDF文件为空: {output_path}")
                else:
                    logging.error(f"合并后的PDF文件不存在: {output_path}")

            except Exception as e:
                logging.error(f"写入合并PDF文件失败: {output_path}，原因: {e}")
        else:
            logging.error(f"没有成功合并任何PDF文件，无法创建: {output_path}")
            logging.error(f"  merged_count: {merged_count}, total_pages: {total_pages}")

    except Exception as e:
        logging.error(f"合并大标PDF失败: {output_path}，原因: {e}")
        import traceback
        logging.error(f"详细错误信息: {traceback.format_exc()}")

def merge_big_label_pdfs_from_folder(big_label_folder, output_path):
    """
    合并指定文件夹中的所有PDF文件为一个文件
    """
    # 延迟导入PyPDF2
    PyPDF2 = lazy_import_pypdf2()
    if not PyPDF2:
        return

    try:
        if not os.path.exists(big_label_folder):
            logging.warning(f"大标文件夹不存在: {big_label_folder}")
            return

        # 获取文件夹中所有PDF文件
        pdf_files = []
        for filename in os.listdir(big_label_folder):
            if filename.lower().endswith('.pdf'):
                pdf_files.append(filename)

        if not pdf_files:
            logging.warning(f"大标文件夹中没有PDF文件: {big_label_folder}")
            return

        writer = PyPDF2.PdfWriter()
        merged_count = 0

        # 按文件名排序，确保页面顺序正确
        sorted_files = sorted(pdf_files)
        logging.info(f"开始合并大标文件夹中的 {len(sorted_files)} 个PDF文件: {sorted_files}")

        for pdf_file in sorted_files:
            source_path = os.path.join(big_label_folder, pdf_file)
            try:
                with open(source_path, 'rb') as file:
                    reader = PyPDF2.PdfReader(file)
                    page_count = len(reader.pages)
                    for page in reader.pages:
                        writer.add_page(page)
                    merged_count += 1
                    logging.info(f"已合并 {pdf_file} ({page_count} 页)")
            except Exception as e:
                logging.error(f"合并PDF文件 {pdf_file} 时出错: {e}")

        # 写入合并后的PDF
        if merged_count > 0:
            # 强制覆盖已存在的文件，确保内容是最新的
            with open(output_path, 'wb') as output_file:
                writer.write(output_file)
            logging.info(f"合并大标PDF完成: {output_path} (合并了 {merged_count} 个文件)")
        else:
            logging.error(f"没有成功合并任何PDF文件，无法创建: {output_path}")

    except Exception as e:
        logging.error(f"合并大标PDF失败: {output_path}，原因: {e}")
        import traceback
        logging.error(f"详细错误信息: {traceback.format_exc()}")



# 主界面
# 全局变量，用于保存上次选择的路径和UI组件
last_folder_paths = {
    "path_entry": "",
    "pdf_entry": "",
    "source_entry": "",
    "small_entry": "",
    "pdf_entry_2": ""
}

# 全局UI组件变量
path_entry = None
pdf_entry = None
source_entry = None
small_entry = None
pdf_entry_2 = None

def load_paths():
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get("main_folder", ""), data.get("img_folder", "")
    except Exception:
        pass
    return "", ""

def save_paths(main_folder, img_folder):
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump({"main_folder": main_folder, "img_folder": img_folder}, f, ensure_ascii=False, indent=4)
    except Exception:
        pass

def load_folder_config():
    """加载文件夹命名配置"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                config = data.get("folder_config", {})
                return {
                    "date": config.get("date", ""),
                    "time_period": config.get("time_period", ""),
                    "operator": config.get("operator", ""),
                    "shop_number": config.get("shop_number", ""),
                    "operator_history": config.get("operator_history", []),
                    "shop_history": config.get("shop_history", [])
                }
    except Exception:
        pass
    return {
        "date": "",
        "time_period": "",
        "operator": "",
        "shop_number": "",
        "operator_history": [],
        "shop_history": []
    }

def save_folder_config(config):
    """保存文件夹命名配置"""
    try:
        data = {}
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)

        data["folder_config"] = config

        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    except Exception:
        pass

def add_to_history(history_list, new_item, max_items=10):
    """添加项目到历史记录，避免重复并限制数量"""
    if new_item and new_item.strip():
        item = new_item.strip()
        if item in history_list:
            history_list.remove(item)
        history_list.insert(0, item)
        return history_list[:max_items]
    return history_list

def parse_date(date_str):
    """解析日期字符串，返回月和日"""
    try:
        if '-' in date_str:
            parts = date_str.split('-')
            if len(parts) == 2:
                month = int(parts[0])
                day = int(parts[1])
                return month, day
    except:
        pass
    return None, None

def format_date(month, day):
    """格式化日期为字符串"""
    return f"{month}-{day}"

def adjust_date(date_str, days):
    """调整日期，支持跨月"""
    month, day = parse_date(date_str)
    if month is None or day is None:
        return date_str

    # 简单的日期调整逻辑（不考虑闰年等复杂情况）
    days_in_month = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]

    day += days

    while day > days_in_month[month - 1]:
        day -= days_in_month[month - 1]
        month += 1
        if month > 12:
            month = 1

    while day < 1:
        month -= 1
        if month < 1:
            month = 12
        day += days_in_month[month - 1]

    return format_date(month, day)

def select_folder(entry):
    """选择文件夹并更新配置"""
    folder_path = filedialog.askdirectory()
    if folder_path:
        entry.delete(0, tk.END)
        entry.insert(0, folder_path)
        last_folder_paths[entry._name] = folder_path
        # 获取当前的img_folder路径
        current_img_folder = last_folder_paths.get("source_entry", "")
        save_paths(folder_path, current_img_folder)  # 立即保存配置
        logging.info(f"已选择文件夹: {folder_path}")

def select_pdf(entry):
    """选择PDF文件并更新配置"""
    pdf_path = filedialog.askopenfilename(filetypes=[("PDF Files", "*.pdf")])
    if pdf_path:
        entry.delete(0, tk.END)
        entry.insert(0, pdf_path)
        last_folder_paths[entry._name] = pdf_path
        # 同时保存父目录路径
        parent_dir = os.path.dirname(pdf_path)
        if parent_dir:
            last_folder_paths[f"{entry._name}_dir"] = parent_dir
        # 获取当前的main_folder路径
        current_main_folder = last_folder_paths.get("path_entry", "")
        save_paths(current_main_folder, parent_dir)  # 立即保存配置
        logging.info(f"已选择PDF文件: {pdf_path}")

def create_main_window():
    try:
        # 检查激活状态
        if not is_activated():
            prompt_for_activation()
            if not is_activated():  # 再次检查，以防用户关闭激活窗口
                messagebox.showerror("错误", "软件未激活，无法使用")
                return

        global path_entry, pdf_entry, source_entry, small_entry, pdf_entry_2
        root = tk.Tk()
        root.title("文件处理工具")
        root.geometry("670x650")
        root.configure(bg='lightgray')
        large_font = tk.font.Font(size=12, family='Arial', weight='bold')

        # 功能一界面
        tk.Label(root, text="牛马利器", font=large_font, bg='lightgray').grid(row=0, column=0, columnspan=3, pady=10)
        
        # 创建输入框并设置名称
        path_entry = tk.Entry(root, font=large_font, width=50, bg='white')
        path_entry._name = "path_entry"
        
        pdf_entry = tk.Entry(root, font=large_font, width=50, bg='white')
        pdf_entry._name = "pdf_entry"
        
        source_entry = tk.Entry(root, font=large_font, width=50, bg='white')
        source_entry._name = "source_entry"
        
        small_entry = tk.Entry(root, font=large_font, width=50, bg='white')
        small_entry._name = "small_entry"
        
        pdf_entry_2 = tk.Entry(root, font=large_font, width=50, bg='white')
        pdf_entry_2._name = "pdf_entry_2"

        # 加载保存的路径
        load_paths()
        
        # 设置输入框的初始值
        path_entry.insert(0, last_folder_paths.get("path_entry", ""))
        pdf_entry.insert(0, last_folder_paths.get("pdf_entry", ""))
        source_entry.insert(0, last_folder_paths.get("source_entry", ""))
        small_entry.insert(0, last_folder_paths.get("small_entry", ""))
        pdf_entry_2.insert(0, last_folder_paths.get("pdf_entry_2", ""))

        # 布局界面元素
        tk.Label(root, text="文件夹：", font=large_font, bg='lightgray').grid(row=1, column=0, sticky='e', padx=5, pady=5)
        path_entry.grid(row=1, column=1, padx=5, pady=5)
        tk.Button(root, text="选择文件夹", command=lambda: select_folder(path_entry), font=large_font, bg='lightgray').grid(row=1, column=2)
        
        tk.Label(root, text="拣货单：", font=large_font, bg='lightgray').grid(row=2, column=0, sticky='e', padx=5, pady=5)
        pdf_entry.grid(row=2, column=1, padx=5, pady=5)
        tk.Button(root, text="选择文件", command=lambda: select_pdf(pdf_entry), font=large_font, bg='lightgray').grid(row=2, column=2)
        
        tk.Label(root, text="原图：", font=large_font, bg='lightgray').grid(row=3, column=0, sticky='e', padx=5, pady=5)
        source_entry.grid(row=3, column=1, padx=5, pady=5)
        tk.Button(root, text="选择文件夹", command=lambda: select_folder(source_entry), font=large_font, bg='lightgray').grid(row=3, column=2)
        
        tk.Label(root, text="小标：", font=large_font, bg='lightgray').grid(row=4, column=0, sticky='e', padx=5, pady=5)
        small_entry.grid(row=4, column=1, padx=5, pady=5)
        tk.Button(root, text="选择文件", command=lambda: select_pdf(small_entry), font=large_font, bg='lightgray').grid(row=4, column=2)

        # 进度条
        progress_bar = ttk.Progressbar(root, orient='horizontal', length=400, mode='determinate', style='TProgressbar')
        progress_bar.grid(row=5, column=0, columnspan=3, pady=20)
        progress_label = tk.Label(root, text="0.00%", font=large_font, bg='lightgray')
        progress_label.grid(row=6, column=0, columnspan=3, pady=10)
        
        # 功能一按钮
        tk.Button(root, text="起飞", command=lambda: run_script_1(progress_bar, progress_label), font=large_font, width=20, bg='lightgray').grid(row=7, column=0, columnspan=3, pady=20)

        # 功能二界面
        tk.Label(root, text="拆大标", font=large_font, bg='lightgray').grid(row=8, column=0, columnspan=3, pady=10)
        tk.Label(root, text="大标：", font=large_font, bg='lightgray').grid(row=9, column=0, sticky='e', padx=5, pady=5)
        pdf_entry_2.grid(row=9, column=1, padx=5, pady=5)
        tk.Button(root, text="选择文件", command=lambda: select_pdf(pdf_entry_2), font=large_font, bg='lightgray').grid(row=9, column=2)
        tk.Button(root, text="拆大标", command=run_script_2, font=large_font, width=20, bg='lightgray').grid(row=10, column=0, columnspan=3, pady=20)

        # 设置窗口关闭事件
        def on_closing():
            save_paths(path_entry.get(), source_entry.get())
            root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)
        root.mainloop()
    except Exception as e:
        logging.error(f"创建主窗口时发生错误: {e}")
        messagebox.showerror("错误", f"创建主窗口时发生错误: {e}")

def parse_canvas_size(text):
    """
    解析类似"帆布画-24in/60cmx16in/40cmx3pc"等任意顺序、分隔符、数量的尺寸描述，返回标准尺寸表达式。
    返回值示例：('24x16inch', '60x40cm', 3)
    """
    # 统一所有分隔符为"|"
    text_norm = re.sub(r'[xX×/\\\-]', '|', text)
    # 全局提取所有 inch 和 cm 数值
    inch_vals = re.findall(r'(\d+\.?\d*)[^\d]*?(?:in|inch)', text_norm, re.IGNORECASE)
    cm_vals = re.findall(r'(\d+\.?\d*)[^\d]*?cm', text_norm, re.IGNORECASE)
    logging.info(f"parse_canvas_size inch_vals: {inch_vals}, cm_vals: {cm_vals}")
    # 组装字符串
    if len(inch_vals) >= 2:
        inch_str = f"{inch_vals[0]}x{inch_vals[1]}inch"
    else:
        inch_str = None
    if len(cm_vals) >= 2:
        cm_str = f"{cm_vals[0]}x{cm_vals[1]}cm"
    else:
        cm_str = None
    if not (inch_str and cm_str):
        return None
    # 查找数量（如3pc，允许任意位置）
    count_match = re.search(r'(\d+)\s*pc', text, re.IGNORECASE)
    count = int(count_match.group(1)) if count_match else 1
    return inch_str, cm_str, count

def auto_find_file(folder, keywords):
    """
    在folder下查找文件名包含keywords任一关键字的PDF文件，返回第一个匹配的完整路径。
    """
    for fname in os.listdir(folder):
        if fname.lower().endswith('.pdf') and any(kw.lower() in fname.lower() for kw in keywords):
            return os.path.join(folder, fname)
    return None

# ========== ttkbootstrap 美化UI主界面入口 ==========
import ttkbootstrap as tb
from ttkbootstrap.constants import *

def main():
    if not is_activated():
        prompt_for_activation()
        if not is_activated():
            messagebox.showerror("错误", "软件未激活，无法使用")
            return
    app = tb.Window(themename="flatly")
    app.title("牛马利器")
    app.geometry("900x580")
    app.minsize(800, 520)
    app.configure(bg="#eaf4fb")
    app.resizable(True, True)
    # 顶部LOGO和标题
    topbar = tk.Frame(app, bg="#eaf4fb", height=60)
    topbar.pack(side=tk.TOP, fill=tk.X)
    logo = tk.Label(topbar, text="🐳", font=("Segoe UI Emoji", 28), bg="#eaf4fb")
    logo.pack(side=tk.LEFT, padx=(24, 8), pady=8)
    logo.bind("<Button-1>", lambda _: messagebox.showinfo("彩蛋", "鲸鱼：别慌，单子做不完！\n摸摸鲸鱼头，bug都消失~"))
    title = tk.Label(topbar, text="牛马利器", font=("微软雅黑", 22, "bold"), fg="#1976d2", bg="#eaf4fb")
    title.pack(side=tk.LEFT, pady=8)
    subtitle = tk.Label(topbar, text="让做单像呼吸一样自然", font=("微软雅黑", 12), fg="#6c757d", bg="#eaf4fb")
    subtitle.pack(side=tk.LEFT, padx=(16,0), pady=8)
    # 主体区域
    main_frame = tk.Frame(app, bg="#eaf4fb")
    main_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=16, pady=(0, 16))
    left_frame = tk.Frame(main_frame, width=360, bg="#fff", highlightthickness=0)
    left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 12), pady=0)
    left_frame.pack_propagate(False)
    # 主文件夹输入
    path_frame = tk.Frame(left_frame, bg="#fff")
    path_frame.pack(fill=tk.X, pady=(18, 8), padx=18)
    tk.Label(path_frame, text="主文件", font=("微软雅黑", 11), fg="#1976d2", bg="#fff").pack(side=tk.LEFT)
    path_var = tk.StringVar()
    path_entry = tk.Entry(path_frame, textvariable=path_var, font=("微软雅黑", 11), width=22, relief="flat", highlightthickness=1, highlightbackground="#e3e7ed", bg="#f5f6fa", fg="#222")
    path_entry.pack(side=tk.LEFT, padx=(8, 6))
    # 路径记忆：初始化时自动填充
    main_path, img_path = load_paths()
    path_var.set(main_path)
    # 原图文件夹输入
    img_frame = tk.Frame(left_frame, bg="#fff")
    img_frame.pack(fill=tk.X, pady=(0, 8), padx=18)
    tk.Label(img_frame, text="原图啊", font=("微软雅黑", 11), fg="#1976d2", bg="#fff").pack(side=tk.LEFT)
    img_var = tk.StringVar()
    img_entry = tk.Entry(img_frame, textvariable=img_var, font=("微软雅黑", 11), width=22, relief="flat", highlightthickness=1, highlightbackground="#e3e7ed", bg="#f5f6fa", fg="#222")
    img_entry.pack(side=tk.LEFT, padx=(8, 6))
    img_var.set(img_path)
    def select_folder():
        result = filedialog.askdirectory()
        if result:
            path_var.set(result)
            save_paths(result, img_var.get())
    tb.Button(path_frame, text="选啊", bootstyle=PRIMARY, width=7, command=select_folder).pack(side=tk.LEFT)
    def select_img_folder():
        result = filedialog.askdirectory()
        if result:
            img_var.set(result)
            save_paths(path_var.get(), result)
    tb.Button(img_frame, text="选啊", bootstyle=PRIMARY, width=7, command=select_img_folder).pack(side=tk.LEFT)

    # 加载文件夹命名配置
    folder_config = load_folder_config()

    # 文件夹命名配置输入框
    config_frame = tk.LabelFrame(left_frame, text="文件夹命名配置（可选）", bg="#fff", fg="#1976d2", font=("微软雅黑", 10, "bold"), labelanchor="n")
    config_frame.pack(fill=tk.X, padx=18, pady=(8, 8))

    # 日期输入（带上下调节按钮）
    date_frame = tk.Frame(config_frame, bg="#fff")
    date_frame.pack(fill=tk.X, pady=2, padx=8)
    tk.Label(date_frame, text="日期:", font=("微软雅黑", 9), fg="#1976d2", bg="#fff", width=8).pack(side=tk.LEFT)
    date_var = tk.StringVar(value=folder_config.get("date", ""))
    date_entry = tk.Entry(date_frame, textvariable=date_var, font=("微软雅黑", 9), width=12, relief="flat", highlightthickness=1, highlightbackground="#e3e7ed", bg="#f5f6fa", fg="#222")
    date_entry.pack(side=tk.LEFT, padx=(5, 2))

    # 日期调节按钮
    date_btn_frame = tk.Frame(date_frame, bg="#fff")
    date_btn_frame.pack(side=tk.LEFT, padx=(2, 5))

    def adjust_date_up():
        current_date = date_var.get().strip()
        if current_date:
            new_date = adjust_date(current_date, 1)
            date_var.set(new_date)
        else:
            # 如果为空，设置为今天的日期格式
            import datetime
            today = datetime.date.today()
            date_var.set(f"{today.month}-{today.day}")

    def adjust_date_down():
        current_date = date_var.get().strip()
        if current_date:
            new_date = adjust_date(current_date, -1)
            date_var.set(new_date)
        else:
            # 如果为空，设置为今天的日期格式
            import datetime
            today = datetime.date.today()
            date_var.set(f"{today.month}-{today.day}")

    tk.Button(date_btn_frame, text="▲", font=("微软雅黑", 8), width=2, height=1, command=adjust_date_up, relief="flat", bg="#e3f2fd", fg="#1976d2", activebackground="#bbdefb").pack(side=tk.TOP)
    tk.Button(date_btn_frame, text="▼", font=("微软雅黑", 8), width=2, height=1, command=adjust_date_down, relief="flat", bg="#e3f2fd", fg="#1976d2", activebackground="#bbdefb").pack(side=tk.TOP)

    tk.Label(date_frame, text="(如: 4-15)", font=("微软雅黑", 8), fg="#6c757d", bg="#fff").pack(side=tk.LEFT, padx=(5, 0))

    # 时间段选择（下拉框）
    time_frame = tk.Frame(config_frame, bg="#fff")
    time_frame.pack(fill=tk.X, pady=2, padx=8)
    tk.Label(time_frame, text="时间段:", font=("微软雅黑", 9), fg="#1976d2", bg="#fff", width=8).pack(side=tk.LEFT)
    time_var = tk.StringVar(value=folder_config.get("time_period", ""))
    time_combo = ttk.Combobox(time_frame, textvariable=time_var, font=("微软雅黑", 9), width=13, state="readonly", values=["", "上午", "下午"])
    time_combo.pack(side=tk.LEFT, padx=(5, 0))
    tk.Label(time_frame, text="(选择时间段)", font=("微软雅黑", 8), fg="#6c757d", bg="#fff").pack(side=tk.LEFT, padx=(5, 0))

    # 操作员输入（带历史记录）
    operator_frame = tk.Frame(config_frame, bg="#fff")
    operator_frame.pack(fill=tk.X, pady=2, padx=8)
    tk.Label(operator_frame, text="操作员:", font=("微软雅黑", 9), fg="#1976d2", bg="#fff", width=8).pack(side=tk.LEFT)
    operator_var = tk.StringVar(value=folder_config.get("operator", ""))
    operator_history = folder_config.get("operator_history", [])
    operator_combo = ttk.Combobox(operator_frame, textvariable=operator_var, font=("微软雅黑", 9), width=13, values=operator_history)
    operator_combo.pack(side=tk.LEFT, padx=(5, 0))
    tk.Label(operator_frame, text="(如: 蔡徐坤)", font=("微软雅黑", 8), fg="#6c757d", bg="#fff").pack(side=tk.LEFT, padx=(5, 0))

    # 店铺号选择（带历史记录）
    shop_frame = tk.Frame(config_frame, bg="#fff")
    shop_frame.pack(fill=tk.X, pady=2, padx=8)
    tk.Label(shop_frame, text="店铺号:", font=("微软雅黑", 9), fg="#1976d2", bg="#fff", width=8).pack(side=tk.LEFT)
    shop_var = tk.StringVar(value=folder_config.get("shop_number", ""))
    shop_history = folder_config.get("shop_history", [])
    shop_combo = ttk.Combobox(shop_frame, textvariable=shop_var, font=("微软雅黑", 9), width=13, values=shop_history)
    shop_combo.pack(side=tk.LEFT, padx=(5, 0))
    tk.Label(shop_frame, text="(选择或输入店铺号)", font=("微软雅黑", 8), fg="#6c757d", bg="#fff").pack(side=tk.LEFT, padx=(5, 0))

    # 进度条
    step_frame = tk.Frame(left_frame, bg="#fff")
    step_frame.pack(fill=tk.X, pady=(8, 8), padx=18)
    progress_var = tk.DoubleVar(value=0)
    progress_bar = tb.Progressbar(left_frame, variable=progress_var, length=260, bootstyle=INFO, mode="determinate")
    progress_bar.pack(padx=18, pady=(0, 8), fill=tk.X)
    def log_callback(msg):
        log_text.insert(tk.END, msg + "\n")
        log_text.see(tk.END)
    def progress_callback(val):
        progress_var.set(val)
        progress_bar.update()
    # 起飞按钮
    def on_start():
        main_folder = path_var.get().strip()
        img_folder = img_var.get().strip()
        save_paths(main_folder, img_folder)

        # 获取当前配置
        current_date = date_var.get().strip()
        current_time = time_var.get().strip()
        current_operator = operator_var.get().strip()
        current_shop = shop_var.get().strip()

        # 更新历史记录
        updated_operator_history = add_to_history(operator_history.copy(), current_operator)
        updated_shop_history = add_to_history(shop_history.copy(), current_shop)

        # 保存文件夹命名配置
        current_config = {
            "date": current_date,
            "time_period": current_time,
            "operator": current_operator,
            "shop_number": current_shop,
            "operator_history": updated_operator_history,
            "shop_history": updated_shop_history
        }
        save_folder_config(current_config)

        # 更新下拉框的选项
        operator_combo['values'] = updated_operator_history
        shop_combo['values'] = updated_shop_history

        if not main_folder or not img_folder:
            messagebox.showerror("错误", "请先选择主文件夹和原图文件夹！")
            return
        log_text.delete(1.0, tk.END)
        log_callback("🐳 开始处理，请稍候...")
        def task():
            picking_pdf = auto_find_file(main_folder, ["拣货单", "picking"])
            small_pdf = auto_find_file(main_folder, ["小标", "small"])
            # big_pdf = auto_find_file(main_folder, ["大标", "big"])  # 暂时不需要

            if not picking_pdf or not small_pdf:
                log_callback("未找到拣货单或小标PDF，请检查主文件夹！")
                return

            # 创建小标PDF文件夹并拆分
            small_pdf_folder = os.path.join(main_folder, '小标PDF')
            os.makedirs(small_pdf_folder, exist_ok=True)
            split_pdf(small_pdf, small_pdf_folder, log_callback)

            # 提取数据
            df = extract_pdf_data(picking_pdf)
            if df.empty:
                log_callback("未能从拣货单PDF中提取任何数据，请检查文件。")
                return

            # 检查图片和小标PDF缺失文件，获取统计信息
            missing_files, stats = check_missing_files(df, img_folder, small_pdf_folder)

            # 输出检查结果日志
            log_callback("检查结果：")
            log_callback(f"- 匹配到的图片数量：{stats['matched_images_count']}")
            log_callback(f"- 匹配到的小标PDF数量：{stats['matched_small_pdfs_count']}")
            log_callback(f"- 是否有重复图片：{'有' if stats['has_duplicates'] else '无'}")
            if stats['duplicate_skcs']:
                duplicate_skcs_str = ','.join(stats['duplicate_skcs'])
                log_callback(f"- 重复图片SKC编号：{duplicate_skcs_str}")
            else:
                log_callback("- 重复图片SKC编号：无")

            if missing_files:
                if show_missing_files_dialog(missing_files):
                    process_files(
                        df=df,
                        target_base_folder=main_folder,
                        source_folder=img_folder,
                        small_pdf_folder=small_pdf_folder,
                        progress_bar=progress_bar,
                        progress_label=None,
                        progress_callback=progress_callback,
                        folder_config=current_config
                    )
                    log_callback("🐳 起飞成功！")
                else:
                    log_callback("已取消起飞。")
            else:
                process_files(
                    df=df,
                    target_base_folder=main_folder,
                    source_folder=img_folder,
                    small_pdf_folder=small_pdf_folder,
                    progress_bar=progress_bar,
                    progress_label=None,
                    progress_callback=progress_callback,
                    folder_config=current_config
                )
                log_callback("🐳 起飞成功！")
        threading.Thread(target=task, daemon=True).start()
    # 拆大标按钮
    def on_split_big():
        main_folder = path_var.get().strip()
        img_folder = img_var.get().strip()
        save_paths(main_folder, img_folder)
        if not main_folder:
            messagebox.showerror("错误", "请先选择主文件夹！")
            return
        log_text.delete(1.0, tk.END)
        log_callback("🐳 开始拆分大标PDF...")
        def task():
            big_pdf = auto_find_file(main_folder, ["大标", "big"])
            picking_pdf = auto_find_file(main_folder, ["拣货单", "picking"])

            if not big_pdf:
                log_callback("未找到大标PDF，请检查主文件夹！")
                return

            output_dir = os.path.join(main_folder, '大标')
            os.makedirs(output_dir, exist_ok=True)

            # 简化版拆分，不输出详细调试信息
            split_pdf_rename(big_pdf, output_dir, None)  # 不传递log_callback，避免详细日志

            # 统计拆分出的大标PDF数量
            big_pdf_count = 0
            if os.path.exists(output_dir):
                for filename in os.listdir(output_dir):
                    if filename.lower().endswith('.pdf'):
                        big_pdf_count += 1

            # 简洁的日志输出
            log_callback("检查结果：")
            log_callback(f"- 匹配到的大标PDF数量：{big_pdf_count}")

            # 如果有拣货单，自动移动大标到对应文件夹
            if picking_pdf:
                log_callback("🐳 开始移动大标到对应文件夹...")
                df = extract_pdf_data(picking_pdf)
                if not df.empty:
                    move_big_labels_to_folders(df, main_folder, output_dir)
                    log_callback("🐳 大标移动完成！")
                else:
                    log_callback("未能从拣货单PDF中提取数据，跳过移动步骤")
            else:
                log_callback("未找到拣货单PDF，跳过移动步骤")

            log_callback("🐳 大标拆分完成！")
        threading.Thread(target=task, daemon=True).start()

    tb.Button(step_frame, text="起飞", bootstyle=SECONDARY, width=22, command=on_start).pack(fill=tk.X, pady=(0, 8))
    tb.Button(step_frame, text="拆大标（可选）", bootstyle=PRIMARY, width=22, command=on_split_big).pack(fill=tk.X)
    # 右侧区域 - 日志和说明
    right_frame = tk.Frame(main_frame, bg="#fff", highlightthickness=0)
    right_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 0), pady=0)
    right_frame.pack_propagate(False)

    # 日志区域
    log_frame = tk.LabelFrame(right_frame, text="验证/日志", bg="#fff", fg="#1976d2", font=("微软雅黑", 10, "bold"), labelanchor="n")
    log_frame.pack(fill=tk.BOTH, expand=True, padx=18, pady=(18, 8))
    log_text = tk.Text(log_frame, height=12, font=("微软雅黑", 10), bg="#f5f6fa", fg="#222", relief="flat")
    log_text.pack(fill=tk.BOTH, expand=True, padx=4, pady=4)
    log_text.insert(tk.END, "鲸鱼提示：这里会显示处理日志和验证信息~\n")

    # 说明区域
    desc_frame = tk.LabelFrame(right_frame, text="说明", bg="#fff", fg="#1976d2", font=("微软雅黑", 10, "bold"), labelanchor="n")
    desc_frame.pack(fill=tk.X, padx=18, pady=(8, 18))
    desc = (
        '【牛马利器】\n'
        '1. 选择主文件夹和原图文件夹，主文件夹放拣货单大标小标\n'
        '2. 填写命名配置（可选），不填则使用简单命名\n'
        '3. 点击"起飞"按钮，所有输出都在主文件夹下\n'
        '4. 自动查找大标/拣货单/小标，大标可选\n'
        '5. G-11.8x15.7 inch识别为金框，11.8x15.7inch-H识别为黑框\n'
        '\n鲸鱼祝你爆单！'
    )
    tk.Label(desc_frame, text=desc, font=("微软雅黑", 10), fg="#222", bg="#fff", justify="left", anchor="nw").pack(fill=tk.BOTH, expand=True, padx=8, pady=8)
    # 关闭窗口时自动保存路径和配置
    def on_closing():
        # 保存路径
        save_paths(path_var.get(), img_var.get())

        # 保存操作员和店铺号配置
        current_date = date_var.get().strip()
        current_time = time_var.get().strip()
        current_operator = operator_var.get().strip()
        current_shop = shop_var.get().strip()

        # 更新历史记录
        updated_operator_history = add_to_history(operator_history.copy(), current_operator)
        updated_shop_history = add_to_history(shop_history.copy(), current_shop)

        # 保存文件夹命名配置
        current_config = {
            "date": current_date,
            "time_period": current_time,
            "operator": current_operator,
            "shop_number": current_shop,
            "operator_history": updated_operator_history,
            "shop_history": updated_shop_history
        }
        save_folder_config(current_config)

        app.destroy()
    app.protocol("WM_DELETE_WINDOW", on_closing)
    app.mainloop()



def calculate_folder_quantities_from_picking_list(target_base_folder, df, source_folder):
    """
    全新逻辑：
    1. 从日志中提取每个SKC移动到了哪个数量文件夹（如1张、2张、3套等）
    2. 根据数量文件夹名称确定实际数量
    3. 按属性集分组统计总数量
    """
    logging.info("开始基于日志数量文件夹的统计逻辑...")

    # 第一步：从拣货单中建立SKC到属性集的映射
    skc_to_attribute = {}      # SKC -> 属性集

    logging.info("=== 分析拣货单，建立SKC到属性集映射 ===")
    for _, row in df.iterrows():
        product_info = row.get('商品信息')
        attribute_set = row.get('属性集')

        if not isinstance(product_info, str) or not product_info.strip():
            continue

        # 提取SKC编号
        skc_match = re.search(r'SKC[:：]?\s*(\d{9,11})', product_info)
        if not skc_match:
            continue

        skc_base = skc_match.group(1)

        # 建立SKC到属性集的映射
        if attribute_set:
            skc_to_attribute[skc_base] = attribute_set
            logging.info(f"拣货单行: SKC {skc_base} -> 属性集: {attribute_set}")

    logging.info("=== SKC到属性集映射完成 ===")

    # 第二步：从已创建的文件夹结构中提取实际的数量信息
    attribute_quantities = {}  # 属性集 -> 总数量

    logging.info("=== 从文件夹结构中提取数量信息 ===")

    def extract_quantity_from_folder_structure(folder_path, folder_name):
        """
        从文件夹结构中提取数量信息
        统一逻辑：
        - 数量 = SKC个数 × 数量文件夹的数字
        - 多pc的SKC（有-1，-2后缀）按套计算，不按个数计算（1个基础SKC = 1个SKC）
        - 最后按匹配到的相同文件夹相加
        """
        total_quantity = 0
        found_skcs = set()
        is_multi_pc = False  # 是否包含多pc的SKC

        try:
            # 检查是否是简单命名的属性集文件夹（如1pc30x40cm, 2pc40x60cm）
            simple_folder_match = re.match(r'(\d+)pc', folder_name.lower())
            if simple_folder_match:
                # 简单命名文件夹：直接在文件夹根目录查找图片
                logging.info(f"    检测到简单命名属性集文件夹: {folder_name}")

                folder_skcs = set()
                skc_details = {}

                for item in os.listdir(folder_path):
                    item_path = os.path.join(folder_path, item)
                    if os.path.isfile(item_path) and item.lower().endswith(('.jpg', '.jpeg', '.png')):
                        # 提取SKC
                        skc_match = re.search(r'(\d{9,11})(-\d+)?', item)
                        if skc_match:
                            base_skc = skc_match.group(1)
                            suffix = skc_match.group(2)
                            folder_skcs.add(base_skc)
                            found_skcs.add(base_skc)

                            if base_skc not in skc_details:
                                skc_details[base_skc] = {'files': [], 'has_suffix': False}
                            skc_details[base_skc]['files'].append(item)
                            if suffix:
                                skc_details[base_skc]['has_suffix'] = True
                                is_multi_pc = True

                if folder_skcs:
                    # 从文件夹名提取数量（如1pc30x40cm -> 1）
                    pc_count = int(simple_folder_match.group(1))
                    skc_count = len(folder_skcs)
                    total_quantity = skc_count * pc_count
                    logging.info(f"    简单命名文件夹: {skc_count}个基础SKC × {pc_count} = {total_quantity}")

                    # 详细显示每个SKC的信息
                    for base_skc, details in skc_details.items():
                        if details['has_suffix']:
                            logging.info(f"      SKC {base_skc}: 多pc (文件: {details['files']})")
                        else:
                            logging.info(f"      SKC {base_skc}: 单pc (文件: {details['files']})")
                else:
                    logging.info(f"    简单命名文件夹 {folder_name} 没有找到有效的SKC，跳过")

            else:
                # 复杂命名文件夹：在数量子文件夹中查找图片
                logging.info(f"    检测到复杂命名属性集文件夹: {folder_name}")

                # 遍历主文件夹下的数量文件夹
                for item in os.listdir(folder_path):
                    item_path = os.path.join(folder_path, item)
                    if os.path.isdir(item_path):
                        # 解析数量文件夹名称（如"1张"、"2张"、"3套"）
                        quantity_match = re.match(r'(\d+)[张套]', item)
                        if quantity_match:
                            quantity_value = int(quantity_match.group(1))
                            logging.info(f"    发现数量文件夹: {item} -> 数量值: {quantity_value}")

                            # 统计该数量文件夹中的基础SKC（去除后缀，多pc按1个SKC计算）
                            folder_skcs = set()  # 该文件夹中的基础SKC
                            skc_details = {}     # SKC -> 详细信息

                            for sub_item in os.listdir(item_path):
                                sub_item_path = os.path.join(item_path, sub_item)
                                if os.path.isfile(sub_item_path) and sub_item.lower().endswith(('.jpg', '.jpeg', '.png')):
                                    # 提取SKC（包括后缀）
                                    skc_match = re.search(r'(\d{9,11})(-\d+)?', sub_item)
                                    if skc_match:
                                        base_skc = skc_match.group(1)  # 基础SKC（不包括后缀）
                                        suffix = skc_match.group(2)   # 后缀部分（如-1，-2）

                                        # 添加到基础SKC集合（多pc的SKC只算1个）
                                        folder_skcs.add(base_skc)
                                        found_skcs.add(base_skc)

                                        # 记录SKC详细信息
                                        if base_skc not in skc_details:
                                            skc_details[base_skc] = {'files': [], 'has_suffix': False}

                                        skc_details[base_skc]['files'].append(sub_item)
                                        if suffix:
                                            skc_details[base_skc]['has_suffix'] = True
                                            is_multi_pc = True  # 标记为多pc

                            if folder_skcs:
                                # 统一计算：SKC个数 × 数量文件夹的数字
                                skc_count = len(folder_skcs)  # 基础SKC个数（多pc按1个计算）
                                folder_quantity = skc_count * quantity_value

                                logging.info(f"    数量文件夹 {item}: {skc_count}个基础SKC × {quantity_value} = {folder_quantity}")

                                # 详细显示每个SKC的信息
                                for base_skc, details in skc_details.items():
                                    if details['has_suffix']:
                                        logging.info(f"      SKC {base_skc}: 多pc (文件: {details['files']})")
                                    else:
                                        logging.info(f"      SKC {base_skc}: 单pc (文件: {details['files']})")

                                total_quantity += folder_quantity
                                logging.info(f"    累加数量: +{folder_quantity} = {total_quantity}")
                            else:
                                logging.info(f"    数量文件夹 {item} 无图片内容，跳过")

        except Exception as e:
            logging.warning(f"  提取数量信息失败: {e}")

        return total_quantity, found_skcs, is_multi_pc

    # 第三步：扫描已创建的文件夹，提取实际数量
    folder_stats = {}

    if not os.path.isdir(target_base_folder):
        logging.error(f"目标文件夹不存在: {target_base_folder}")
        return folder_stats

    logging.info("=== 扫描已创建的文件夹，提取实际数量 ===")

    # 多次扫描确保获取所有文件夹
    import time
    max_scan_attempts = 3
    scan_delay = 1  # 秒

    all_valid_folders = set()

    for scan_attempt in range(max_scan_attempts):
        if scan_attempt > 0:
            logging.info(f"第{scan_attempt+1}次扫描文件夹...")
            time.sleep(scan_delay)

        if os.path.exists(target_base_folder):
            current_folders = os.listdir(target_base_folder)
            logging.info(f"第{scan_attempt+1}次扫描找到 {len(current_folders)} 个项目: {current_folders}")

            # 过滤并收集有效文件夹
            for folder_name in current_folders:
                folder_path = os.path.join(target_base_folder, folder_name)
                if os.path.isdir(folder_path):
                    # 只过滤掉明显的系统文件夹或临时文件夹
                    if folder_name.startswith('.') or folder_name.lower() in ['temp', 'tmp', 'cache', '小标pdf', '大标']:
                        continue

                    # 保留所有属性集文件夹，包括：
                    # 1. 复杂命名：特级JIT-6-14-上午自送-1pc帆布画30x40cm-彭于晏-俞志敏组-(总X)-店铺号
                    # 2. 简单命名：1pc30x40cm, 2pc40x60cm, two-side18x18inch 等
                    all_valid_folders.add(folder_name)

        logging.info(f"第{scan_attempt+1}次扫描累计发现 {len(all_valid_folders)} 个有效文件夹")

    valid_folders = list(all_valid_folders)
    logging.info(f"最终有效属性集文件夹 ({len(valid_folders)}个): {valid_folders}")

    # 如果文件夹数量仍然很少，给出警告
    if len(valid_folders) < len(df) * 0.5:
        logging.warning(f"⚠️  检测到的文件夹数量({len(valid_folders)})明显少于拣货单行数({len(df)})，可能存在文件夹创建延迟问题")
        logging.warning("建议：1) 检查文件夹创建是否完成 2) 手动刷新后重新统计")

    # 第四步：为每个文件夹提取数量信息并匹配属性集
    for folder_name in valid_folders:
        logging.info(f"分析文件夹: {folder_name}")
        folder_path = os.path.join(target_base_folder, folder_name)

        # 从文件夹结构中提取数量信息
        total_quantity, found_skcs, is_multi_pc = extract_quantity_from_folder_structure(folder_path, folder_name)

        # 调试信息
        logging.info(f"  提取结果: total_quantity={total_quantity}, found_skcs={found_skcs}, is_multi_pc={is_multi_pc}")

        if total_quantity > 0 and found_skcs:
            # 通过SKC找到对应的属性集
            matched_attr_set = None
            for skc in found_skcs:
                attr_set = skc_to_attribute.get(skc)
                if attr_set:
                    matched_attr_set = attr_set
                    logging.info(f"  通过SKC {skc} 匹配到属性集: {attr_set}")
                    break

            # 如果没有通过SKC匹配到，尝试直接从文件夹名匹配属性集
            if not matched_attr_set:
                # 尝试从文件夹名直接匹配属性集
                for attr_set in skc_to_attribute.values():
                    if attr_set and attr_set.lower() in folder_name.lower():
                        matched_attr_set = attr_set
                        logging.info(f"  通过文件夹名匹配到属性集: {attr_set}")
                        break

            if matched_attr_set:
                # 检测pc数判断默认单位
                default_unit = "套" if is_multi_pc else "张"
                logging.info(f"  检测到多pc: {is_multi_pc}，默认单位: {default_unit}")

                folder_stats[folder_name] = {
                    'total_quantity': total_quantity,
                    'default_unit': default_unit,
                    'attribute_set': matched_attr_set
                }
                logging.info(f"  ✅ 文件夹统计完成: {folder_name} -> 属性集: {matched_attr_set}, 实际数量: {total_quantity}, 默认单位: {default_unit}")
            else:
                # 如果没有匹配到属性集，使用文件夹名作为属性集
                matched_attr_set = folder_name
                default_unit = "套" if is_multi_pc else "张"

                folder_stats[folder_name] = {
                    'total_quantity': total_quantity,
                    'default_unit': default_unit,
                    'attribute_set': matched_attr_set
                }
                logging.info(f"  ✅ 文件夹统计完成: {folder_name} -> 使用文件夹名作为属性集, 实际数量: {total_quantity}, 默认单位: {default_unit}")
        else:
            logging.warning(f"  ❌ 文件夹 '{folder_name}' 没有有效的数量信息")

    # 第五步：按匹配到的属性集文件夹累加数量
    logging.info("=== 按匹配到的属性集文件夹累加数量 ===")
    attribute_totals = {}  # 属性集 -> 总数量

    for folder_name, stats in folder_stats.items():
        attr_set = stats['attribute_set']
        quantity = stats['total_quantity']

        if attr_set not in attribute_totals:
            attribute_totals[attr_set] = 0
        attribute_totals[attr_set] += quantity

        logging.info(f"  文件夹 '{folder_name}' -> 属性集 '{attr_set}' 累加数量: +{quantity} = {attribute_totals[attr_set]}")

    # 更新folder_stats中的数量为属性集总数
    for folder_name, stats in folder_stats.items():
        attr_set = stats['attribute_set']
        total_for_attr_set = attribute_totals[attr_set]
        stats['total_quantity'] = total_for_attr_set
        logging.info(f"  更新文件夹 '{folder_name}' 数量为属性集总数: {total_for_attr_set}")

    # 确保所有文件夹都被包含在统计中
    logging.info(f"=== 最终文件夹统计结果 ===")
    logging.info(f"共统计 {len(folder_stats)} 个文件夹:")
    for i, (folder_name, stats) in enumerate(folder_stats.items(), 1):
        logging.info(f"  {i}. {folder_name} -> 数量: {stats['total_quantity']}, 单位: {stats['default_unit']}, 属性集: {stats['attribute_set']}")
    logging.info(f"=== 统计结束 ===")

    # 第五步：输出最终统计结果
    logging.info("=== 最终属性集数量统计 ===")
    for attr_set, total_count in attribute_quantities.items():
        logging.info(f"  属性集: {attr_set} -> 总数量: {total_count}")
    logging.info("=== 统计结束 ===")

    logging.info(f"文件夹数量统计完成，共统计 {len(folder_stats)} 个文件夹")
    return folder_stats

def calculate_final_stats_from_folders_and_pdfs(target_base_folder, df, source_folder, small_pdf_folder):
    """
    检查刚创建的文件夹对应的SKC，去匹配小标PDF数量
    相同SKC叠加：如果SKC匹配到两个小标就是1+1=2
    """
    logging.info("开始统计刚创建文件夹的SKC对应小标PDF数量...")

    # 第一步：统计小标PDF中各SKC的数量（相同SKC叠加）
    skc_pdf_counts = {}
    if small_pdf_folder and os.path.isdir(small_pdf_folder):
        logging.info("扫描小标PDF文件夹...")
        for filename in os.listdir(small_pdf_folder):
            if filename.lower().endswith('.pdf'):
                # 提取SKC编号
                skc_match = re.match(r'(\d{9,11})', filename)
                if skc_match:
                    skc = skc_match.group(1)
                    if skc not in skc_pdf_counts:
                        skc_pdf_counts[skc] = 0
                    skc_pdf_counts[skc] += 1  # 相同SKC叠加
                    logging.info(f"小标PDF: {filename} -> SKC {skc} (累计: {skc_pdf_counts[skc]})")

        logging.info(f"小标PDF统计完成，共找到 {len(skc_pdf_counts)} 个不同的SKC")
        for skc, count in skc_pdf_counts.items():
            logging.info(f"  SKC {skc}: {count} 个小标PDF")
    else:
        logging.warning("没有找到小标PDF文件夹")

    # 第二步：检查刚创建的文件夹，提取对应的SKC
    folder_stats = {}

    if not os.path.isdir(target_base_folder):
        logging.error(f"目标文件夹不存在: {target_base_folder}")
        return folder_stats

    logging.info(f"扫描已创建的文件夹: {target_base_folder}")
    for folder_name in os.listdir(target_base_folder):
        folder_path = os.path.join(target_base_folder, folder_name)
        if os.path.isdir(folder_path):
            logging.info(f"分析文件夹: {folder_name}")

            # 从文件夹内的图片文件名提取SKC（最准确的方法）
            found_skc = None
            for file_name in os.listdir(folder_path):
                if file_name.lower().endswith(('.jpg', '.jpeg', '.png')):
                    skc_match = re.search(r'(\d{9,11})', file_name)
                    if skc_match:
                        found_skc = skc_match.group(1)
                        logging.info(f"  从图片文件 '{file_name}' 提取SKC: {found_skc}")
                        break

            # 如果没找到，尝试从文件夹名直接提取SKC
            if not found_skc:
                skc_match = re.search(r'(\d{9,11})', folder_name)
                if skc_match:
                    found_skc = skc_match.group(1)
                    logging.info(f"  从文件夹名提取SKC: {found_skc}")

            if found_skc:
                # 第三步：用SKC匹配小标PDF数量
                pdf_quantity = skc_pdf_counts.get(found_skc, 0)

                if pdf_quantity > 0:
                    logging.info(f"  SKC {found_skc} 匹配到 {pdf_quantity} 个小标PDF")
                else:
                    logging.warning(f"  SKC {found_skc} 没有匹配到小标PDF，设置默认数量为1")
                    pdf_quantity = 1  # 没有小标PDF时默认为1

                # 检测pc数判断单位
                try:
                    jpg_count, pc_count = count_images_and_pc(source_folder, found_skc)
                    is_multi_pc = pc_count > 1
                    logging.info(f"  SKC {found_skc} 检测到 {pc_count}pc，{'多pc' if is_multi_pc else '单pc'}")
                except Exception as e:
                    logging.warning(f"  检测pc数失败: {e}，默认为单pc")
                    pc_count = 1
                    is_multi_pc = False

                folder_stats[folder_name] = {
                    'total_quantity': pdf_quantity,
                    'is_multi_pc': is_multi_pc,
                    'skc_count': 1,
                    'skc_base': found_skc
                }
                logging.info(f"  文件夹统计完成: {folder_name} -> SKC {found_skc}, 数量: {pdf_quantity}, 单位: {'套' if is_multi_pc else '张'}")
            else:
                logging.warning(f"  无法为文件夹 '{folder_name}' 找到对应的SKC")

    logging.info(f"文件夹统计完成，共统计 {len(folder_stats)} 个文件夹")
    return folder_stats

def show_quantity_confirmation_dialog(folder_stats):
    """显示数量确认对话框，基于拣货单数量自动填入"""
    dialog = tk.Toplevel()
    dialog.title("数量统计确认")
    dialog.geometry("900x600")
    dialog.transient()
    dialog.grab_set()

    # 创建主框架
    main_frame = tk.Frame(dialog, bg="#fff")
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

    # 标题
    title_label = tk.Label(main_frame, text="📊 数量统计确认", font=("微软雅黑", 14, "bold"), fg="#1976d2", bg="#fff")
    title_label.pack(pady=(0, 10))

    # 说明文字
    desc_label = tk.Label(main_frame, text="程序已根据拣货单数量统计各文件夹，请确认或修改总X张/套：", font=("微软雅黑", 10), fg="#333", bg="#fff")
    desc_label.pack(pady=(0, 10))

    # 滚动框架
    canvas = tk.Canvas(main_frame, bg="#fff")
    scrollbar = tk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg="#fff")

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    # 存储输入框的字典
    quantity_vars = {}
    unit_vars = {}

    # 为每个文件夹创建输入行
    for i, (folder_name, stats) in enumerate(folder_stats.items()):
        # 创建行框架
        row_frame = tk.Frame(scrollable_frame, bg="#f8f9fa", relief="solid", bd=1)
        row_frame.pack(fill=tk.X, padx=5, pady=2)

        # 文件夹名称（截断显示）
        folder_display = folder_name[:60] + "..." if len(folder_name) > 60 else folder_name
        folder_label = tk.Label(row_frame, text=f"{i+1}. {folder_display}", font=("微软雅黑", 9), fg="#333", bg="#f8f9fa", anchor="w")
        folder_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10, pady=5)

        # 数量和单位输入框
        input_frame = tk.Frame(row_frame, bg="#f8f9fa")
        input_frame.pack(side=tk.RIGHT, padx=10, pady=5)

        tk.Label(input_frame, text="总", font=("微软雅黑", 9), fg="#333", bg="#f8f9fa").pack(side=tk.LEFT)

        quantity_var = tk.StringVar(value=str(stats['total_quantity']))
        quantity_entry = tk.Entry(input_frame, textvariable=quantity_var, font=("微软雅黑", 9), width=6, justify="center")
        quantity_entry.pack(side=tk.LEFT, padx=(2, 2))

        # 单位选择
        unit_var = tk.StringVar(value=stats['default_unit'])
        unit_combo = ttk.Combobox(input_frame, textvariable=unit_var, values=["张", "套"], width=3, state="readonly")
        unit_combo.pack(side=tk.LEFT, padx=(2, 0))

        quantity_vars[folder_name] = quantity_var
        unit_vars[folder_name] = unit_var

    # 布局滚动区域
    canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # 按钮框架
    button_frame = tk.Frame(main_frame, bg="#fff")
    button_frame.pack(fill=tk.X, pady=(20, 0))

    result = [None]  # 存储结果

    def on_confirm():
        # 收集修改后的数量和单位
        updated_stats = {}
        for folder_name in folder_stats.keys():
            try:
                new_quantity = int(quantity_vars[folder_name].get())
                new_unit = unit_vars[folder_name].get()
                updated_stats[folder_name] = {
                    'total_quantity': new_quantity,
                    'unit': new_unit,
                    'is_multi_pc': new_unit == "套"
                }
            except ValueError:
                messagebox.showerror("错误", f"文件夹 '{folder_name[:30]}...' 的数量必须是整数！")
                return

        result[0] = updated_stats
        dialog.destroy()

    def on_cancel():
        result[0] = None
        dialog.destroy()

    # 按钮
    cancel_btn = tk.Button(button_frame, text="取消", font=("微软雅黑", 10), bg="#6c757d", fg="white", relief="flat", padx=20, command=on_cancel)
    cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))

    confirm_btn = tk.Button(button_frame, text="确认", font=("微软雅黑", 10), bg="#28a745", fg="white", relief="flat", padx=20, command=on_confirm)
    confirm_btn.pack(side=tk.RIGHT)

    # 居中显示
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
    y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
    dialog.geometry(f"+{x}+{y}")

    dialog.wait_window()
    return result[0]

def update_folder_names_with_confirmed_quantities(target_base_folder, confirmed_stats):
    """根据确认的数量更新文件夹名称"""
    for attr_set, stats in confirmed_stats.items():
        # 查找包含该属性集的文件夹
        for folder_name in os.listdir(target_base_folder):
            folder_path = os.path.join(target_base_folder, folder_name)
            if os.path.isdir(folder_path) and attr_set in folder_name:
                # 更新文件夹名称中的总数部分
                new_quantity = stats['total_quantity']
                unit = stats['unit']

                if "总X" in folder_name:
                    # 替换 "总X" 为具体数量
                    new_folder_name = folder_name.replace("总X", f"总{new_quantity}{unit}")
                elif "总" in folder_name and ("张" in folder_name or "套" in folder_name):
                    # 更新现有的总数
                    new_folder_name = re.sub(r'总\d+[张套]', f'总{new_quantity}{unit}', folder_name)
                else:
                    # 添加总数信息
                    new_folder_name = f"{folder_name}(总{new_quantity}{unit})"

                if new_folder_name != folder_name:
                    new_folder_path = os.path.join(target_base_folder, new_folder_name)
                    try:
                        os.rename(folder_path, new_folder_path)
                        logging.info(f"更新文件夹名称: {folder_name} -> {new_folder_name}")
                    except Exception as e:
                        logging.error(f"重命名文件夹失败: {folder_name} -> {new_folder_name}，原因: {e}")

if __name__ == '__main__':
    main()

